using SmartBI.Core.Services;
using SmartBI.Core.Middleware;
using Newtonsoft.Json.Serialization;
using OfficeOpenXml;
using System.Text;
using SmartBI.Core.Hubs;
using DocumentFormat.OpenXml.Office2016.Drawing.ChartDrawing;

//using Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation;

// Set EPPlus license context
ExcelPackage.LicenseContext = LicenseContext.NonCommercial;

// Register encoding providers for RDLC reports
Encoding.RegisterProvider(CodePagesEncodingProvider.Instance);

var builder = WebApplication.CreateBuilder(args);

// Add services to the container.
builder.Services.AddControllersWithViews()
    .AddJsonOptions(options =>
    {
        options.JsonSerializerOptions.DefaultIgnoreCondition = JsonIgnoreCondition.WhenWritingNull;
        options.JsonSerializerOptions.ReferenceHandler = ReferenceHandler.Preserve; // Handle circular references
    })
    .AddNewtonsoftJson(options =>
    {
        options.SerializerSettings.ContractResolver = new CamelCasePropertyNamesContractResolver();
        options.SerializerSettings.ReferenceLoopHandling = Newtonsoft.Json.ReferenceLoopHandling.Ignore;
    });
//.AddRazorRuntimeCompilation(); // Add Razor runtime compilation here
//builder.Services.AddMvc().AddRazorRuntimeCompilation();
// SmartBI.Core is a pure presentation layer - no direct database access
// All data operations go through microservice APIs

// Add caching services
builder.Services.AddMemoryCache(); // For RM Dashboard caching
builder.Services.AddDistributedMemoryCache();
builder.Services.AddSession(options => {
    options.IdleTimeout = TimeSpan.FromMinutes(30);
    options.Cookie.HttpOnly = true;
    options.Cookie.IsEssential = true;
});

// Configure authentication
builder.Services.AddAuthentication(CookieAuthenticationDefaults.AuthenticationScheme)
    .AddCookie(options => {
        options.LoginPath = "/Account/Login";
        options.LogoutPath = "/Account/Logout";
        options.AccessDeniedPath = "/Account/AccessDenied";
        options.ExpireTimeSpan = TimeSpan.FromMinutes(30);
    });

// API-Only Architecture - No direct database access
// All data operations go through SmartBIReportService.API microservice

// Register email service
builder.Services.AddScoped<IEmailService, EmailService>();

// Register Report services
builder.Services.AddScoped<SmartBI.Core.Services.IReportViewerCoreService, SmartBI.Core.Services.ReportViewerCoreService>();

// Register stub repository implementations for ReportController compatibility
// These are temporary stubs that delegate to microservice APIs
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IReportRepository, SmartBI.Core.Services.StubReportRepository>();
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IReportParameterRepository, SmartBI.Core.Services.StubReportParameterRepository>();

// SmartBI.Core is a pure presentation layer - no direct database access
// All data operations go through microservice APIs (including reports)

// Register API Client Services for microservice communication
builder.Services.AddHttpClient<IApiClientService, ApiClientService>(client =>
{
    var apiUrl = builder.Configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000";
    client.BaseAddress = new Uri(apiUrl);
    client.Timeout = TimeSpan.FromMinutes(10); // Increased timeout for report generation
    client.DefaultRequestHeaders.Add("User-Agent", "SmartBI.Core/2.0");
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
    var handler = new HttpClientHandler();
    if (builder.Environment.IsDevelopment())
    {
        // Bypass SSL certificate validation in development
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
    }
    return handler;
});

// Register Report API Client - uses same microservice as other APIs
builder.Services.AddHttpClient<IReportApiClient, ReportApiClient>(client =>
{
    var apiUrl = builder.Configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000";
    client.BaseAddress = new Uri(apiUrl);
    client.Timeout = TimeSpan.FromMinutes(10); // Increased timeout for report generation
    client.DefaultRequestHeaders.Add("User-Agent", "SmartBI.Core/2.0");
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
    var handler = new HttpClientHandler();
    if (builder.Environment.IsDevelopment())
    {
        // Bypass SSL certificate validation in development
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
    }
    return handler;
});

// Register File Upload API Client
builder.Services.AddHttpClient<FileUploadApiClient>(client =>
{
    var apiUrl = builder.Configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000"; // Updated to HTTPS port 7000
    client.BaseAddress = new Uri(apiUrl);
    client.Timeout = TimeSpan.FromMinutes(10); // Allow for large file uploads
    client.DefaultRequestHeaders.Add("User-Agent", "SmartBI.Core/2.0");
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
    var handler = new HttpClientHandler();
    if (builder.Environment.IsDevelopment())
    {
        // Bypass SSL certificate validation in development
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
    }
    return handler;
});

// Register TradeOps API Client
builder.Services.AddHttpClient<SmartBI.Core.Services.Interfaces.ITradeOpsApiClient, SmartBI.Core.Services.TradeOpsApiClient>(client =>
{
    var apiUrl = builder.Configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000"; // Updated to HTTPS port 7000
    client.BaseAddress = new Uri(apiUrl);
    client.Timeout = TimeSpan.FromMinutes(5);
    client.DefaultRequestHeaders.Add("User-Agent", "SmartBI.Core/2.0");
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
    var handler = new HttpClientHandler();
    if (builder.Environment.IsDevelopment())
    {
        // Bypass SSL certificate validation in development
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
    }
    return handler;
});

// Register Retail API Client with HttpClient for microservice communication
builder.Services.AddHttpClient<SmartBI.Core.Services.Interfaces.IRetailApiClient, SmartBI.Core.Services.RetailApiClient>(client =>
{
    var apiUrl = builder.Configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000";
    client.BaseAddress = new Uri(apiUrl);
    client.Timeout = TimeSpan.FromMinutes(5);
    client.DefaultRequestHeaders.Add("User-Agent", "SmartBI.Core/2.0");
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
    var handler = new HttpClientHandler();
    if (builder.Environment.IsDevelopment())
    {
        // Bypass SSL certificate validation in development
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
    }
    return handler;
});

// Register Dashboard API Client with HttpClient for microservice communication
builder.Services.AddHttpClient<SmartBI.Core.Services.Interfaces.IDashboardApiClient, SmartBI.Core.Services.DashboardApiClient>(client =>
{
    var apiUrl = builder.Configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000";
    client.BaseAddress = new Uri(apiUrl);
    client.Timeout = TimeSpan.FromMinutes(5);
    client.DefaultRequestHeaders.Add("User-Agent", "SmartBI.Core/2.0");
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
    var handler = new HttpClientHandler();
    if (builder.Environment.IsDevelopment())
    {
        // Bypass SSL certificate validation in development
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
    }
    return handler;
});

// Register Relationship Manager API Client with HttpClient for microservice communication
builder.Services.AddHttpClient<SmartBI.Core.Services.Interfaces.IRelationshipManagerApiClient, SmartBI.Core.Services.RelationshipManagerApiClient>(client =>
{
    var apiUrl = builder.Configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000";
    client.BaseAddress = new Uri(apiUrl);
    client.Timeout = TimeSpan.FromMinutes(5);
    client.DefaultRequestHeaders.Add("User-Agent", "SmartBI.Core/2.0");
})
.ConfigurePrimaryHttpMessageHandler(() =>
{
    var handler = new HttpClientHandler();
    if (builder.Environment.IsDevelopment())
    {
        // Bypass SSL certificate validation in development
        handler.ServerCertificateCustomValidationCallback = (message, cert, chain, errors) => true;
    }
    return handler;
});

// Pure Presentation Layer - No direct database access
// All data operations go through SmartBIReportService.API microservice

// Add HttpContextAccessor for accessing HttpContext in repositories
builder.Services.AddHttpContextAccessor();

// Add Razor Pages
builder.Services.AddRazorPages();

// RDLC report service is registered above

// Add CORS policy
builder.Services.AddCors(options =>
{
    options.AddPolicy("CorsPolicy", builder =>
        builder.AllowAnyOrigin()
               .AllowAnyMethod()
               .AllowAnyHeader());
});

// Add SignalR services
builder.Services.AddSignalR();

var app = builder.Build();

// Configure the HTTP request pipeline.
if (!app.Environment.IsDevelopment())
{
    app.UseExceptionHandler("/Home/Error");
    // The default HSTS value is 30 days. You may want to change this for production scenarios, see https://aka.ms/aspnetcore-hsts.
    app.UseHsts();
}

app.UseHttpsRedirection();
app.UseStaticFiles(); // Revert to default

// Add static files support for additional folders (maintaining compatibility with original structure)
//app.UseStaticFiles(new StaticFileOptions
//{
//    FileProvider = new PhysicalFileProvider(Path.Combine(app.Environment.ContentRootPath, "Rotativa")),
//    RequestPath = "/Rotativa"
//});



// Add WebSocket support
/*app.UseWebSockets(new WebSocketOptions
{
    KeepAliveInterval = TimeSpan.FromSeconds(120),
});

// Add WebSocket handler middleware to properly handle WebSocket connections
app.UseWebSocketHandler();
*/
app.UseRouting();

// Use CORS before authentication
app.UseCors("CorsPolicy");

app.UseAuthentication();
app.UseAuthorization();
app.UseSession();

// Add SignalR hub endpoint
//app.MapHub<NotificationHub>("/notificationHub");

// Configure routing for clean URLs
//app.MapControllerRoute(
//    name: "controller-only",
//    pattern: "{controller}",
//    defaults: new { action = "Index" });

//app.MapControllerRoute(
//    name: "default",
//    pattern: "{controller=Home}/{action=Index}/{id?}");

app.MapControllerRoute(
    name: "root",
    pattern: "",
    defaults: new { controller = "Home", action = "Index" });

app.MapControllerRoute(
    name: "controller-only",
    pattern: "{controller}",
    defaults: new { action = "Index" });

app.MapControllerRoute(
    name: "default",
    pattern: "{controller=Home}/{action=Index}/{id?}");



// API-Only Architecture - Test microservice connectivity (non-blocking with timeout)
using (var scope = app.Services.CreateScope())
{
    var services = scope.ServiceProvider;
    try
    {
        var apiClient = services.GetRequiredService<IApiClientService>();
        var logger = services.GetRequiredService<ILogger<Program>>();

        var cts = new CancellationTokenSource(TimeSpan.FromSeconds(5));
        try
        {
            var isHealthyTask = apiClient.IsApiHealthyAsync(cts.Token);
            var isHealthy = isHealthyTask.GetAwaiter().GetResult();
            if (isHealthy)
            {
                Console.WriteLine("✓ SmartBIMicroService.API microservice connection successful - True Microservice Architecture Active.");
                logger.LogInformation("✅ Microservice architecture initialized successfully");
            }
            else
            {
                Console.WriteLine("⚠ Warning: Could not connect to SmartBIMicroService.API microservice.");
                logger.LogWarning("❌ Microservice connection failed - check API service availability");
            }
        }
        catch (OperationCanceledException)
        {
            logger.LogWarning("API health check timed out - continuing startup");
            Console.WriteLine("⚠ API health check timed out - continuing startup");
        }
    }
    catch (Exception ex)
    {
        var logger = services.GetRequiredService<ILogger<Program>>();
        logger.LogError(ex, "An error occurred while testing the microservice connection.");
        Console.WriteLine("⚠ Microservice connection test failed - continuing startup");
    }
}

app.Run();
