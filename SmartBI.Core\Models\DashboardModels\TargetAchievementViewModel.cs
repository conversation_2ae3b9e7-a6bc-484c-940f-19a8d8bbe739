namespace SmartBI.Core.Models.DashboardModels
{
    public class TargetAchievementViewModel
    {
        public decimal TOTAL_TARGET { get; set; }
        public decimal TOTAL_ACHIEVEMENT { get; set; }
        public decimal ACHIEVEMENT_PERCENTAGE { get; set; }
        public List<ProductTarget> ProductTargets { get; set; } = new();
        public List<MonthlyTarget> MonthlyTargets { get; set; } = new();
    }

    public class ProductTarget
    {
        public string ProductName { get; set; }
        public decimal Target { get; set; }
        public decimal Achievement { get; set; }
        public decimal AchievementPercentage { get; set; }
    }

    public class MonthlyTarget
    {
        public string Month { get; set; }
        public decimal Target { get; set; }
        public decimal Achievement { get; set; }
        public decimal AchievementPercentage { get; set; }
    }
} 