@model SmartBI.Core.Models.DashboardModels.PerformanceAttritionViewModel

<div class="row" id="attritionMetrics">
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">people</i>
                </div>
                <p class="card-category">Previous Total</p>
                <h3 class="card-title">@Model.TOTAL_ACCOUNTS_PREVIOUS.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-info">history</i>
                    Previous period total accounts
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-success card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">person_add</i>
                </div>
                <p class="card-category">New Accounts</p>
                <h3 class="card-title">@Model.NEW_ACCOUNTS.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-success">add_circle</i>
                    New accounts opened
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-danger card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">person_remove</i>
                </div>
                <p class="card-category">Closed Accounts</p>
                <h3 class="card-title">@Model.CLOSED_ACCOUNTS.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-danger">remove_circle</i>
                    Accounts closed
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-warning card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">trending_up</i>
                </div>
                <p class="card-category">Net Growth</p>
                <h3 class="card-title">@Model.NET_GROWTH.ToString("N2")%</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-warning">info</i>
                    Overall growth rate
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Attrition Chart -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header card-header-primary">
                <h4 class="card-title">Monthly Attrition Analysis</h4>
                <p class="card-category">Monthly breakdown of account changes</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead class="text-primary">
                            <tr>
                                <th>Month</th>
                                <th>New Accounts</th>
                                <th>Closed Accounts</th>
                                <th>Net Growth</th>
                                <th>Attrition Rate</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var month in Model.MonthlyData)
                            {
                                <tr>
                                    <td>@month.Month</td>
                                    <td>@month.NewAccounts.ToString("N0")</td>
                                    <td>@month.ClosedAccounts.ToString("N0")</td>
                                    <td>@month.NetGrowth.ToString("N2")%</td>
                                    <td>@month.AttritionRate.ToString("N2")%</td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Retention Stats -->
<div class="row mt-4">
    <div class="col-md-6">
        <div class="card">
            <div class="card-header card-header-success">
                <h4 class="card-title">Retention Rate</h4>
                <p class="card-category">@Model.RETENTION_RATE.ToString("N2")%</p>
            </div>
            <div class="card-body">
                <div class="progress">
                    <div class="progress-bar bg-success" role="progressbar" 
                         style="width: @Model.RETENTION_RATE%"
                         aria-valuenow="@Model.RETENTION_RATE" aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
            </div>
        </div>
    </div>
    <div class="col-md-6">
        <div class="card">
            <div class="card-header card-header-danger">
                <h4 class="card-title">Attrition Rate</h4>
                <p class="card-category">@Model.ATTRITION_RATE.ToString("N2")%</p>
            </div>
            <div class="card-body">
                <div class="progress">
                    <div class="progress-bar bg-danger" role="progressbar" 
                         style="width: @Model.ATTRITION_RATE%"
                         aria-valuenow="@Model.ATTRITION_RATE" aria-valuemin="0" aria-valuemax="100">
                    </div>
                </div>
            </div>
        </div>
    </div>
</div> 