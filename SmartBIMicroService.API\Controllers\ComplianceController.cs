using Microsoft.AspNetCore.Mvc;
using SmartBI.Data.Repositories.Interfaces;
using SmartBI.Data.ViewModels.Dashboard;
using System.Data;

namespace SmartBIMicroService.API.Controllers
{
    /// <summary>
    /// Compliance API Controller for dashboard operations
    /// Legacy SmartBI2 DashboardRepository compatible endpoints
    /// </summary>
    [ApiController]
    [Route("api/[controller]")]
    public class ComplianceController : ControllerBase
    {
        private readonly IComplianceRepository _complianceRepository;
        private readonly ILogger<ComplianceController> _logger;

        public ComplianceController(
            IComplianceRepository complianceRepository,
            ILogger<ComplianceController> logger)
        {
            _complianceRepository = complianceRepository ?? throw new ArgumentNullException(nameof(complianceRepository));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
        }

        #region KYC Overdue APIs

        /// <summary>
        /// Legacy API: GET_DASHBOARD_KYC_OVERDUE("HIGH", userId) - Gets KYC overdue high risk data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_KYC_OVERDUE method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>KYC overdue high risk data</returns>
        [HttpGet("kyc-overdue-high")]
        public async Task<ActionResult<DataTable>> GetKYCOverdueHigh([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_KYC_OVERDUE(HIGH) API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_KYC_OVERDUE("HIGH", userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetKYCOverdueHigh for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_KYC_OVERDUE("LOW", userId) - Gets KYC overdue low risk data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_KYC_OVERDUE method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>KYC overdue low risk data</returns>
        [HttpGet("kyc-overdue-low")]
        public async Task<ActionResult<DataTable>> GetKYCOverdueLow([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_KYC_OVERDUE(LOW) API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_KYC_OVERDUE("LOW", userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetKYCOverdueLow for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_KYC_OVERDUE_SAF("HIGH", userId) - Gets KYC overdue SAF high risk data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_KYC_OVERDUE_SAF method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>KYC overdue SAF high risk data</returns>
        [HttpGet("kyc-overdue-saf-high")]
        public async Task<ActionResult<DataTable>> GetKYCOverdueSAFHigh([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_KYC_OVERDUE_SAF(HIGH) API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_KYC_OVERDUE_SAF("HIGH", userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetKYCOverdueSAFHigh for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_KYC_OVERDUE_SAF("LOW", userId) - Gets KYC overdue SAF low risk data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_KYC_OVERDUE_SAF method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>KYC overdue SAF low risk data</returns>
        [HttpGet("kyc-overdue-saf-low")]
        public async Task<ActionResult<DataTable>> GetKYCOverdueSAFLow([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_KYC_OVERDUE_SAF(LOW) API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_KYC_OVERDUE_SAF("LOW", userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetKYCOverdueSAFLow for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        #endregion

        #region Compliance Monitoring APIs

        /// <summary>
        /// Legacy API: GET_DASHBOARD_NGO_NPO(userId) - Gets NGO/NPO compliance data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_NGO_NPO method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>NGO/NPO data</returns>
        [HttpGet("ngo-npo")]
        public async Task<ActionResult<DataTable>> GetNGONPO([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_NGO_NPO API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_NGO_NPO(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetNGONPO for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_PEP_IP(userId) - Gets PEP/IP compliance data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_PEP_IP method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>PEP/IP data</returns>
        [HttpGet("pep-ip")]
        public async Task<ActionResult<DataTable>> GetPEPIP([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_PEP_IP API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_PEP_IP(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetPEPIP for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_STAND_ALONE_FDR(userId) - Gets standalone FDR data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_STAND_ALONE_FDR method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Standalone FDR data</returns>
        [HttpGet("standalone-fdr")]
        public async Task<ActionResult<DataTable>> GetStandaloneFDR([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_STAND_ALONE_FDR API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_STAND_ALONE_FDR(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetStandaloneFDR for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_FATCA(userId) - Gets FATCA compliance data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_FATCA method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>FATCA data</returns>
        [HttpGet("fatca")]
        public async Task<ActionResult<DataTable>> GetFATCA([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_FATCA API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_FATCA(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetFATCA for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_AML(userId) - Gets AML compliance data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_AML method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>AML data</returns>
        [HttpGet("aml")]
        public async Task<ActionResult<DataTable>> GetAML([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_AML API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_AML(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAML for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_FCCM_ALERT(userId) - Gets FCCM alert data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_FCCM_ALERT method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>FCCM alert data</returns>
        [HttpGet("fccm-alert")]
        public async Task<ActionResult<FCCM_ALERT_DASHBOARD>> GetFCCMAlert([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_FCCM_ALERT API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_FCCM_ALERT(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetFCCMAlert for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS(userId) - Gets customer KYC update status
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Customer KYC update status data</returns>
        [HttpGet("customer-kyc-update-status")]
        public async Task<ActionResult<DataTable>> GetCustomerKYCUpdateStatus([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetCustomerKYCUpdateStatus for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS(userId) - Gets compliance account statistics
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS method
        /// Note: Legacy spelling "COMPLAINCE" maintained for compatibility
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Account statistics data</returns>
        [HttpGet("account-statistics")]
        public async Task<ActionResult<DataTable>> GetAccountStatistics([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetAccountStatistics for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        /// <summary>
        /// Legacy API: SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY(userId) - Gets standalone FDR summary
        /// Compatible with legacy DashboardRepository.SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Standalone FDR summary data</returns>
        [HttpGet("standalone-fdr-summary")]
        public async Task<ActionResult<DataTable>> GetStandaloneFDRSummary([FromQuery] string userId)
        {
            try
            {
                _logger.LogInformation("Legacy SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY API called for user: {UserId}", userId);

                if (string.IsNullOrEmpty(userId))
                {
                    return BadRequest("User ID is required");
                }

                var result = await _complianceRepository.SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY(userId);
                return Ok(result);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GetStandaloneFDRSummary for user: {UserId}", userId);
                return StatusCode(500, "Internal server error");
            }
        }

        #endregion
    }
}
