{"format": 1, "restore": {"E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\SmartBI.Core.csproj": {}}, "projects": {"E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\SmartBI.Core.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\SmartBI.Core.csproj", "projectName": "SmartBI.Core", "projectPath": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\SmartBI.Core.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {"E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Data\\SmartBI.Data.csproj": {"projectPath": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Data\\SmartBI.Data.csproj"}}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"ClosedXML": {"target": "Package", "version": "[0.95.4, )"}, "Dapper": {"target": "Package", "version": "[2.1.28, )"}, "DocumentFormat.OpenXml": {"target": "Package", "version": "[2.20.0, )"}, "EPPlus": {"target": "Package", "version": "[6.2.10, )"}, "Microsoft.AspNetCore.Mvc.NewtonsoftJson": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation": {"target": "Package", "version": "[8.0.16, )"}, "Microsoft.Extensions.Logging": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Console": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.VisualStudio.Web.CodeGeneration.Design": {"target": "Package", "version": "[8.0.0, )"}, "ReportViewerCore.NETCore": {"target": "Package", "version": "[15.1.26, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.9.0, )"}, "System.Text.Encoding.CodePages": {"target": "Package", "version": "[9.0.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.AspNetCore.App": {"privateAssets": "none"}, "Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}, "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Data\\SmartBI.Data.csproj": {"version": "1.0.0", "restore": {"projectUniqueName": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Data\\SmartBI.Data.csproj", "projectName": "SmartBI.Data", "projectPath": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Data\\SmartBI.Data.csproj", "packagesPath": "C:\\Users\\<USER>\\.nuget\\packages\\", "outputPath": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Data\\obj\\", "projectStyle": "PackageReference", "fallbackFolders": ["C:\\Program Files (x86)\\Microsoft Visual Studio\\Shared\\NuGetPackages", "C:\\Program Files\\dotnet\\sdk\\NuGetFallbackFolder"], "configFilePaths": ["C:\\Users\\<USER>\\AppData\\Roaming\\NuGet\\NuGet.Config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.FallbackLocation.config", "C:\\Program Files (x86)\\NuGet\\Config\\Microsoft.VisualStudio.Offline.config"], "originalTargetFrameworks": ["net8.0"], "sources": {"C:\\Program Files (x86)\\Microsoft SDKs\\NuGetPackages\\": {}, "C:\\Program Files\\dotnet\\library-packs": {}, "https://api.nuget.org/v3/index.json": {}}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "projectReferences": {}}}, "warningProperties": {"warnAsError": ["NU1605"]}, "restoreAuditProperties": {"enableAudit": "true", "auditLevel": "low", "auditMode": "direct"}, "SdkAnalysisLevel": "9.0.300"}, "frameworks": {"net8.0": {"targetAlias": "net8.0", "dependencies": {"Microsoft.AspNetCore.Http.Features": {"target": "Package", "version": "[5.0.17, )"}, "Microsoft.EntityFrameworkCore": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.SqlServer": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.EntityFrameworkCore.Tools": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Configuration.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.DependencyInjection.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "Microsoft.Extensions.Logging.Abstractions": {"target": "Package", "version": "[8.0.0, )"}, "System.ComponentModel.Annotations": {"target": "Package", "version": "[5.0.0, )"}, "System.Data.SqlClient": {"target": "Package", "version": "[4.8.5, )"}}, "imports": ["net461", "net462", "net47", "net471", "net472", "net48", "net481"], "assetTargetFallback": true, "warn": true, "frameworkReferences": {"Microsoft.NETCore.App": {"privateAssets": "all"}}, "runtimeIdentifierGraphPath": "C:\\Program Files\\dotnet\\sdk\\9.0.300/PortableRuntimeIdentifierGraph.json"}}}}}