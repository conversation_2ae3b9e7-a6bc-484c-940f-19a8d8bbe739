<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="utf-8" />
    <link rel="apple-touch-icon" sizes="76x76" href="~/assets/img/apple-icon.png">
    <link rel="icon" type="image/png" href="~/assets/img/favicon.png">
    <meta http-equiv="X-UA-Compatible" content="IE=edge,chrome=1" />
    <title>@ViewBag.Title - SmartBI</title>
    <meta content='width=device-width, initial-scale=1.0, maximum-scale=1.0, user-scalable=0, shrink-to-fit=no' name='viewport' />

    <!--     Fonts and icons     -->
    <link rel="stylesheet" type="text/css" href="https://fonts.googleapis.com/css?family=Roboto:300,400,500,700|Roboto+Slab:400,700|Material+Icons" />
    <link rel="stylesheet" href="https://maxcdn.bootstrapcdn.com/font-awesome/latest/css/font-awesome.min.css">

    <!-- CSS Files -->
    <link href="~/assets/css/material-dashboard.min.css?v=2.1.0" rel="stylesheet" />

    <!-- Select2 CSS for Material Dashboard Pro -->
    <link href="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/css/select2.min.css" rel="stylesheet" />

    <!-- Flatpickr CSS with Material Design Theme -->
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css" rel="stylesheet" />
    <link href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css" rel="stylesheet" />

    <!-- ONLY Essential Application CSS - No Framework Overrides -->
    <link href="~/css/user-profile.css" rel="stylesheet" />
    <link href="~/css/material-forms.css" rel="stylesheet" />

    @RenderSection("Styles", required: false)
</head>

<body class="@ViewBag.BodyClass">
    <div class="wrapper">
        <!-- Sidebar -->
        @await Html.PartialAsync("_Sidebar")
       
        <div class="main-panel">
            <!-- Navbar -->
            @await Html.PartialAsync("_Navbar")
            <!-- End Navbar -->

            <!-- Content area -->
            <div class="content">
                <div class="container-fluid">
                    <!-- TempData Messages Display -->
                    @if (TempData["SuccessMessage"] != null)
                    {
                        <div class="alert alert-success alert-dismissible fade show" role="alert">
                            <i class="fas fa-check-circle mr-2"></i> @TempData["SuccessMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }
                    @if (TempData["ErrorMessage"] != null)
                    {
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-circle mr-2"></i> @TempData["ErrorMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }
                    @if (TempData["WarningMessage"] != null)
                    {
                        <div class="alert alert-warning alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle mr-2"></i> @TempData["WarningMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }
                    @if (TempData["InfoMessage"] != null)
                    {
                        <div class="alert alert-info alert-dismissible fade show" role="alert">
                            <i class="fas fa-info-circle mr-2"></i> @TempData["InfoMessage"]
                            <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                                <span aria-hidden="true">&times;</span>
                            </button>
                        </div>
                    }

                    @RenderBody()
                </div>
            </div>
            
            <footer class="footer">
                <div class="container-fluid">
                    <nav class="float-left">
                        <ul>
                            <li>
                                <a href="@Url.Action("Index", "Home")">
                                    SmartBI
                                </a>
                            </li>
                            <li>
                                <a href="https://smartbi.io/about">
                                    About Us
                                </a>
                            </li>
                            <li>
                                <a href="https://smartbi.io/blog">
                                    Blog
                                </a>
                            </li>
                        </ul>
                    </nav>
                    <div class="copyright float-right">
                        &copy;
                        <script>
                            document.write(new Date().getFullYear())
                        </script> SmartBI, All Rights Reserved.
                    </div>
                </div>
            </footer>
        </div>
    </div>

    <!-- Fixed Plugin for Theme Customization -->
    <div class="fixed-plugin">
        <div class="dropdown show-dropdown">
            <a href="#" data-toggle="dropdown">
                <i class="fa fa-cog fa-2x"> </i>
            </a>
            <ul class="dropdown-menu">
                <li class="header-title"> Sidebar Filters</li>
                <li class="adjustments-line">
                    <a href="javascript:void(0)" class="switch-trigger active-color">
                        <div class="badge-colors ml-auto mr-auto">
                            <span class="badge filter badge-purple" data-color="purple"></span>
                            <span class="badge filter badge-azure" data-color="azure"></span>
                            <span class="badge filter badge-green" data-color="green"></span>
                            <span class="badge filter badge-warning" data-color="orange"></span>
                            <span class="badge filter badge-danger" data-color="danger"></span>
                            <span class="badge filter badge-rose active" data-color="rose"></span>
                        </div>
                        <div class="clearfix"></div>
                    </a>
                </li>
                <li class="header-title">Sidebar Background</li>
                <li class="adjustments-line">
                    <a href="javascript:void(0)" class="switch-trigger background-color">
                        <div class="ml-auto mr-auto">
                            <span class="badge filter badge-black active" data-background-color="black"></span>
                            <span class="badge filter badge-white" data-background-color="white"></span>
                            <span class="badge filter badge-red" data-background-color="red"></span>
                        </div>
                        <div class="clearfix"></div>
                    </a>
                </li>
                <li class="adjustments-line">
                    <a href="javascript:void(0)" class="switch-trigger">
                        <p>Sidebar Mini</p>
                        <label class="ml-auto">
                            <div class="togglebutton switch-sidebar-mini">
                                <label>
                                    <input type="checkbox">
                                    <span class="toggle"></span>
                                </label>
                            </div>
                        </label>
                        <div class="clearfix"></div>
                    </a>
                </li>
                <li class="adjustments-line">
                    <a href="javascript:void(0)" class="switch-trigger">
                        <p>Sidebar Images</p>
                        <label class="switch-mini ml-auto">
                            <div class="togglebutton switch-sidebar-image">
                                <label>
                                    <input type="checkbox" checked="">
                                    <span class="toggle"></span>
                                </label>
                            </div>
                        </label>
                        <div class="clearfix"></div>
                    </a>
                </li>
                <li class="header-title">Images</li>
                <li class="active">
                    <a class="img-holder switch-trigger" href="javascript:void(0)">
                        <img src="~/assets/img/sidebar-1.jpg" alt="">
                    </a>
                </li>
                <li>
                    <a class="img-holder switch-trigger" href="javascript:void(0)">
                        <img src="~/assets/img/sidebar-2.jpg" alt="">
                    </a>
                </li>
                <li>
                    <a class="img-holder switch-trigger" href="javascript:void(0)">
                        <img src="~/assets/img/sidebar-3.jpg" alt="">
                    </a>
                </li>
                <li>
                    <a class="img-holder switch-trigger" href="javascript:void(0)">
                        <img src="~/assets/img/sidebar-4.jpg" alt="">
                    </a>
                </li>
                <li class="button-container github-star">
                    <a class="github-button" href="https://github.com/creativetimofficial/ct-material-dashboard-pro" data-icon="octicon-star" data-size="large" data-show-count="true" aria-label="Star SmartBI on GitHub">Star</a>
                </li>
                <li class="header-title">Thank you for 95 shares!</li>
                <li class="button-container text-center">
                    <button id="twitter" class="btn btn-round btn-twitter"><i class="fa fa-twitter"></i> &middot; 45</button>
                    <button id="facebook" class="btn btn-round btn-facebook"><i class="fa fa-facebook-f"></i> &middot; 50</button>
                    <br>
                    <br>
                </li>
            </ul>
        </div>
    </div>

    <!--   Core JS Files   -->
    <script src="~/assets/js/core/jquery.min.js"></script>
    <script src="~/assets/js/core/popper.min.js"></script>
    <script src="~/assets/js/core/bootstrap-material-design.min.js"></script>
    <script src="~/assets/js/plugins/perfect-scrollbar.jquery.min.js"></script>
    <script src="~/assets/js/plugins/arrive.min.js"></script>

    <!-- Material Dashboard Pro Core JS Files -->
    <script src="~/assets/js/plugins/moment.min.js"></script>
    <script src="~/assets/js/plugins/sweetalert2.js"></script>
    <script src="~/assets/js/plugins/jquery.validate.min.js"></script>
    <script src="~/assets/js/plugins/jquery.bootstrap-wizard.js"></script>
    <script src="~/assets/js/plugins/bootstrap-selectpicker.js"></script>
    <script src="~/assets/js/plugins/jquery.dataTables.min.js"></script>
    <script src="~/assets/js/plugins/bootstrap-tagsinput.js"></script>
    <script src="~/assets/js/plugins/jasny-bootstrap.min.js"></script>
    <script src="~/assets/js/plugins/fullcalendar.min.js"></script>
    <script src="~/assets/js/plugins/jquery-jvectormap.js"></script>
    <script src="~/assets/js/plugins/nouislider.min.js"></script>
    <script src="~/assets/js/plugins/chartist.min.js"></script>
    <script src="~/assets/js/plugins/bootstrap-notify.js"></script>
    
    <!-- Control Center for Material Dashboard: parallax effects, scripts for the example pages etc -->
    <script src="~/assets/js/material-dashboard.min.js?v=2.1.0" type="text/javascript"></script>

    <!-- Material Dashboard DEMO methods for Fixed Plugin -->
    <script src="~/assets/demo/demo.js"></script>

    <!-- GitHub Buttons for Fixed Plugin -->
    <script async defer src="https://buttons.github.io/buttons.js"></script>

    <!-- Material Dashboard Pro Sidebar Navigation CSS -->
    <style>
        /* Caret rotation animation for sidebar navigation */
        .sidebar .nav .caret {
            transition: transform 0.3s ease;
        }

        .sidebar .nav .caret.rotate-180 {
            transform: rotate(180deg);
        }

        /* Hover effects for sidebar navigation */
        .sidebar .nav .nav-link.nav-link-hover {
            background-color: rgba(255, 255, 255, 0.1);
        }

        /* Ensure proper spacing for sidebar navigation */
        .sidebar .nav .nav-item {
            margin-bottom: 0;
        }

        .sidebar .nav .collapse .nav {
            padding-left: 0;
        }

        .sidebar .nav .collapse .nav .nav-item {
            margin-left: 0;
        }

        /* Flatpickr Material Dashboard Pro Rose Theme Styling */
        .flatpickr-calendar.material-dashboard-datepicker,
        .flatpickr-calendar.material-dashboard-monthpicker {
            box-shadow: 0 16px 24px 2px rgba(0, 0, 0, 0.14), 0 6px 30px 5px rgba(0, 0, 0, 0.12), 0 8px 10px -5px rgba(0, 0, 0, 0.2);
            border-radius: 6px;
            border: none;
        }

        .flatpickr-calendar .flatpickr-day.selected,
        .flatpickr-calendar .flatpickr-day.startRange,
        .flatpickr-calendar .flatpickr-day.endRange,
        .flatpickr-calendar .flatpickr-day.selected.inRange,
        .flatpickr-calendar .flatpickr-day.startRange.inRange,
        .flatpickr-calendar .flatpickr-day.endRange.inRange,
        .flatpickr-calendar .flatpickr-day.selected:focus,
        .flatpickr-calendar .flatpickr-day.startRange:focus,
        .flatpickr-calendar .flatpickr-day.endRange:focus,
        .flatpickr-calendar .flatpickr-day.selected:hover,
        .flatpickr-calendar .flatpickr-day.startRange:hover,
        .flatpickr-calendar .flatpickr-day.endRange:hover,
        .flatpickr-calendar .flatpickr-day.selected.prevMonthDay,
        .flatpickr-calendar .flatpickr-day.startRange.prevMonthDay,
        .flatpickr-calendar .flatpickr-day.endRange.prevMonthDay,
        .flatpickr-calendar .flatpickr-day.selected.nextMonthDay,
        .flatpickr-calendar .flatpickr-day.startRange.nextMonthDay,
        .flatpickr-calendar .flatpickr-day.endRange.nextMonthDay {
            background: #e91e63;
            border-color: #e91e63;
            color: #fff;
        }

        .flatpickr-calendar .flatpickr-day:hover {
            background: rgba(233, 30, 99, 0.1);
            border-color: rgba(233, 30, 99, 0.3);
        }

        .flatpickr-calendar .flatpickr-day.today {
            border-color: #e91e63;
            color: #e91e63;
        }

        .flatpickr-calendar .flatpickr-day.today:hover {
            background: #e91e63;
            color: #fff;
        }

        .flatpickr-calendar .flatpickr-day.highlighted-day {
            background: rgba(233, 30, 99, 0.05);
            border-color: rgba(233, 30, 99, 0.2);
        }

        .flatpickr-calendar .flatpickr-day.disabled {
            color: #ccc;
            background: #f5f5f5;
        }

        .flatpickr-calendar .flatpickr-months .flatpickr-month {
            background: #e91e63;
            color: #fff;
        }

        .flatpickr-calendar .flatpickr-current-month .flatpickr-monthDropdown-months {
            background: #e91e63;
            color: #fff;
        }

        .flatpickr-calendar .flatpickr-current-month .numInputWrapper span.arrowUp:after {
            border-bottom-color: #fff;
        }

        .flatpickr-calendar .flatpickr-current-month .numInputWrapper span.arrowDown:after {
            border-top-color: #fff;
        }

        .flatpickr-calendar .flatpickr-weekdays {
            background: #e91e63;
        }

        .flatpickr-calendar .flatpickr-weekday {
            background: #e91e63;
            color: #fff;
            font-weight: 500;
        }

        .flatpickr-calendar .flatpickr-prev-month,
        .flatpickr-calendar .flatpickr-next-month {
            color: #fff;
        }

        .flatpickr-calendar .flatpickr-prev-month:hover,
        .flatpickr-calendar .flatpickr-next-month:hover {
            color: rgba(255, 255, 255, 0.8);
        }

        /* Custom Month Picker Grid Styling */
        .custom-month-picker {
            position: absolute;
            z-index: 9999;
            background: white;
            border: 1px solid #ddd;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0, 0, 0, 0.15);
            padding: 0;
            min-width: 280px;
            font-family: 'Roboto', sans-serif;
        }

        .custom-month-picker-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            padding: 12px 16px;
            border-bottom: 1px solid #e0e0e0;
            background: #f8f9fa;
            border-radius: 8px 8px 0 0;
        }

        .custom-month-picker-year {
            font-size: 16px;
            font-weight: 500;
            color: #333;
        }

        .custom-month-picker-nav {
            background: none;
            border: none;
            font-size: 18px;
            color: #666;
            cursor: pointer;
            padding: 4px 8px;
            border-radius: 4px;
            transition: all 0.2s ease;
        }

        .custom-month-picker-nav:hover {
            background: #e0e0e0;
            color: #9c27b0;
        }

        .custom-month-picker-grid {
            display: grid;
            grid-template-columns: repeat(4, 1fr);
            gap: 8px;
            padding: 16px;
        }

        .custom-month-picker-month {
            padding: 12px 8px;
            text-align: center;
            border: 1px solid #e0e0e0;
            border-radius: 6px;
            cursor: pointer;
            transition: all 0.2s ease;
            background: #f8f9fa;
            font-weight: 500;
            color: #495057;
            font-size: 13px;
            min-height: 40px;
            display: flex;
            align-items: center;
            justify-content: center;
        }

        .custom-month-picker-month:hover {
            background: #e3f2fd;
            border-color: #9c27b0;
            color: #9c27b0;
            transform: translateY(-1px);
        }

        .custom-month-picker-month.selected {
            background: #9c27b0;
            border-color: #9c27b0;
            color: white;
        }

        .custom-month-picker-month.current {
            background: #e8f5e8;
            border-color: #4caf50;
            color: #2e7d32;
        }

        /* Month picker input styling */
        .monthpicker {
            cursor: pointer !important;
        }

        .monthpicker[readonly] {
            background-color: white !important;
            cursor: pointer !important;
        }

        /* Simplified month picker styling */
        .material-dashboard-monthpicker .flatpickr-months {
            padding: 15px !important;
        }

        .material-dashboard-monthpicker .flatpickr-month {
            background: transparent !important;
        }

        .material-dashboard-monthpicker .flatpickr-monthDropdown-months {
            font-size: 14px !important;
            padding: 8px 12px !important;
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            background: white !important;
            min-width: 120px !important;
        }

        .material-dashboard-monthpicker .numInput.cur-year {
            font-size: 14px !important;
            padding: 8px !important;
            border: 1px solid #ddd !important;
            border-radius: 4px !important;
            width: 80px !important;
            text-align: center !important;
        }

        .material-dashboard-monthpicker .flatpickr-current-month {
            padding: 10px 0 !important;
        }

        /* Hide unnecessary elements in month picker */
        .material-dashboard-monthpicker .flatpickr-weekdays,
        .material-dashboard-monthpicker .flatpickr-days {
            display: none !important;
        }

        /* Style the month picker calendar container */
        .material-dashboard-monthpicker.flatpickr-calendar {
            width: auto !important;
            min-width: 280px !important;
        }
    </style>

    <!-- Additional Libraries -->
    <script src="https://cdn.jsdelivr.net/npm/select2@4.1.0-rc.0/dist/js/select2.min.js"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr"></script>
    <script src="https://cdn.jsdelivr.net/npm/flatpickr/dist/plugins/monthSelect/index.js"></script>

    <!-- Debug Flatpickr loading -->
    <script>
        console.log('📅 Flatpickr loaded:', typeof flatpickr !== 'undefined');
        console.log('📅 monthSelectPlugin loaded:', typeof monthSelectPlugin !== 'undefined');
    </script>

    <!-- Bootstrap Material Design Polyfill (Global) -->
    <script>
        // Ensure bootstrapMaterialDesign function exists globally to prevent errors
        (function() {
            if (typeof window.jQuery !== 'undefined' && typeof window.jQuery.fn.bootstrapMaterialDesign !== 'function') {
                console.log('🔧 Adding global Bootstrap Material Design polyfill');
                window.jQuery.fn.bootstrapMaterialDesign = function(options) {
                    console.log('📦 Global Bootstrap Material Design polyfill executed');
                    // Initialize standard Bootstrap components as fallback
                    this.find('[data-toggle="tooltip"]').tooltip();
                    this.find('[data-toggle="popover"]').popover();
                    this.find('[data-toggle="dropdown"]').dropdown();
                    return this;
                };
            }
        })();
    </script>

    <!-- SmartBI Application JavaScript -->
    <script>
        $(document).ready(function() {
            // Initialize Material Dashboard
            console.log('🚀 Initializing Material Dashboard Pro...');

            // Wait for all scripts to load before initializing
            setTimeout(function() {
                // Initialize Bootstrap Material Design
                if (typeof $().bootstrapMaterialDesign === 'function') {
                    $('body').bootstrapMaterialDesign();
                    console.log('✅ Bootstrap Material Design initialized');
                } else {
                    console.warn('⚠️ Bootstrap Material Design not found');
                }

                // Initialize Material Dashboard core if available
                if (typeof window.md !== 'undefined') {
                    console.log('✅ Material Dashboard core found');

                    // Initialize sidebar background image
                    if (typeof md.checkSidebarImage === 'function') {
                        md.checkSidebarImage();
                        console.log('✅ Sidebar background image initialized');

                        // Debug: Check if background container was created
                        var $sidebar = $('.sidebar');
                        var $sidebarBg = $sidebar.find('.sidebar-background');
                        console.log('🔍 Sidebar element:', $sidebar.length);
                        console.log('🔍 Sidebar background container:', $sidebarBg.length);
                        console.log('🔍 Data-image attribute:', $sidebar.attr('data-image'));
                        if ($sidebarBg.length > 0) {
                            console.log('🔍 Background style:', $sidebarBg.attr('style'));
                        }

                        // Fallback: If framework didn't create background container, create it manually
                        if ($sidebarBg.length === 0 && $sidebar.length > 0) {
                            var imageUrl = $sidebar.attr('data-image');
                            if (imageUrl) {
                                console.log('🔧 Creating sidebar background manually with image:', imageUrl);
                                var backgroundContainer = '<div class="sidebar-background" style="background-image: url(' + imageUrl + ')"></div>';
                                $sidebar.append(backgroundContainer);
                                console.log('✅ Manual sidebar background created');
                            }
                        }
                    }

                    // Initialize sidebar checks
                    if (typeof md.initSidebarsCheck === 'function') {
                        md.initSidebarsCheck();
                        console.log('✅ Sidebar checks initialized');
                    }

                    // Initialize minimize sidebar functionality
                    if (typeof md.initMinimizeSidebar === 'function') {
                        md.initMinimizeSidebar();
                        console.log('✅ Minimize sidebar initialized');
                    }
                } else {
                    console.warn('⚠️ Material Dashboard core not found - navigation may have limited functionality');
                }

                // Ensure collapse functionality is working
                if (typeof $.fn.collapse === 'function') {
                    console.log('✅ Bootstrap collapse functionality available');
                } else {
                    console.warn('⚠️ Bootstrap collapse functionality not available');
                }
            }, 100);

            // Add Bootstrap Material Design polyfill if needed
            if (typeof $.fn.bootstrapMaterialDesign !== 'function') {
                console.log('🔧 Adding Bootstrap Material Design polyfill');
                $.fn.bootstrapMaterialDesign = function(options) {
                    console.log('📦 Bootstrap Material Design polyfill executed');
                    // Initialize standard Bootstrap components as fallback
                    this.find('[data-toggle="tooltip"]').tooltip();
                    this.find('[data-toggle="popover"]').popover();
                    this.find('[data-toggle="dropdown"]').dropdown();
                    return this;
                };
            }

            // Initialize Material Dashboard Pro notifications
            if (typeof $.notify !== 'undefined') {
                console.log('✅ Material Dashboard Pro notifications available');
            } else {
                console.warn('⚠️ Material Dashboard Pro notifications not available');
            }

            // Auto-dismiss Bootstrap alerts after 10 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 10000);

            // Display TempData messages using Material Dashboard Pro notifications
            @if (TempData["ToastSuccessMessage"] != null)
            {
                <text>SmartBIToast.success('@Html.Raw(TempData["ToastSuccessMessage"])', 'Success');</text>
            }
            @if (TempData["ToastErrorMessage"] != null)
            {
                <text>SmartBIToast.error('@Html.Raw(TempData["ToastErrorMessage"])', 'Error');</text>
            }
            @if (TempData["ToastWarningMessage"] != null)
            {
                <text>SmartBIToast.warning('@Html.Raw(TempData["ToastWarningMessage"])', 'Warning');</text>
            }
            @if (TempData["ToastInfoMessage"] != null)
            {
                <text>SmartBIToast.info('@Html.Raw(TempData["ToastInfoMessage"])', 'Info');</text>
            }

            // Simple Select2 initialization following WebAppSelect2Demo approach
            if (typeof $.fn.select2 !== 'undefined') {
                $('.select2').select2({
                    placeholder: "Select an option",
                    allowClear: true,
                    width: 'resolve'
                });
                console.log('✅ Select2 initialized with clean configuration');
            } else {
                console.warn('⚠️ Select2 not available');
            }

            // Initialize Flatpickr with Material Dashboard Pro styling
            initializeFlatpickr();

            // Initialize Material Dashboard Pro form behaviors
            initializeMaterialForms();

            // Initialize Fixed Plugin functionality
            initFixedPlugin();

            // Initialize sidebar navigation - ensure all menus are collapsed by default
            initSidebarNavigation();

            console.log('✅ SmartBI Application initialized successfully');
        });

        // Global toast messaging functions using Material Dashboard Pro notifications
        window.SmartBIToast = {
            success: function(message, title = '') {
                if (typeof $.notify !== 'undefined') {
                    $.notify({
                        icon: "check",
                        message: message,
                        title: title
                    }, {
                        type: 'success',
                        timer: 3000,
                        placement: {
                            from: 'top',
                            align: 'right'
                        },
                        animate: {
                            enter: 'animated fadeInDown',
                            exit: 'animated fadeOutUp'
                        }
                    });
                } else {
                    alert('SUCCESS: ' + message);
                }
            },
            error: function(message, title = '') {
                if (typeof $.notify !== 'undefined') {
                    $.notify({
                        icon: "error_outline",
                        message: message,
                        title: title
                    }, {
                        type: 'danger',
                        timer: 5000,
                        placement: {
                            from: 'top',
                            align: 'right'
                        },
                        animate: {
                            enter: 'animated fadeInDown',
                            exit: 'animated fadeOutUp'
                        }
                    });
                } else {
                    alert('ERROR: ' + message);
                }
            },
            warning: function(message, title = '') {
                if (typeof $.notify !== 'undefined') {
                    $.notify({
                        icon: "warning",
                        message: message,
                        title: title
                    }, {
                        type: 'warning',
                        timer: 4000,
                        placement: {
                            from: 'top',
                            align: 'right'
                        },
                        animate: {
                            enter: 'animated fadeInDown',
                            exit: 'animated fadeOutUp'
                        }
                    });
                } else {
                    alert('WARNING: ' + message);
                }
            },
            info: function(message, title = '') {
                if (typeof $.notify !== 'undefined') {
                    $.notify({
                        icon: "info_outline",
                        message: message,
                        title: title
                    }, {
                        type: 'info',
                        timer: 3000,
                        placement: {
                            from: 'top',
                            align: 'right'
                        },
                        animate: {
                            enter: 'animated fadeInDown',
                            exit: 'animated fadeOutUp'
                        }
                    });
                } else {
                    alert('INFO: ' + message);
                }
            },
            clear: function() {
                if (typeof $.notifyClose !== 'undefined') {
                    $.notifyClose('all');
                }
            }
        };



        // Theme Settings Persistence Functions
        function saveThemeSetting(key, value) {
            try {
                var themeSettings = JSON.parse(localStorage.getItem('smartbi_theme_settings') || '{}');
                themeSettings[key] = value;
                localStorage.setItem('smartbi_theme_settings', JSON.stringify(themeSettings));
                console.log('💾 Theme setting saved:', key, '=', value);
            } catch (e) {
                console.warn('⚠️ Failed to save theme setting:', e);
            }
        }

        function getThemeSetting(key, defaultValue) {
            try {
                var themeSettings = JSON.parse(localStorage.getItem('smartbi_theme_settings') || '{}');
                return themeSettings.hasOwnProperty(key) ? themeSettings[key] : defaultValue;
            } catch (e) {
                console.warn('⚠️ Failed to get theme setting:', e);
                return defaultValue;
            }
        }

        function loadThemeSettings() {
            console.log('🔄 Loading saved theme settings...');

            var $sidebar = $('.sidebar');
            var $sidebar_img_container = $sidebar.find('.sidebar-background');
            var $full_page = $('.full-page');
            var $sidebar_responsive = $('body > .navbar-collapse');

            // Load sidebar color
            var savedColor = getThemeSetting('sidebarColor', 'rose');
            if ($sidebar.length != 0) {
                $sidebar.attr('data-color', savedColor);
            }
            if ($full_page.length != 0) {
                $full_page.attr('filter-color', savedColor);
            }
            if ($sidebar_responsive.length != 0) {
                $sidebar_responsive.attr('data-color', savedColor);
            }
            // Update active color in UI
            $('.fixed-plugin .active-color span').removeClass('active');
            $('.fixed-plugin .active-color span[data-color="' + savedColor + '"]').addClass('active');

            // Load sidebar background color
            var savedBgColor = getThemeSetting('sidebarBackgroundColor', 'black');
            if ($sidebar.length != 0) {
                $sidebar.attr('data-background-color', savedBgColor);
            }
            // Update active background color in UI
            $('.fixed-plugin .background-color .badge').removeClass('active');
            $('.fixed-plugin .background-color .badge[data-background-color="' + savedBgColor + '"]').addClass('active');

            // Load sidebar image
            var savedImage = getThemeSetting('sidebarImage', null);
            if (savedImage && $sidebar_img_container.length != 0) {
                $sidebar_img_container.css('background-image', 'url("' + savedImage + '")');
                // Update active image in UI
                $('.fixed-plugin .img-holder').parent('li').removeClass('active');
                $('.fixed-plugin .img-holder img[src="' + savedImage + '"]').closest('li').addClass('active');
            }

            // Load sidebar images enabled state
            var imagesEnabled = getThemeSetting('sidebarImagesEnabled', true);
            $('.switch-sidebar-image input').prop('checked', imagesEnabled);
            if (imagesEnabled) {
                if ($sidebar_img_container.length != 0) {
                    $sidebar_img_container.show();
                    $sidebar.attr('data-image', '#');
                }
            } else {
                if ($sidebar_img_container.length != 0) {
                    $sidebar.removeAttr('data-image');
                    $sidebar_img_container.hide();
                }
            }

            // Load sidebar mini state
            var miniEnabled = getThemeSetting('sidebarMini', false);
            $('.switch-sidebar-mini input').prop('checked', miniEnabled);
            if (miniEnabled) {
                $('body').addClass('sidebar-mini');
                if (typeof md !== 'undefined' && md.misc) {
                    md.misc.sidebar_mini_active = true;
                }
            } else {
                $('body').removeClass('sidebar-mini');
                if (typeof md !== 'undefined' && md.misc) {
                    md.misc.sidebar_mini_active = false;
                }
            }

            console.log('✅ Theme settings loaded successfully');
        }

        // Fixed Plugin Initialization Function
        function initFixedPlugin() {
            console.log('🎨 Initializing Fixed Plugin...');

            var $sidebar = $('.sidebar');
            var $sidebar_img_container = $sidebar.find('.sidebar-background');
            var $full_page = $('.full-page');
            var $sidebar_responsive = $('body > .navbar-collapse');
            var window_width = $(window).width();

            // Load saved theme settings from localStorage
            loadThemeSettings();

            // Auto-open Fixed Plugin on Dashboard page for desktop
            var fixed_plugin_open = $('.sidebar .sidebar-wrapper .nav li.active a p').html();
            if (window_width > 767 && fixed_plugin_open == 'Dashboard') {
                if ($('.fixed-plugin .dropdown').hasClass('show-dropdown')) {
                    $('.fixed-plugin .dropdown').addClass('open');
                }
            }

            // Prevent dropdown from closing when clicking on switches
            $('.fixed-plugin a').click(function(event) {
                if ($(this).hasClass('switch-trigger')) {
                    if (event.stopPropagation) {
                        event.stopPropagation();
                    } else if (window.event) {
                        window.event.cancelBubble = true;
                    }
                }
            });

            // Sidebar Color Change
            $('.fixed-plugin .active-color span').click(function() {
                $(this).siblings().removeClass('active');
                $(this).addClass('active');

                var new_color = $(this).data('color');

                if ($sidebar.length != 0) {
                    $sidebar.attr('data-color', new_color);
                }

                if ($full_page.length != 0) {
                    $full_page.attr('filter-color', new_color);
                }

                if ($sidebar_responsive.length != 0) {
                    $sidebar_responsive.attr('data-color', new_color);
                }

                // Save to localStorage
                saveThemeSetting('sidebarColor', new_color);
                console.log('🎨 Sidebar color changed to:', new_color);
            });

            // Sidebar Background Color Change
            $('.fixed-plugin .background-color .badge').click(function() {
                $(this).siblings().removeClass('active');
                $(this).addClass('active');

                var new_color = $(this).data('background-color');

                if ($sidebar.length != 0) {
                    $sidebar.attr('data-background-color', new_color);
                }

                // Save to localStorage
                saveThemeSetting('sidebarBackgroundColor', new_color);
                console.log('🎨 Sidebar background changed to:', new_color);
            });

            // Sidebar Background Image Change
            $('.fixed-plugin .img-holder').click(function() {
                $(this).parent('li').siblings().removeClass('active');
                $(this).parent('li').addClass('active');

                var new_image = $(this).find("img").attr('src');

                if ($sidebar_img_container.length != 0 && $('.switch-sidebar-image input:checked').length != 0) {
                    $sidebar_img_container.fadeOut('fast', function() {
                        $sidebar_img_container.css('background-image', 'url("' + new_image + '")');
                        $sidebar_img_container.fadeIn('fast');
                    });
                }

                // Save to localStorage
                saveThemeSetting('sidebarImage', new_image);
                console.log('🎨 Sidebar background image changed to:', new_image);
            });

            // Sidebar Images Toggle
            $('.switch-sidebar-image input').change(function() {
                var $input = $(this);

                if ($input.is(':checked')) {
                    if ($sidebar_img_container.length != 0) {
                        $sidebar_img_container.fadeIn('fast');
                        $sidebar.attr('data-image', '#');
                    }
                    // Save to localStorage
                    saveThemeSetting('sidebarImagesEnabled', true);
                    console.log('🎨 Sidebar images enabled');
                } else {
                    if ($sidebar_img_container.length != 0) {
                        $sidebar.removeAttr('data-image');
                        $sidebar_img_container.fadeOut('fast');
                    }
                    // Save to localStorage
                    saveThemeSetting('sidebarImagesEnabled', false);
                    console.log('🎨 Sidebar images disabled');
                }
            });

            // Sidebar Mini Toggle
            $('.switch-sidebar-mini input').change(function() {
                var $body = $('body');
                var $input = $(this);

                if (typeof md !== 'undefined' && md.misc && md.misc.sidebar_mini_active == true) {
                    $('body').removeClass('sidebar-mini');
                    if (md.misc) md.misc.sidebar_mini_active = false;

                    // Initialize perfect scrollbar if available
                    if (typeof $.fn.perfectScrollbar === 'function') {
                        $('.sidebar .sidebar-wrapper, .main-panel').perfectScrollbar();
                    }
                    // Save to localStorage
                    saveThemeSetting('sidebarMini', false);
                    console.log('🎨 Sidebar mini disabled');
                } else {
                    // Destroy perfect scrollbar if available
                    if (typeof $.fn.perfectScrollbar === 'function') {
                        $('.sidebar .sidebar-wrapper, .main-panel').perfectScrollbar('destroy');
                    }

                    setTimeout(function() {
                        $('body').addClass('sidebar-mini');
                        if (typeof md !== 'undefined' && md.misc) {
                            md.misc.sidebar_mini_active = true;
                        }
                    }, 300);
                    // Save to localStorage
                    saveThemeSetting('sidebarMini', true);
                    console.log('🎨 Sidebar mini enabled');
                }

                // Simulate window resize for charts
                var simulateWindowResize = setInterval(function() {
                    window.dispatchEvent(new Event('resize'));
                }, 180);

                setTimeout(function() {
                    clearInterval(simulateWindowResize);
                }, 1000);
            });

            console.log('✅ Fixed Plugin initialized successfully');
        }

        // Sidebar Navigation Initialization Function
        function initSidebarNavigation() {
            console.log('🧭 Initializing Material Dashboard Pro Sidebar Navigation...');

            // Ensure all sidebar menu items are collapsed by default
            $('.sidebar .nav .collapse').removeClass('show').attr('aria-expanded', 'false');
            $('.sidebar .nav .nav-link[data-toggle="collapse"]').attr('aria-expanded', 'false');

            // Ensure user dropdown is also collapsed by default
            $('#ProfileNav').removeClass('show').attr('aria-expanded', 'false');
            $('.sidebar .user .username').attr('aria-expanded', 'false');

            // Remove any existing event handlers to prevent conflicts
            $('.sidebar .nav .nav-link[data-toggle="collapse"]').off('click');
            $('.sidebar .user .username').off('click');

            // MAIN NAVIGATION: Let Bootstrap handle the collapse behavior naturally
            // Just add accordion behavior to close other menus
            $('.sidebar .nav .nav-link[data-toggle="collapse"]').on('click', function(e) {
                var $this = $(this);
                var target = $this.attr('href');
                var $target = $(target);

                // Close all other open main navigation menus (accordion behavior)
                $('.sidebar .nav .collapse.show').not($target).collapse('hide');

                console.log('🔄 Main navigation clicked:', target);
            });

            // Handle Bootstrap collapse events for proper state management and caret rotation
            $('.sidebar .nav .collapse').on('show.bs.collapse', function() {
                var $this = $(this);
                var $trigger = $('.sidebar .nav .nav-link[href="#' + $this.attr('id') + '"]');
                $trigger.attr('aria-expanded', 'true');
                $trigger.find('.caret').addClass('rotate-180');
                console.log('🔼 Main menu expanded:', '#' + $this.attr('id'));
            });

            $('.sidebar .nav .collapse').on('hide.bs.collapse', function() {
                var $this = $(this);
                var $trigger = $('.sidebar .nav .nav-link[href="#' + $this.attr('id') + '"]');
                $trigger.attr('aria-expanded', 'false');
                $trigger.find('.caret').removeClass('rotate-180');
                console.log('🔽 Main menu collapsed:', '#' + $this.attr('id'));
            });

            // USER DROPDOWN: Handle separately with vertical dropdown behavior
            $('.sidebar .user .username').on('click', function(e) {
                e.preventDefault();
                e.stopPropagation();

                var $this = $(this);
                var target = $this.attr('href'); // Should be #ProfileNav
                var $target = $(target);
                var isExpanded = $target.hasClass('show');

                // Toggle user dropdown (vertical dropdown, not slide-right)
                if (isExpanded) {
                    $target.removeClass('show').attr('aria-expanded', 'false');
                    $this.attr('aria-expanded', 'false');
                    $this.find('.caret').removeClass('rotate-180');
                    console.log('🔽 User dropdown collapsed');
                } else {
                    $target.addClass('show').attr('aria-expanded', 'true');
                    $this.attr('aria-expanded', 'true');
                    $this.find('.caret').addClass('rotate-180');
                    console.log('🔼 User dropdown expanded');
                }

                return false;
            });

            // Close user dropdown when clicking elsewhere
            $(document).on('click', function(e) {
                if (!$(e.target).closest('.sidebar .user').length) {
                    $('#ProfileNav').removeClass('show').attr('aria-expanded', 'false');
                    $('.sidebar .user .username').attr('aria-expanded', 'false').find('.caret').removeClass('rotate-180');
                }
            });

            // Add hover effects for better UX
            $('.sidebar .nav .nav-link').hover(
                function() {
                    if (!$(this).hasClass('active')) {
                        $(this).addClass('nav-link-hover');
                    }
                },
                function() {
                    $(this).removeClass('nav-link-hover');
                }
            );

            console.log('✅ Material Dashboard Pro Sidebar Navigation initialized successfully');
        }

        // Material Dashboard Pro Form Initialization Function
        function initializeMaterialForms() {
            console.log('📝 Initializing Material Dashboard Pro Forms...');

            // Handle form group focus states for floating labels
            $('.form-control').off('focus.materialForms blur.materialForms');
            $('.form-control').on('focus.materialForms', function() {
                $(this).closest('.form-group').addClass('is-focused');
            });

            $('.form-control').on('blur.materialForms', function() {
                var $formGroup = $(this).closest('.form-group');
                $formGroup.removeClass('is-focused');

                if ($(this).val() !== '' && $(this).val() !== null) {
                    $formGroup.addClass('is-filled has-value');
                } else {
                    $formGroup.removeClass('is-filled has-value');
                }
            });

            // Check for pre-filled values and mark form groups accordingly
            $('.form-control').each(function() {
                var $this = $(this);
                var $formGroup = $this.closest('.form-group');

                if ($this.val() !== '' && $this.val() !== null) {
                    $formGroup.addClass('is-filled has-value');
                } else {
                    $formGroup.removeClass('is-filled has-value');
                }
            });

            // Handle Select2 form group states
            $('.select2').off('select2:open.materialForms select2:close.materialForms change.materialForms');
            $('.select2').on('select2:open.materialForms', function() {
                $(this).closest('.form-group').addClass('is-focused');
            });

            $('.select2').on('select2:close.materialForms', function() {
                var $formGroup = $(this).closest('.form-group');
                $formGroup.removeClass('is-focused');

                if ($(this).val() !== '' && $(this).val() !== null) {
                    $formGroup.addClass('is-filled has-value');
                } else {
                    $formGroup.removeClass('is-filled has-value');
                }
            });

            $('.select2').on('change.materialForms', function() {
                var $formGroup = $(this).closest('.form-group');

                if ($(this).val() !== '' && $(this).val() !== null) {
                    $formGroup.addClass('is-filled has-value');
                } else {
                    $formGroup.removeClass('is-filled has-value');
                }
            });

            // Handle multi-select dropdowns
            $('.select2-multiple').off('select2:open.materialForms select2:close.materialForms change.materialForms');
            $('.select2-multiple').on('select2:open.materialForms', function() {
                $(this).closest('.form-group').addClass('is-focused');
            });

            $('.select2-multiple').on('select2:close.materialForms', function() {
                var $formGroup = $(this).closest('.form-group');
                $formGroup.removeClass('is-focused');

                var selectedValues = $(this).val();
                if (selectedValues && selectedValues.length > 0) {
                    $formGroup.addClass('is-filled has-value');
                } else {
                    $formGroup.removeClass('is-filled has-value');
                }
            });

            $('.select2-multiple').on('change.materialForms', function() {
                var $formGroup = $(this).closest('.form-group');

                var selectedValues = $(this).val();
                if (selectedValues && selectedValues.length > 0) {
                    $formGroup.addClass('is-filled has-value');
                } else {
                    $formGroup.removeClass('is-filled has-value');
                }
            });

            console.log('✅ Material Dashboard Pro Forms initialized successfully');
        }

        // Flatpickr Initialization Function with Material Dashboard Pro Styling
        function initializeFlatpickr() {
            console.log('📅 Initializing Flatpickr with Material Dashboard Pro styling...');

            if (typeof flatpickr === 'undefined') {
                console.warn('⚠️ Flatpickr not available');
                return;
            }

            // Initialize date pickers
            $('input[class*=datepicker]:not(.flatpickr-input)').each(function() {
                var $input = $(this);

                flatpickr(this, {
                    dateFormat: "m/d/Y",
                    allowInput: true,
                    clickOpens: true,
                    theme: "material_blue",
                    // Weekend disabling (Friday=5, Saturday=6)
                    disable: [
                        function(date) {
                            return (date.getDay() === 5 || date.getDay() === 6);
                        }
                    ],
                    // Highlight weekdays (Sunday=0 to Thursday=4)
                    onDayCreate: function(dObj, dStr, fp, dayElem) {
                        var day = dayElem.dateObj.getDay();
                        if (day >= 0 && day <= 4) {
                            dayElem.classList.add("highlighted-day");
                        }
                    },
                    // Auto-close on selection
                    onChange: function(selectedDates, dateStr, instance) {
                        if (selectedDates.length > 0) {
                            instance.close();
                        }
                    },
                    // Material Dashboard Pro styling
                    onReady: function(selectedDates, dateStr, instance) {
                        // Add Material Dashboard Pro classes
                        instance.calendarContainer.classList.add('material-dashboard-datepicker');
                    }
                });

                console.log('📅 Date picker initialized for:', $input.attr('id') || $input.attr('name') || 'unnamed input');
            });

            // Initialize custom month pickers with Material theme grid layout
            $('input[class*=monthpicker]:not(.custom-month-picker-initialized)').each(function() {
                var $input = $(this);
                $input.addClass('custom-month-picker-initialized');

                // Make input readonly and add click handler
                $input.attr('readonly', true);
                $input.css('cursor', 'pointer');

                // Initialize custom month picker
                initializeCustomMonthPicker($input[0]);

                console.log('📅 Custom month picker initialized for:', $input.attr('id') || $input.attr('name') || 'unnamed input');
            });

            console.log('✅ Flatpickr initialization completed');
        }

        // Custom Month Picker Implementation
        function initializeCustomMonthPicker(input) {
            var currentYear = new Date().getFullYear();
            var currentMonth = new Date().getMonth();
            var selectedYear = currentYear;
            var selectedMonth = null;
            var picker = null;

            // Month names
            var monthNames = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun',
                             'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
            var fullMonthNames = ['January', 'February', 'March', 'April', 'May', 'June',
                                 'July', 'August', 'September', 'October', 'November', 'December'];

            function createMonthPicker() {
                picker = $('<div class="custom-month-picker"></div>');

                // Header with year navigation
                var header = $('<div class="custom-month-picker-header"></div>');
                var prevBtn = $('<button class="custom-month-picker-nav">‹</button>');
                var yearDisplay = $('<div class="custom-month-picker-year">' + selectedYear + '</div>');
                var nextBtn = $('<button class="custom-month-picker-nav">›</button>');

                header.append(prevBtn, yearDisplay, nextBtn);
                picker.append(header);

                // Month grid
                var grid = $('<div class="custom-month-picker-grid"></div>');

                for (var i = 0; i < 12; i++) {
                    var monthBtn = $('<div class="custom-month-picker-month" data-month="' + i + '">' + monthNames[i] + '</div>');

                    // Highlight current month if it's the current year
                    if (selectedYear === currentYear && i === currentMonth) {
                        monthBtn.addClass('current');
                    }

                    grid.append(monthBtn);
                }

                picker.append(grid);
                return picker;
            }

            function showPicker() {
                if (picker) {
                    picker.remove();
                }

                picker = createMonthPicker();
                $('body').append(picker);

                // Position picker below input
                var inputOffset = $(input).offset();
                var inputHeight = $(input).outerHeight();

                picker.css({
                    position: 'absolute',
                    top: inputOffset.top + inputHeight + 5,
                    left: inputOffset.left,
                    zIndex: 9999
                });

                // Event handlers
                picker.find('.custom-month-picker-nav').first().click(function() {
                    selectedYear--;
                    updateYearDisplay();
                });

                picker.find('.custom-month-picker-nav').last().click(function() {
                    selectedYear++;
                    updateYearDisplay();
                });

                picker.find('.custom-month-picker-month').click(function() {
                    var month = parseInt($(this).data('month'));
                    selectMonth(month);
                });

                // Close picker when clicking outside
                $(document).on('click.monthpicker', function(e) {
                    if (!$(e.target).closest('.custom-month-picker').length && e.target !== input) {
                        hidePicker();
                    }
                });
            }

            function updateYearDisplay() {
                picker.find('.custom-month-picker-year').text(selectedYear);

                // Update current month highlighting
                picker.find('.custom-month-picker-month').removeClass('current');
                if (selectedYear === currentYear) {
                    picker.find('.custom-month-picker-month[data-month="' + currentMonth + '"]').addClass('current');
                }
            }

            function selectMonth(month) {
                selectedMonth = month;

                // Update input value with mm-yyyy format
                var monthStr = (month + 1).toString().padStart(2, '0');
                $(input).val(monthStr + '-' + selectedYear);

                // Update display to show full month name
                $(input).attr('title', fullMonthNames[month] + ' ' + selectedYear);

                // Trigger change event
                $(input).trigger('change');

                hidePicker();
            }

            function hidePicker() {
                if (picker) {
                    picker.remove();
                    picker = null;
                }
                $(document).off('click.monthpicker');
            }

            // Initialize input click handler
            $(input).click(function(e) {
                e.preventDefault();
                e.stopPropagation();

                // Parse existing value if any (mm-yyyy format)
                var currentValue = $(input).val();
                if (currentValue && currentValue.match(/^\d{1,2}-\d{4}$/)) {
                    var parts = currentValue.split('-');
                    selectedMonth = parseInt(parts[0]) - 1;
                    selectedYear = parseInt(parts[1]);
                } else {
                    selectedYear = currentYear;
                    selectedMonth = null;
                }

                showPicker();
            });

            // Set placeholder
            $(input).attr('placeholder', 'Select a month...');
        }

        // Global function to reinitialize Flatpickr for specific containers (for AJAX content)
        window.initializeAllFlatpickrComponents = function(container) {
            console.log('📅 Initializing Flatpickr components for container:', container || 'document');

            var $container = container ? $(container) : $(document);

            if (typeof flatpickr === 'undefined') {
                console.warn('⚠️ Flatpickr not available');
                return;
            }

            // Debug: Check what elements are found
            var datepickerElements = $container.find('input[class*=datepicker]:not(.flatpickr-input)');
            var monthpickerElements = $container.find('input[class*=monthpicker]:not(.flatpickr-input)');
            console.log('📅 Found datepicker elements:', datepickerElements.length);
            console.log('📅 Found monthpicker elements:', monthpickerElements.length);

            if (datepickerElements.length > 0) {
                console.log('📅 Datepicker elements:', datepickerElements.toArray().map(el => ({ id: el.id, name: el.name, class: el.className })));
            }
            if (monthpickerElements.length > 0) {
                console.log('📅 Monthpicker elements:', monthpickerElements.toArray().map(el => ({ id: el.id, name: el.name, class: el.className })));
            }

            // Initialize date pickers in container
            $container.find('input[class*=datepicker]:not(.flatpickr-input)').each(function() {
                var $input = $(this);

                flatpickr(this, {
                    dateFormat: "m/d/Y",
                    allowInput: true,
                    clickOpens: true,
                    theme: "material_blue",
                    // Weekend disabling (Friday=5, Saturday=6)
                    disable: [
                        function(date) {
                            return (date.getDay() === 5 || date.getDay() === 6);
                        }
                    ],
                    // Highlight weekdays (Sunday=0 to Thursday=4)
                    onDayCreate: function(dObj, dStr, fp, dayElem) {
                        var day = dayElem.dateObj.getDay();
                        if (day >= 0 && day <= 4) {
                            dayElem.classList.add("highlighted-day");
                        }
                    },
                    // Auto-close on selection
                    onChange: function(selectedDates, dateStr, instance) {
                        if (selectedDates.length > 0) {
                            instance.close();
                        }
                    },
                    // Material Dashboard Pro styling
                    onReady: function(selectedDates, dateStr, instance) {
                        // Add Material Dashboard Pro classes
                        instance.calendarContainer.classList.add('material-dashboard-datepicker');
                    }
                });

                console.log('📅 Date picker initialized for:', $input.attr('id') || $input.attr('name') || 'unnamed input');
            });

            // Initialize custom month pickers in container with Material theme grid layout
            $container.find('input[class*=monthpicker]:not(.custom-month-picker-initialized)').each(function() {
                var $input = $(this);
                $input.addClass('custom-month-picker-initialized');

                // Make input readonly and add click handler
                $input.attr('readonly', true);
                $input.css('cursor', 'pointer');

                // Initialize custom month picker
                initializeCustomMonthPicker($input[0]);

                console.log('📅 Custom month picker initialized for:', $input.attr('id') || $input.attr('name') || 'unnamed input');
            });

            console.log('✅ Flatpickr components initialization completed for container');
        };

        // Debug function for troubleshooting Flatpickr
        window.debugFlatpickr = function() {
            console.log('🔧 === Flatpickr Debug Information ===');
            console.log('🔧 Flatpickr available:', typeof flatpickr !== 'undefined');
            console.log('🔧 monthSelectPlugin available:', typeof monthSelectPlugin !== 'undefined');
            console.log('🔧 jQuery available:', typeof $ !== 'undefined');

            var allDatepickers = $('input[class*=datepicker]');
            var allMonthpickers = $('input[class*=monthpicker]');
            var flatpickrInputs = $('.flatpickr-input');

            console.log('🔧 Total datepicker elements found:', allDatepickers.length);
            console.log('🔧 Total monthpicker elements found:', allMonthpickers.length);
            console.log('🔧 Already initialized Flatpickr inputs:', flatpickrInputs.length);

            if (allDatepickers.length > 0) {
                console.log('🔧 Datepicker elements details:');
                allDatepickers.each(function(i, el) {
                    console.log(`  ${i+1}. ID: ${el.id}, Name: ${el.name}, Class: ${el.className}, Visible: ${$(el).is(':visible')}`);
                });
            }

            if (allMonthpickers.length > 0) {
                console.log('🔧 Monthpicker elements details:');
                allMonthpickers.each(function(i, el) {
                    console.log(`  ${i+1}. ID: ${el.id}, Name: ${el.name}, Class: ${el.className}, Visible: ${$(el).is(':visible')}`);
                });
            }

            console.log('🔧 === End Debug Information ===');
        };

        // Global function to reinitialize forms (for AJAX content)
        window.initializeMaterialForms = initializeMaterialForms;
        window.initializeFlatpickr = initializeFlatpickr;
    </script>

    <!-- Global Modal for CRUD Operations -->
    <div class="modal fade" id="form-modal" tabindex="-1" role="dialog" aria-labelledby="form-modal-label" aria-hidden="true">
        <div class="modal-dialog modal-lg" role="document">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title" id="form-modal-label">Modal Title</h5>
                    <button type="button" class="close" data-dismiss="modal" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
                <div class="modal-body">
                    <!-- Content will be loaded here -->
                </div>
            </div>
        </div>
    </div>

    <!-- Global JavaScript Functions -->
    <script>
        // Global function to show modal popup
        function showInPopup(url, title) {
            $.get(url).done(function (data) {
                $('#form-modal .modal-title').html(title);
                $('#form-modal .modal-body').html(data);
                $('#form-modal').modal('show');

                // Reinitialize Material Dashboard Pro forms and Select2 in modal
                setTimeout(function() {
                    initializeMaterialForms();

                    // Simple Select2 initialization in modal
                    $('#form-modal .select2').select2({
                        placeholder: "Select an option",
                        allowClear: true,
                        width: 'resolve',
                        dropdownParent: $('#form-modal')
                    });

                    console.log('✅ Modal forms initialized');
                }, 100);
            }).fail(function (xhr, status, error) {
                var errorMessage = 'Failed to load the form. ';
                if (xhr.status === 404) {
                    errorMessage += 'The requested page was not found.';
                } else if (xhr.status === 500) {
                    errorMessage += 'Internal server error.';
                } else if (xhr.status === 0) {
                    errorMessage += 'Network error or server is not responding.';
                } else {
                    errorMessage += 'Error ' + xhr.status + ': ' + (xhr.statusText || 'Unknown error');
                }
                SmartBIToast.error(errorMessage);
            });
        }

        // Global function to close modal safely
        function closeModal() {
            try {
                if (typeof $('#form-modal').modal === 'function') {
                    $('#form-modal').modal('hide');
                } else {
                    // Fallback for when Bootstrap modal is not available
                    $('#form-modal').hide();
                    $('.modal-backdrop').remove();
                    $('body').removeClass('modal-open');
                }
            } catch (ex) {
                console.error('Error closing modal:', ex);
                // Force close modal
                $('#form-modal').hide();
                $('.modal-backdrop').remove();
                $('body').removeClass('modal-open');
            }
        }

        // Global function to handle form submission
        function jQueryAjaxPost(form) {
            try {
                $.ajax({
                    type: 'POST',
                    url: form.action,
                    data: new FormData(form),
                    contentType: false,
                    processData: false,
                    success: function (res) {
                        if (res.isValid) {
                            $('#form-modal .modal-body').html('');
                            $('#form-modal .modal-title').html('');
                            $('#form-modal').modal('hide');

                            if (res.message) {
                                if (res.type === 'warning') {
                                    SmartBIToast.warning(res.message);
                                } else if (res.type === 'info') {
                                    SmartBIToast.info(res.message);
                                } else if (res.success === false) {
                                    SmartBIToast.error(res.message);
                                } else {
                                    SmartBIToast.success(res.message);
                                }
                            }

                            $('.content .container-fluid').html(res.html);

                            // Reinitialize all components after content update
                            setTimeout(function() {
                                // Reinitialize Material Dashboard Pro forms
                                initializeMaterialForms();

                                // Simple Select2 reinitialization
                                $('.select2').select2({
                                    placeholder: "Select an option",
                                    allowClear: true,
                                    width: 'resolve'
                                });

                                // Reinitialize DataTable if it exists
                                if ($.fn.DataTable && $.fn.DataTable.isDataTable('#UserTable')) {
                                    $('#UserTable').DataTable().destroy();
                                }

                                if ($('#UserTable').length > 0) {
                                    $("#UserTable").DataTable({
                                        "paging": true,
                                        "lengthChange": true,
                                        lengthMenu: [[5, 10, 15, -1], [5, 10, 15, 'All']],
                                        "searching": true,
                                        "ordering": true,
                                        "aaSorting": [],
                                        "info": true,
                                        "autoWidth": false,
                                        "responsive": true,
                                        "columnDefs": [
                                            { "orderable": false, "targets": -1 }
                                        ]
                                    });
                                }

                                console.log('✅ Components reinitialized after AJAX update');
                            }, 100);
                        } else {
                            $('#form-modal .modal-body').html(res.html);
                        }
                    },
                    error: function (err) {
                        SmartBIToast.error('An error occurred while processing your request.');
                        console.error('AJAX Error:', err);
                    }
                });
            } catch (ex) {
                console.error('Exception in jQueryAjaxPost:', ex);
                SmartBIToast.error('An error occurred while submitting the form.');
            }
            return false;
        }
    </script>

    @RenderSection("Scripts", required: false)
</body>
</html>
