@model SmartBI.Data.ViewModels.RM_PERFORMANCE_DASHBOARD

<div class="row">
    <!-- Total Stats Row -->
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-rose card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_balance_wallet</i>
                </div>
                <p class="card-category">Total Accounts</p>
                <h3 class="card-title">@((Model.CA_AC + Model.SA_AC + Model.FDR_AC + Model.DPS_AC).ToString("N0"))</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-rose">info</i>
                    All account types combined
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-success card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_balance</i>
                </div>
                <p class="card-category">Total Balances</p>
                <h3 class="card-title">BDT @((Model.CA_BAL + Model.SA_BAL + Model.FDR_BAL + Model.DPS_BAL).ToString("N2"))</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-success">info</i>
                    Combined balance amount
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">trending_up</i>
                </div>
                <p class="card-category">Average Balances</p>
                <h3 class="card-title">BDT @(((Model.CA_BAL + Model.SA_BAL + Model.FDR_BAL + Model.DPS_BAL) / Math.Max(1, Model.CA_AC + Model.SA_AC + Model.FDR_AC + Model.DPS_AC)).ToString("N2"))</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-info">info</i>
                    Per account average
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-warning card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">monetization_on</i>
                </div>
                <p class="card-category">Cost of Deposit</p>
                <h3 class="card-title">@(Model.COD.ToString("N2"))%</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-warning">info</i>
                    Current COD rate
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Current Account Stats Row -->
<div class="row">
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-warning card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_circle</i>
                </div>
                <p class="card-category">Total Current Accounts</p>
                <h3 class="card-title">@Model.CA_AC.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-warning">info</i>
                    Current accounts count
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-warning card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_balance</i>
                </div>
                <p class="card-category">Total Current Balances</p>
                <h3 class="card-title">BDT @Model.CA_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-warning">info</i>
                    Current accounts balance
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-warning card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">trending_up</i>
                </div>
                <p class="card-category">Current Avg Balances</p>
                <h3 class="card-title">BDT @Model.CA_AVG_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-warning">info</i>
                    Average current balance
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-warning card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">hourglass_empty</i>
                </div>
                <p class="card-category">Total Dormant Accounts</p>
                <h3 class="card-title">@Model.NO_OF_DORMANT_ACCOUNTS.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-warning">info</i>
                    Dormant accounts count
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Savings Account Stats Row -->
<div class="row">
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">savings</i>
                </div>
                <p class="card-category">Total Savings Accounts</p>
                <h3 class="card-title">@Model.SA_AC.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-info">info</i>
                    Savings accounts count
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_balance</i>
                </div>
                <p class="card-category">Total Savings Balances</p>
                <h3 class="card-title">BDT @Model.SA_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-info">info</i>
                    Savings accounts balance
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">trending_up</i>
                </div>
                <p class="card-category">Savings Avg Balances</p>
                <h3 class="card-title">BDT @Model.SA_AVG_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-info">info</i>
                    Average savings balance
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">hourglass_empty</i>
                </div>
                <p class="card-category">Total Dormant Balances</p>
                <h3 class="card-title">BDT @Model.BALANCE_OF_DORMANT_ACCOUNTS.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-info">info</i>
                    Dormant accounts balance
                </div>
            </div>
        </div>
    </div>
</div>

<!-- FDR Account Stats Row -->
<div class="row">
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-success card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_balance</i>
                </div>
                <p class="card-category">Total FDR Accounts</p>
                <h3 class="card-title">@Model.FDR_AC.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-success">info</i>
                    FDR accounts count
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-success card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_balance</i>
                </div>
                <p class="card-category">Total FDR Balances</p>
                <h3 class="card-title">BDT @Model.FDR_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-success">info</i>
                    FDR accounts balance
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-success card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">trending_up</i>
                </div>
                <p class="card-category">FDR Avg Balances</p>
                <h3 class="card-title">BDT @Model.FDR_AVG_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-success">info</i>
                    Average FDR balance
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-success card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">hourglass_empty</i>
                </div>
                <p class="card-category">Total Zero Balance Accounts</p>
                <h3 class="card-title">@Model.NO_OF_ZERO_ACCOUNTS.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-success">info</i>
                    Zero balance accounts
                </div>
            </div>
        </div>
    </div>
</div>

<!-- DPS Account Stats Row -->
<div class="row">
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-rose card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">savings</i>
                </div>
                <p class="card-category">Total DPS Accounts</p>
                <h3 class="card-title">@Model.DPS_AC.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-rose">info</i>
                    DPS accounts count
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-rose card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_balance</i>
                </div>
                <p class="card-category">Total DPS Balances</p>
                <h3 class="card-title">BDT @Model.DPS_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-rose">info</i>
                    DPS accounts balance
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-rose card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">trending_up</i>
                </div>
                <p class="card-category">DPS Avg Balances</p>
                <h3 class="card-title">BDT @Model.DPS_AVG_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-rose">info</i>
                    Average DPS balance
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-rose card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">money_off</i>
                </div>
                <p class="card-category">Total balances of Zero Accounts</p>
                <h3 class="card-title">BDT @Model.BALANCE_OF_ZERO_ACCOUNTS.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-rose">info</i>
                    Zero balance total
                </div>
            </div>
        </div>
    </div>
</div>
