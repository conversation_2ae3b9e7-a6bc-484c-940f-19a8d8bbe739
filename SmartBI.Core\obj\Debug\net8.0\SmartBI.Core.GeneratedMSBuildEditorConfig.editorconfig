is_global = true
build_property.TargetFramework = net8.0
build_property.TargetFramework = net8.0
build_property.TargetPlatformMinVersion = 
build_property.TargetPlatformMinVersion = 
build_property.UsingMicrosoftNETSdkWeb = true
build_property.UsingMicrosoftNETSdkWeb = true
build_property.ProjectTypeGuids = 
build_property.ProjectTypeGuids = 
build_property.InvariantGlobalization = 
build_property.InvariantGlobalization = 
build_property.PlatformNeutralAssembly = 
build_property.PlatformNeutralAssembly = 
build_property.EnforceExtendedAnalyzerRules = 
build_property.EnforceExtendedAnalyzerRules = 
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property._SupportedPlatformList = Linux,macOS,Windows
build_property.RootNamespace = SmartBI.Core
build_property.RootNamespace = SmartBI.Core
build_property.ProjectDir = e:\Files From IP 45\Development\RoboAdvisory\Migration\Working\SmartBI.Core\
build_property.EnableComHosting = 
build_property.EnableGeneratedComInterfaceComImportInterop = 
build_property.RazorLangVersion = 8.0
build_property.SupportLocalizedComponentNames = 
build_property.GenerateRazorMetadataSourceChecksumAttributes = true
build_property.MSBuildProjectDirectory = e:\Files From IP 45\Development\RoboAdvisory\Migration\Working\SmartBI.Core
build_property._RazorSourceGeneratorDebug = 
build_property.EffectiveAnalysisLevelStyle = 8.0
build_property.EnableCodeStyleSeverity = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/AccessDenied.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxBY2Nlc3NEZW5pZWQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/AddOrEditRole.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxBZGRPckVkaXRSb2xlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/AddOrEditUser.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxBZGRPckVkaXRVc2VyLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/ChangePassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxDaGFuZ2VQYXNzd29yZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/ChangeUserPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxDaGFuZ2VVc2VyUGFzc3dvcmQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/DeleteRole.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxEZWxldGVSb2xlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/DeleteUser.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxEZWxldGVVc2VyLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/ForgotPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxGb3Jnb3RQYXNzd29yZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/ForgotPasswordConfirmation.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxGb3Jnb3RQYXNzd29yZENvbmZpcm1hdGlvbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/Login.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxMb2dpbi5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/ReportPrivilege.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxSZXBvcnRQcml2aWxlZ2UuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/ResetPassword.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxSZXNldFBhc3N3b3JkLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/Role.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxSb2xlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/RolePrivilege.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxSb2xlUHJpdmlsZWdlLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/TestToast.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxUZXN0VG9hc3QuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/UnauthUsers.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxVbmF1dGhVc2Vycy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/UserCompanyAccess.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxVc2VyQ29tcGFueUFjY2Vzcy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Account/Users.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcQWNjb3VudFxVc2Vycy5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Dashboard/CUARM.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGFzaGJvYXJkXENVQVJNLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Dashboard/RMDashboard.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcRGFzaGJvYXJkXFJNRGFzaGJvYXJkLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Home/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxJbmRleC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Home/TestJQuery.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxUZXN0SlF1ZXJ5LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Home/TestReturnUrl.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcSG9tZVxUZXN0UmV0dXJuVXJsLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Report/ApiTest.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmVwb3J0XEFwaVRlc3QuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Report/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmVwb3J0XEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Report/TestFlatpickr.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmVwb3J0XFRlc3RGbGF0cGlja3IuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Report/Viewer.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmVwb3J0XFZpZXdlci5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Report/ViewReport.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmVwb3J0XFZpZXdSZXBvcnQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Report/_Param.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmVwb3J0XF9QYXJhbS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Retail/AuthorizeRMTarget.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmV0YWlsXEF1dGhvcml6ZVJNVGFyZ2V0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Retail/AuthorizeTarget.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmV0YWlsXEF1dGhvcml6ZVRhcmdldC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Retail/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmV0YWlsXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Retail/TestUpload.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmV0YWlsXFRlc3RVcGxvYWQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Retail/UploadTarget.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcUmV0YWlsXFVwbG9hZFRhcmdldC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/AccessDenied.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEFjY2Vzc0RlbmllZC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/Error.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXEVycm9yLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/NotFound.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXE5vdEZvdW5kLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/_Layout.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/_Layout.fixed.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXQuZml4ZWQuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/_Navbar.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9OYXZiYXIuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/_Sidebar.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9TaWRlYmFyLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/_SidebarMenu.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9TaWRlYmFyTWVudS5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/_Topbar.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9Ub3BiYXIuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/_ValidationScriptsPartial.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9WYWxpZGF0aW9uU2NyaXB0c1BhcnRpYWwuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Test/FlatpickrTest.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVGVzdFxGbGF0cGlja3JUZXN0LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/TradeOps/AddOrEditBill.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVHJhZGVPcHNcQWRkT3JFZGl0QmlsbC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/TradeOps/AuthorizeBills.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVHJhZGVPcHNcQXV0aG9yaXplQmlsbHMuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/TradeOps/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVHJhZGVPcHNcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/TradeOps/Reporting.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVHJhZGVPcHNcUmVwb3J0aW5nLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/TradeOps/ViewBillDetails.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVHJhZGVPcHNcVmlld0JpbGxEZXRhaWxzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Upload/AuthorizeUploadFile.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXBsb2FkXEF1dGhvcml6ZVVwbG9hZEZpbGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Upload/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXBsb2FkXEluZGV4LmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Upload/UploadFile.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXBsb2FkXFVwbG9hZEZpbGUuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Upload/UploadHistory.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXBsb2FkXFVwbG9hZEhpc3RvcnkuY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/UserProfile/Index.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcVXNlclByb2ZpbGVcSW5kZXguY3NodG1s
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/_ViewImports.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdJbXBvcnRzLmNzaHRtbA==
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/_ViewStart.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcX1ZpZXdTdGFydC5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = 

[e:/Files From IP 45/Development/RoboAdvisory/Migration/Working/SmartBI.Core/Views/Shared/_Layout_o.cshtml]
build_metadata.AdditionalFiles.TargetPath = Vmlld3NcU2hhcmVkXF9MYXlvdXRfby5jc2h0bWw=
build_metadata.AdditionalFiles.CssScope = b-ndi8eis16q
