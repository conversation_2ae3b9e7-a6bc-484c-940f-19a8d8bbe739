/* Global Styles */
body {
    font-family: 'Roboto', sans-serif;
    background-color: #f5f5f5;
}

/* Content Area */
.content {
    margin-top: 20px;
    padding: 30px 15px;
    min-height: calc(100vh - 123px);
}

/* Card Styles */
.card {
    margin-bottom: 30px;
    border-radius: 6px;
    color: rgba(0,0,0,.87);
    background: #fff;
    box-shadow: 0 2px 2px 0 rgba(0,0,0,.14), 0 3px 1px -2px rgba(0,0,0,.2), 0 1px 5px 0 rgba(0,0,0,.12);
}

.card-header {
    padding: 15px;
    margin: -20px 15px 0;
    border-radius: 3px;
    box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(233,30,99,.4);
}

.card-header.card-header-rose {
    background: linear-gradient(60deg,#ec407a,#d81b60);
    box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(233,30,99,.4);
}

.card-header.card-header-warning {
    background: linear-gradient(60deg,#ffa726,#fb8c00);
    box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(255,152,0,.4);
}

.card-header.card-header-info {
    background: linear-gradient(60deg,#26c6da,#00acc1);
    box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(0,188,212,.4);
}

.card-header.card-header-success {
    background: linear-gradient(60deg,#66bb6a,#43a047);
    box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(76,175,80,.4);
}

.card-title {
    color: #fff;
    margin-top: 0;
    margin-bottom: 5px;
    font-size: 1.825em;
    font-weight: 300;
}

.card-category {
    color: rgba(255,255,255,.8);
    margin: 0;
    font-size: 14px;
}

/* Stats Cards */
.card-stats .card-header.card-header-icon {
    text-align: right;
    padding: 15px;
}

.card-stats .card-header.card-header-icon i {
    font-size: 36px;
    line-height: 56px;
    width: 56px;
    height: 56px;
    text-align: center;
}

.card-stats .card-header.card-header-icon .card-category {
    margin-top: 10px;
    margin-bottom: 0;
    color: #999;
}

.card-stats .card-header.card-header-icon .card-title {
    margin: 0;
    color: #3c4858;
    font-size: 1.825em;
    font-weight: 300;
    line-height: 1.4em;
}

.card-stats .card-footer {
    margin-top: 20px;
    border-top: 1px solid #eee;
}

.card-stats .card-footer .stats {
    color: #999;
    font-size: 12px;
    line-height: 22px;
}

.card-stats .card-footer .stats .material-icons {
    position: relative;
    top: 4px;
    font-size: 16px;
}

/* Card Icons */
.card-icon {
    border-radius: 3px;
    background-color: #999;
    padding: 15px;
    margin-top: -20px;
    margin-right: 15px;
    float: left;
}

.card-icon i {
    font-size: 36px;
    line-height: 56px;
    width: 56px;
    height: 56px;
    text-align: center;
    color: #fff;
}

/* Card Colors */
.text-warning {
    color: #ff9800 !important;
}

.text-info {
    color: #00bcd4 !important;
}

.text-success {
    color: #4caf50 !important;
}

.text-rose {
    color: #e91e63 !important;
}

/* Responsive Adjustments */
@media (max-width: 991px) {
    .card-stats [class*="col-"] .card {
        margin-bottom: 30px;
    }
}

@media (max-width: 767px) {
    .card-stats .card-header.card-header-icon {
        text-align: center;
    }
    
    .card-stats .card-header.card-header-icon i {
        float: none;
        margin-top: 0;
        margin-right: 0;
    }
    
    .card-stats .card-header.card-header-icon .card-category,
    .card-stats .card-header.card-header-icon .card-title {
        text-align: center;
    }
}

/* Loading States */
.spinner-border {
    display: inline-block;
    width: 2rem;
    height: 2rem;
    vertical-align: text-bottom;
    border: .25em solid currentColor;
    border-right-color: transparent;
    border-radius: 50%;
    animation: spinner-border .75s linear infinite;
}

@keyframes spinner-border {
    to { transform: rotate(360deg); }
}

/* Alert Messages */
.alert {
    position: relative;
    padding: .75rem 1.25rem;
    margin-bottom: 1rem;
    border: 0;
    border-radius: .25rem;
}

.alert.alert-success {
    background-color: #dff0d8;
    color: #3c763d;
}

.alert.alert-danger {
    background-color: #f2dede;
    color: #a94442;
}

/* Row Spacing */
.row {
    margin-bottom: 30px;
}

.row:last-child {
    margin-bottom: 0;
}

/* Navigation Pills */
.nav-pills {
    border: 0;
    border-radius: 3px;
    padding: 0 15px;
}

.nav-pills .nav-item .nav-link {
    line-height: 24px;
    text-transform: uppercase;
    font-size: 12px;
    font-weight: 500;
    min-width: 100px;
    text-align: center;
    color: #555;
    transition: all .3s;
    border-radius: 30px;
    padding: 10px 15px;
}

.nav-pills .nav-item .nav-link:hover {
    background-color: rgba(200,200,200,.2);
}

.nav-pills .nav-item .nav-link.active {
    color: #fff;
    background-color: #e91e63;
    box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(233,30,99,.4);
}

/* Responsive Adjustments */
@media (max-width: 767px) {
    .nav-pills {
        padding: 0;
    }
    
    .nav-pills .nav-item .nav-link {
        min-width: auto;
    }
} 