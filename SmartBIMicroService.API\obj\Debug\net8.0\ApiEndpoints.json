[{"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_5", "RelativePath": "", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "ApproveUser", "RelativePath": "api/Account/approve-user/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "Authenticate", "RelativePath": "api/Account/authenticate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBI.Data.ViewModels.UserAuthenticationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetBranchInfo", "RelativePath": "api/Account/branch/{branchCode}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "branchCode", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetBranches", "RelativePath": "api/Account/branches", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "ChangePassword", "RelativePath": "api/Account/changepassword", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBIMicroService.API.Controllers.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetUserCompanies", "RelativePath": "api/Account/companies/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetUserInfo", "RelativePath": "api/Account/info/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetMenuTree", "RelativePath": "api/Account/menu-tree", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "RejectUser", "RelativePath": "api/Account/reject-user/{userId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "UpdateReportPrivileges", "RelativePath": "api/Account/report-privileges", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBI.Data.ViewModels.UpdateReportPrivilegeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetReportTreeByRoleId", "RelativePath": "api/Account/report-tree/by-role/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SmartBI.Data.ViewModels.TreeViewNode, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetReportTreeByUsername", "RelativePath": "api/Account/report-tree/by-user/{username}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SmartBI.Data.ViewModels.TreeViewNode, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetReportTreeStructure", "RelativePath": "api/Account/report-tree/structure", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SmartBI.Data.ViewModels.TreeViewNode, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "UpdateRolePrivileges", "RelativePath": "api/Account/role-privileges", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBI.Data.ViewModels.UpdateRolePrivilegeRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetRolePrivileges", "RelativePath": "api/Account/role-privileges/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetAllRoles", "RelativePath": "api/Account/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "CreateRole", "RelativePath": "api/Account/roles", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "role", "Type": "SmartBI.Data.Models.Role", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetRoleById", "RelativePath": "api/Account/roles/{id}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "UpdateRole", "RelativePath": "api/Account/roles/{id}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}, {"Name": "role", "Type": "SmartBI.Data.Models.Role", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "DeleteRole", "RelativePath": "api/Account/roles/{id}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "id", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetUnapprovedUsers", "RelativePath": "api/Account/unapproved-users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "UpdateUserCompanyAccess", "RelativePath": "api/Account/user-company-access", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBI.Data.ViewModels.UpdateUserCompanyAccessRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetUserCompanyAccess", "RelativePath": "api/Account/user-company-access/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetUserRoles", "RelativePath": "api/Account/user-roles/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetUserProfile", "RelativePath": "api/Account/userprofile/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetUsers", "RelativePath": "api/Account/users", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "CreateUser", "RelativePath": "api/Account/users", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBI.Data.ViewModels.UserCreateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetUser", "RelativePath": "api/Account/users/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "UpdateUser", "RelativePath": "api/Account/users/{userId}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SmartBI.Data.ViewModels.UserUpdateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "DeleteUser", "RelativePath": "api/Account/users/{userId}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "GetAllUsers", "RelativePath": "api/Account/users/all", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.AccountController", "Method": "ValidateUser", "RelativePath": "api/Account/validate/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetAccountStatistics", "RelativePath": "api/Compliance/account-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetAML", "RelativePath": "api/Compliance/aml", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetCustomerKYCUpdateStatus", "RelativePath": "api/Compliance/customer-kyc-update-status", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetFATCA", "RelativePath": "api/Compliance/fatca", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetFCCMAlert", "RelativePath": "api/Compliance/fccm-alert", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.Dashboard.FCCM_ALERT_DASHBOARD", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetKYCOverdueHigh", "RelativePath": "api/Compliance/kyc-overdue-high", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetKYCOverdueLow", "RelativePath": "api/Compliance/kyc-overdue-low", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetKYCOverdueSAFHigh", "RelativePath": "api/Compliance/kyc-overdue-saf-high", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetKYCOverdueSAFLow", "RelativePath": "api/Compliance/kyc-overdue-saf-low", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetNGONPO", "RelativePath": "api/Compliance/ngo-npo", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetPEPIP", "RelativePath": "api/Compliance/pep-ip", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetStandaloneFDR", "RelativePath": "api/Compliance/standalone-fdr", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ComplianceController", "Method": "GetStandaloneFDRSummary", "RelativePath": "api/Compliance/standalone-fdr-summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Data.DataTable", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.DashboardController", "Method": "GetDashboardUserSummary", "RelativePath": "api/Dashboard/user-summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.FileUploadController", "Method": "AuthorizeUpload", "RelativePath": "api/FileUpload/authorize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBIMicroService.API.Controllers.FileUploadAuthorizationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.FileUploadResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.FileUploadController", "Method": "GetFileUploadHistory", "RelativePath": "api/FileUpload/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "FileName", "Type": "System.String", "IsRequired": false}, {"Name": "UploadedBy", "Type": "System.String", "IsRequired": false}, {"Name": "DataType", "Type": "System.String", "IsRequired": false}, {"Name": "FromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ToDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SupervisionStatus", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IsDataProcessed", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "PageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}, {"Name": "SortBy", "Type": "System.String", "IsRequired": false}, {"Name": "SortDirection", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.PaginatedResult`1[[SmartBI.Data.ViewModels.FileUploadHistoryViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.FileUploadController", "Method": "GetPendingUploads", "RelativePath": "api/FileUpload/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "dataType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[SmartBI.Data.ViewModels.FileUploadAuthorizationViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.FileUploadController", "Method": "GetStatistics", "RelativePath": "api/FileUpload/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "username", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.FileUploadStatistics", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.FileUploadController", "Method": "UploadFile", "RelativePath": "api/FileUpload/upload", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "file", "Type": "Microsoft.AspNetCore.Http.IFormFile", "IsRequired": false}, {"Name": "dataType", "Type": "System.String", "IsRequired": false}, {"Name": "uploadedBy", "Type": "System.String", "IsRequired": false}, {"Name": "description", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.FileUploadResponse", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.FileUploadController", "Method": "ValidateUpload", "RelativePath": "api/FileUpload/validate", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": false}, {"Name": "fileSize", "Type": "System.Int64", "IsRequired": false}, {"Name": "dataType", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.FileUploadValidationResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_8", "RelativePath": "api/health", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.LovController", "Method": "GetLovItems", "RelativePath": "api/Lov/{entityType}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "entityType", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "filter", "Type": "System.String", "IsRequired": false}, {"Name": "includeInactive", "Type": "System.Boolean", "IsRequired": false}, {"Name": "includeAllOption", "Type": "System.Boolean", "IsRequired": false}, {"Name": "pageNumber", "Type": "System.Int32", "IsRequired": false}, {"Name": "pageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.LovController", "Method": "GetBranches", "RelativePath": "api/Lov/branches", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}, {"Name": "includeAllOption", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.LovController", "Method": "GetDepartments", "RelativePath": "api/Lov/departments", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeAllOption", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.LovController", "Method": "GetSupportedEntityTypes", "RelativePath": "api/Lov/entity-types", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.LovController", "Method": "GetRoles", "RelativePath": "api/Lov/roles", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "includeAllOption", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.MenuController", "Method": "GetAllMainMenus", "RelativePath": "api/Menu/main-menus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.MenuController", "Method": "GetAllPrivileges", "RelativePath": "api/Menu/privileges", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.MenuController", "Method": "GetPrivilegesByRoleId", "RelativePath": "api/Menu/privileges/by-role/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.MenuController", "Method": "GetAllSubMenus", "RelativePath": "api/Menu/sub-menus", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.MenuController", "Method": "GetSubMenusByMainMenuId", "RelativePath": "api/Menu/sub-menus/by-main-menu/{mainMenuId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "mainMenuId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[System.Object, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.MenuController", "Method": "GetUserMenuItemsByRoleId", "RelativePath": "api/Menu/user-menu-items/{roleId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "roleId", "Type": "System.Int32", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.IEnumerable`1[[SmartBI.Data.ViewModels.MenuItemViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.RelationshipManagerController", "Method": "Health", "RelativePath": "api/RelationshipManager/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.RelationshipManagerController", "Method": "GetRMAttritionGrowthSummary", "RelativePath": "api/RelationshipManager/rm-attrition-growth-summary", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[SmartBI.Data.ViewModels.RM_ATTRITION_GROWTH_SUMMARY, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.RelationshipManagerController", "Method": "GetRMDashboard", "RelativePath": "api/RelationshipManager/rm-dashboard", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.RM_PERFORMANCE_DASHBOARD", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.RelationshipManagerController", "Method": "GetRMGrowth", "RelativePath": "api/RelationshipManager/rm-growth", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[SmartBI.Data.ViewModels.RM_PERFORMANCE_GROWTH, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.RelationshipManagerController", "Method": "GetRMTargetVsAchievement", "RelativePath": "api/RelationshipManager/rm-target-vs-achievement", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.RM_TARGET_VS_ACHIEVEMENT", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.ReportController", "Method": "GetReportData", "RelativePath": "api/report/data/{reportId}", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "reportId", "Type": "System.String", "IsRequired": true}, {"Name": "parameters", "Type": "System.Collections.Generic.Dictionary`2[[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e],[System.String, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.ReportController", "Method": "GenerateReport", "RelativePath": "api/report/generate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBIMicroService.API.Models.ReportRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.ReportController", "Method": "GetParameterControls", "RelativePath": "api/report/param-controls/{Id}/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "Id", "Type": "System.String", "IsRequired": true}, {"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.ReportController", "Method": "GetReportPath", "RelativePath": "api/report/path/{reportId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "reportId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.ReportController", "Method": "GetReportTree", "RelativePath": "api/report/tree/{userName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.ReportsController", "Method": "GetAvailableReports", "RelativePath": "api/Reports/available/{userId}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "userId", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.ReportsController", "Method": "ExportReport", "RelativePath": "api/Reports/export/{format}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "format", "Type": "System.String", "IsRequired": true}, {"Name": "cache<PERSON>ey", "Type": "System.String", "IsRequired": false}, {"Name": "userId", "Type": "System.String", "IsRequired": false}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.ReportsController", "Method": "GenerateReport", "RelativePath": "api/Reports/generate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBI.Data.ViewModels.ReportGenerationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.ReportsController", "Method": "HealthCheck", "RelativePath": "api/Reports/health", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetAuthorizationStatistics", "RelativePath": "api/TradeOps/authorization-statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.TradeOpsAuthorizationStatisticsViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetTradeOverdueExportBills", "RelativePath": "api/TradeOps/bills", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "EXP_NO", "Type": "System.String", "IsRequired": false}, {"Name": "REASON_ID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LEGAL_ACTION_INITIATOR_ID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "INPUTTER", "Type": "System.String", "IsRequired": false}, {"Name": "FromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ToDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SUPERVISIONSTATUS", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ISREPORTED", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.PaginatedResult`1[[SmartBI.Data.ViewModels.TradeOverdueExportBillViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "CreateTradeOverdueExportBill", "RelativePath": "api/TradeOps/bills", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBI.Data.ViewModels.TradeOverdueExportBillCreateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetTradeOverdueExportBill", "RelativePath": "api/TradeOps/bills/{expNo}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "expNo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.TradeOverdueExportBillViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "UpdateTradeOverdueExportBill", "RelativePath": "api/TradeOps/bills/{expNo}", "HttpMethod": "PUT", "IsController": true, "Order": 0, "Parameters": [{"Name": "expNo", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SmartBI.Data.ViewModels.TradeOverdueExportBillUpdateRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "DeleteTradeOverdueExportBill", "RelativePath": "api/TradeOps/bills/{expNo}", "HttpMethod": "DELETE", "IsController": true, "Order": 0, "Parameters": [{"Name": "expNo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "AuthorizeTradeOverdueExportBill", "RelativePath": "api/TradeOps/bills/{expNo}/authorize", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "expNo", "Type": "System.String", "IsRequired": true}, {"Name": "request", "Type": "SmartBI.Data.ViewModels.TradeOpsAuthorizationRequest", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "ExistsTradeOverdueExportBill", "RelativePath": "api/TradeOps/bills/{expNo}/exists", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "expNo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetAuthorizationHistory", "RelativePath": "api/TradeOps/bills/{expNo}/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "expNo", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[SmartBI.Data.ViewModels.TradeOpsAuthorizationHistoryViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetAllConsolidatedTradeOverdueExportBills", "RelativePath": "api/TradeOps/bills/consolidated", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "EXP_NO", "Type": "System.String", "IsRequired": false}, {"Name": "REASON_ID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LEGAL_ACTION_INITIATOR_ID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "INPUTTER", "Type": "System.String", "IsRequired": false}, {"Name": "FromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ToDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SUPERVISIONSTATUS", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ISREPORTED", "Type": "System.Nullable`1[[<PERSON><PERSON>Boolean, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "SearchTerm", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.PaginatedResult`1[[SmartBI.Data.ViewModels.TradeOverdueExportBillViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GenerateOverdueExportBill", "RelativePath": "api/TradeOps/bills/generate-legacy-xml", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBIMicroService.API.Controllers.LegacyXmlGenerationRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.String", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetPendingAuthorizationBills", "RelativePath": "api/TradeOps/bills/pending", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "REASON_ID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LEGAL_ACTION_INITIATOR_ID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "INPUTTER", "Type": "System.String", "IsRequired": false}, {"Name": "FromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ToDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[SmartBI.Data.ViewModels.TradeOverdueExportBillAuthorizationViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetBillsForReporting", "RelativePath": "api/TradeOps/bills/reporting", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "FromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ToDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "REASON_ID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "LEGAL_ACTION_INITIATOR_ID", "Type": "System.Nullable`1[[System.Int32, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "IncludeReported", "Type": "System.Boolean", "IsRequired": false}], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[SmartBI.Data.ViewModels.TradeOverdueExportBillReportingViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "DownloadExportFile", "RelativePath": "api/TradeOps/download/{fileName}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fileName", "Type": "System.String", "IsRequired": true}], "ReturnTypes": []}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "ExportBills", "RelativePath": "api/TradeOps/export", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBI.Data.ViewModels.TradeOpsExportRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.TradeOpsExportResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetExportHistory", "RelativePath": "api/TradeOps/export/history", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "FromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ToDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "ExportedBy", "Type": "System.String", "IsRequired": false}, {"Name": "Status", "Type": "System.String", "IsRequired": false}, {"Name": "Page", "Type": "System.Int32", "IsRequired": false}, {"Name": "PageSize", "Type": "System.Int32", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.PaginatedResult`1[[SmartBI.Data.ViewModels.TradeOpsExportHistoryViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetLegalActionInitiatorsSelectList", "RelativePath": "api/TradeOps/initiators", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[SmartBI.Data.ViewModels.SelectListItemViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetOverdueReasonsSelectList", "RelativePath": "api/TradeOps/reasons", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Collections.Generic.List`1[[SmartBI.Data.ViewModels.SelectListItemViewModel, SmartBI.Data, Version=*******, Culture=neutral, PublicKeyToken=null]]", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "GetTradeOpsStatistics", "RelativePath": "api/TradeOps/statistics", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "fromDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}, {"Name": "toDate", "Type": "System.Nullable`1[[System.DateTime, System.Private.CoreLib, Version=*******, Culture=neutral, PublicKeyToken=7cec85d7bea7798e]]", "IsRequired": false}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.TradeOpsStatisticsViewModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "ValidateTradeOverdueExportBill", "RelativePath": "api/TradeOps/validate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBI.Data.ViewModels.TradeOverdueExportBillCreateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.TradeOpsValidationResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.TradeOpsController", "Method": "ValidateAuthorizationRequest", "RelativePath": "api/TradeOps/validate-authorization", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "System.Object", "IsRequired": true}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.TradeOpsValidationResult", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.UserController", "Method": "GetUserDetails", "RelativePath": "api/User/authenticate", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBIMicroService.API.Controllers.AuthenticateRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.LoginModels", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.UserController", "Method": "GetUserByEmail", "RelativePath": "api/User/by-email/{email}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "email", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.UserModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.UserController", "Method": "GetUserByUsername", "RelativePath": "api/User/by-username/{username}", "HttpMethod": "GET", "IsController": true, "Order": 0, "Parameters": [{"Name": "username", "Type": "System.String", "IsRequired": true}], "ReturnTypes": [{"Type": "SmartBI.Data.ViewModels.UserModel", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.UserController", "Method": "ChangePassword", "RelativePath": "api/User/change-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBIMicroService.API.Controllers.ChangePasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "SmartBIMicroService.API.Controllers.UserController", "Method": "ResetPassword", "RelativePath": "api/User/reset-password", "HttpMethod": "POST", "IsController": true, "Order": 0, "Parameters": [{"Name": "request", "Type": "SmartBIMicroService.API.Controllers.ResetPasswordRequest", "IsRequired": true}], "ReturnTypes": [{"Type": "System.Boolean", "MediaTypes": ["text/plain", "application/json", "text/json"], "StatusCode": 200}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_6", "RelativePath": "index.html", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}, {"ContainingType": "Program+<>c", "Method": "<<Main>$>b__0_7", "RelativePath": "Views/{**path}", "HttpMethod": "GET", "IsController": false, "Order": 0, "Parameters": [], "ReturnTypes": [{"Type": "System.Void", "MediaTypes": [], "StatusCode": 200}]}]