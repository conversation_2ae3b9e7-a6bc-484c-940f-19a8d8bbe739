namespace SmartBI.Core.Models.DashboardModels
{
    public class PerformanceAttritionViewModel
    {
        public int TOTAL_ACCOUNTS_PREVIOUS { get; set; }
        public int TOTAL_ACCOUNTS_CURRENT { get; set; }
        public int NEW_ACCOUNTS { get; set; }
        public int CLOSED_ACCOUNTS { get; set; }
        public decimal NET_GROWTH { get; set; }
        public decimal ATTRITION_RATE { get; set; }
        public decimal RETENTION_RATE { get; set; }
        public List<MonthlyAttrition> MonthlyData { get; set; } = new();
    }

    public class MonthlyAttrition
    {
        public string Month { get; set; }
        public int NewAccounts { get; set; }
        public int ClosedAccounts { get; set; }
        public decimal NetGrowth { get; set; }
        public decimal AttritionRate { get; set; }
    }
} 