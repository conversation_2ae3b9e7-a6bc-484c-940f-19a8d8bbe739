@{
    ViewData["Title"] = "RM Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- BACKUP OF COMPLEX IMPLEMENTATION - Created on 2025-01-14 -->
<!-- This is a backup of the original complex RMDashboard implementation -->
<!-- The original file was 1614 lines with complex caching, fallback data, and multiple functions -->
<!-- Backed up before rewriting with simple AJAX approach -->

@* 
BACKUP NOTES:
- Original implementation had complex JavaScript object with multiple methods
- Used caching system with local and server-side cache
- Had fallback sample data for when API fails
- Complex buildPerformanceHTML, buildAttritionHTML, buildTargetsHTML functions
- Multiple AJAX calls with Promise.all for attrition data
- DataTables initialization with complex configuration
- Material Dashboard Pro styling with responsive CSS
- Font size fixes and card height consistency
- Smart fallback data solution for all three tabs

FEATURES THAT WERE WORKING:
- Performance & Attrition tab with card-based dashboard
- Performance & Attrition tab with growth/attrition tables  
- Target vs Achievement tab with target/achievement metrics
- Proper number formatting and percentage display
- Responsive design and Material Dashboard Pro compliance
- Error handling and loading indicators
- Cache management and refresh functionality

The file was too large (1614 lines) to backup completely in one operation.
Original file location: SmartBI.Core/Views/Dashboard/RMDashboard.cshtml
*@

<!-- This backup file serves as reference for the complex implementation -->
<!-- The new simple implementation will replace the original file -->
