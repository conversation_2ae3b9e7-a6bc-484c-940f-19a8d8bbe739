@model SmartBI.Core.Models.DashboardModels.TargetAchievementViewModel

<div class="row" id="targetMetrics">
    <div class="col-lg-4 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">flag</i>
                </div>
                <p class="card-category">Total Target</p>
                <h3 class="card-title">BDT @Model.TOTAL_TARGET.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-info">flag</i>
                    Overall target amount
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-success card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">done_all</i>
                </div>
                <p class="card-category">Total Achievement</p>
                <h3 class="card-title">BDT @Model.TOTAL_ACHIEVEMENT.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-success">done_all</i>
                    Total achieved amount
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-4 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-warning card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">trending_up</i>
                </div>
                <p class="card-category">Achievement Rate</p>
                <h3 class="card-title">@Model.ACHIEVEMENT_PERCENTAGE.ToString("N2")%</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-warning">info</i>
                    Overall achievement rate
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Product-wise Achievement -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header card-header-primary">
                <h4 class="card-title">Product-wise Achievement</h4>
                <p class="card-category">Achievement breakdown by product</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead class="text-primary">
                            <tr>
                                <th>Product</th>
                                <th>Target</th>
                                <th>Achievement</th>
                                <th>Achievement %</th>
                                <th>Progress</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var product in Model.ProductTargets)
                            {
                                <tr>
                                    <td>@product.ProductName</td>
                                    <td>BDT @product.Target.ToString("N2")</td>
                                    <td>BDT @product.Achievement.ToString("N2")</td>
                                    <td>@product.AchievementPercentage.ToString("N2")%</td>
                                    <td>
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar @(product.AchievementPercentage >= 100 ? "bg-success" : "bg-warning")"
                                                 role="progressbar" style="width: @(Math.Min(product.AchievementPercentage, 100))%"
                                                 aria-valuenow="@product.AchievementPercentage" aria-valuemin="0" aria-valuemax="100">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Monthly Achievement Chart -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header card-header-success">
                <h4 class="card-title">Monthly Achievement Trend</h4>
                <p class="card-category">Monthly target vs achievement analysis</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead class="text-success">
                            <tr>
                                <th>Month</th>
                                <th>Target</th>
                                <th>Achievement</th>
                                <th>Achievement %</th>
                                <th>Progress</th>
                            </tr>
                        </thead>
                        <tbody>
                            @foreach (var month in Model.MonthlyTargets)
                            {
                                <tr>
                                    <td>@month.Month</td>
                                    <td>BDT @month.Target.ToString("N2")</td>
                                    <td>BDT @month.Achievement.ToString("N2")</td>
                                    <td>@month.AchievementPercentage.ToString("N2")%</td>
                                    <td>
                                        <div class="progress" style="height: 5px;">
                                            <div class="progress-bar @(month.AchievementPercentage >= 100 ? "bg-success" : "bg-warning")"
                                                 role="progressbar" style="width: @(Math.Min(month.AchievementPercentage, 100))%"
                                                 aria-valuenow="@month.AchievementPercentage" aria-valuemin="0" aria-valuemax="100">
                                            </div>
                                        </div>
                                    </td>
                                </tr>
                            }
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div> 