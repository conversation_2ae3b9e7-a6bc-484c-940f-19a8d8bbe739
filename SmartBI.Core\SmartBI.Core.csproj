﻿<Project Sdk="Microsoft.NET.Sdk.Web">

  <PropertyGroup>
    <TargetFramework>net8.0</TargetFramework>
    <Nullable>disable</Nullable>
    <ImplicitUsings>enable</ImplicitUsings>

    <!-- PERMANENT FIX: Comprehensive Static Web Assets Configuration -->
    <CompressionEnabled>false</CompressionEnabled>
    <StaticWebAssetBasePath>/</StaticWebAssetBasePath>
    <GenerateStaticWebAssetsManifest>false</GenerateStaticWebAssetsManifest>
    <DisableStaticWebAssetsBuildPropsFileGeneration>true</DisableStaticWebAssetsBuildPropsFileGeneration>
    <StaticWebAssetsEnabled>true</StaticWebAssetsEnabled>

    <!-- Prevent duplicate content discovery -->
    <EnableDefaultContentItems>true</EnableDefaultContentItems>
    <EnableDefaultEmbeddedResourceItems>false</EnableDefaultEmbeddedResourceItems>

    <!-- Disable problematic features that cause hash conflicts -->
    <RunAnalyzersDuringBuild>false</RunAnalyzersDuringBuild>
    <RunCodeAnalysis>false</RunCodeAnalysis>
  </PropertyGroup>

  <ItemGroup>
    <Compile Remove="Models\ReferenceModels.cs" />
  </ItemGroup>



  <ItemGroup>
    <PackageReference Include="ClosedXML" Version="0.95.4" />
    <PackageReference Include="Dapper" Version="2.1.28" />
    <PackageReference Include="DocumentFormat.OpenXml" Version="2.20.0" />
    <PackageReference Include="EPPlus" Version="6.2.10" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.NewtonsoftJson" Version="8.0.0" />
    <PackageReference Include="Microsoft.AspNetCore.Mvc.Razor.RuntimeCompilation" Version="8.0.16" />

    <!-- Entity Framework removed - using API-only architecture -->
    <PackageReference Include="Microsoft.Extensions.Logging" Version="8.0.0" />
    <PackageReference Include="Microsoft.Extensions.Logging.Console" Version="8.0.0" />
    <PackageReference Include="Microsoft.VisualStudio.Web.CodeGeneration.Design" Version="8.0.0" />
    <PackageReference Include="ReportViewerCore.NETCore" Version="15.1.26" />
    <PackageReference Include="System.Data.SqlClient" Version="4.9.0" />

    <!-- SQL Client removed - using API-only architecture -->
    <PackageReference Include="System.Text.Encoding.CodePages" Version="9.0.5" />
  </ItemGroup>

  <ItemGroup>
    <ProjectReference Include="..\SmartBI.Data\SmartBI.Data.csproj" />
  </ItemGroup>

  <ItemGroup>
    <Folder Include="App_Browsers\" />
    <Folder Include="App_Data\" />
    <Folder Include="Extensions\" />
    <Folder Include="Logs\" />
    <Folder Include="Middleware\" />
    <Folder Include="Markdown\" />
    <Folder Include="ScriptsDataTable\" />
    <Folder Include="wwwroot\adminlte\" />
    <Folder Include="wwwroot\Content\" />
    <Folder Include="wwwroot\fonts\" />
    <Folder Include="wwwroot\Scripts\" />
  </ItemGroup>

  <!-- The following ItemGroup was causing NETSDK1022, removing it. -->
  <!--
  <ItemGroup>
    <Content Include="wwwroot\**">
      <CopyToOutputDirectory>PreserveNewest</CopyToOutputDirectory>
    </Content>
  </ItemGroup>
  -->

  <!-- PERMANENT FIX: Exclude all problematic folders and files to avoid static web asset conflicts -->
  <ItemGroup>
    <!-- Explicitly exclude Reports folder to avoid conflicts with microservice -->
    <Content Remove="wwwroot/Reports/**" />
    <None Include="wwwroot/Reports/**" />

    <!-- Exclude test files and documentation -->
    <Content Remove="TestPages/**" />
    <Content Remove="*.md" />

    <!-- CRITICAL: Remove ALL duplicate files that cause hash conflicts -->
    <!-- jQuery duplicates -->
    <Content Remove="wwwroot/lib/jquery/jquery.min.js" />
    <Content Remove="wwwroot/assets/js/core/jquery.min.js" />

    <!-- Bootstrap duplicates -->
    <Content Remove="wwwroot/lib/bootstrap/dist/css/bootstrap.css" />
    <Content Remove="wwwroot/lib/bootstrap/dist/css/bootstrap.css.map" />
    <Content Remove="wwwroot/lib/bootstrap/dist/css/bootstrap.min.css" />
    <Content Remove="wwwroot/lib/bootstrap/dist/css/bootstrap.min.css.map" />
    <Content Remove="wwwroot/assets/js/core/bootstrap-material-design.min.js" />

    <!-- Material Dashboard duplicates -->
    <Content Remove="wwwroot/css/material-dashboard.min.css" />
    <Content Remove="wwwroot/js/material-dashboard.min.js" />

    <!-- Perfect Scrollbar duplicates -->
    <Content Remove="wwwroot/js/perfect-scrollbar.min.js" />
    <Content Remove="wwwroot/js/plugins/perfect-scrollbar.jquery.min.js" />

    <!-- Other library duplicates -->
    <Content Remove="wwwroot/lib/signalr/signalr.min.js" />
    <Content Remove="wwwroot/lib/select2/select2.min.css" />
    <Content Remove="wwwroot/lib/select2/select2.min.js" />
    <Content Remove="wwwroot/assets/js/core/popper.min.js" />

    <!-- Exclude entire problematic directories -->
    <Content Remove="wwwroot/assets/js/core/**" />
    <Content Remove="wwwroot/js/core/**" />

    <!-- Exclude duplicate assets that exist in both /assets and root directories -->
    <Content Remove="wwwroot/assets/img/sidebar-*.jpg" />

    <!-- Exclude build artifacts and temp files -->
    <Content Remove="obj/**" />
    <Content Remove="bin/**" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="wwwroot\assets\img\sidebar-1.jpg" />
    <Content Include="wwwroot\assets\img\sidebar-2.jpg" />
    <Content Include="wwwroot\assets\img\sidebar-3.jpg" />
    <Content Include="wwwroot\assets\img\sidebar-4.jpg" />
  </ItemGroup>

</Project>
