using Microsoft.EntityFrameworkCore;
using Microsoft.Extensions.Logging;
using SmartBI.Data.Models;
using SmartBI.Data.ViewModels.Dashboard;
using SmartBI.Data.Repositories.Interfaces;
using System.Data;
using System.Data.SqlClient;
using Dapper;

namespace SmartBI.Data.Repositories
{
    /// <summary>
    /// Repository for Compliance dashboard operations
    /// Legacy SmartBI2 DashboardRepository compatible implementation
    /// </summary>
    public class ComplianceRepository : IComplianceRepository
    {
        private readonly SmartBIContext _context;
        private readonly ILogger<ComplianceRepository> _logger;
        private readonly string _connectionString;

        public ComplianceRepository(SmartBIContext context, ILogger<ComplianceRepository> logger)
        {
            _context = context ?? throw new ArgumentNullException(nameof(context));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _connectionString = _context.Database.GetConnectionString() ?? throw new InvalidOperationException("Connection string not found");
        }

        #region KYC Overdue APIs

        /// <summary>
        /// Legacy API: GET_DASHBOARD_KYC_OVERDUE - Gets KYC overdue data by risk level
        /// Executes: SP_GET_DASHBOARD_KYC_OVERDUE_[RISK_LEVEL]
        /// </summary>
        public async Task<DataTable> GET_DASHBOARD_KYC_OVERDUE(string riskLevel, string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_KYC_OVERDUE called for risk level: {RiskLevel}, user: {UserId}", riskLevel, userId);

                using var connection = new SqlConnection(_connectionString);
                var query = $"EXEC dbo.SP_GET_DASHBOARD_KYC_OVERDUE_{riskLevel?.ToUpper()} @UserId";
                
                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);
                
                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy GET_DASHBOARD_KYC_OVERDUE returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_KYC_OVERDUE for risk level: {RiskLevel}, user: {UserId}", riskLevel, userId);
                return new DataTable();
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_KYC_OVERDUE_SAF - Gets KYC overdue SAF data by risk level
        /// Executes: SP_GET_DASHBOARD_KYC_OVERDUE_SAF_[RISK_LEVEL]
        /// </summary>
        public async Task<DataTable> GET_DASHBOARD_KYC_OVERDUE_SAF(string riskLevel, string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_KYC_OVERDUE_SAF called for risk level: {RiskLevel}, user: {UserId}", riskLevel, userId);

                using var connection = new SqlConnection(_connectionString);
                var query = $"EXEC dbo.SP_GET_DASHBOARD_KYC_OVERDUE_SAF_{riskLevel?.ToUpper()} @UserId";
                
                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);
                
                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy GET_DASHBOARD_KYC_OVERDUE_SAF returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_KYC_OVERDUE_SAF for risk level: {RiskLevel}, user: {UserId}", riskLevel, userId);
                return new DataTable();
            }
        }

        #endregion

        #region Compliance Monitoring APIs

        /// <summary>
        /// Legacy API: GET_DASHBOARD_NGO_NPO - Gets NGO/NPO compliance data
        /// Executes: SP_GET_DASHBOARD_COMPLAINCE_NGO_NPO
        /// </summary>
        public async Task<DataTable> GET_DASHBOARD_NGO_NPO(string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_NGO_NPO called for user: {UserId}", userId);

                using var connection = new SqlConnection(_connectionString);
                var query = "EXEC dbo.SP_GET_DASHBOARD_COMPLAINCE_NGO_NPO @UserId";
                
                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);
                
                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy GET_DASHBOARD_NGO_NPO returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_NGO_NPO for user: {UserId}", userId);
                return new DataTable();
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_PEP_IP - Gets PEP/IP compliance data
        /// Executes: SP_GET_DASHBOARD_COMPLAINCE_PEP_DATA
        /// </summary>
        public async Task<DataTable> GET_DASHBOARD_PEP_IP(string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_PEP_IP called for user: {UserId}", userId);

                using var connection = new SqlConnection(_connectionString);
                var query = "EXEC dbo.SP_GET_DASHBOARD_COMPLAINCE_PEP_DATA @UserId";
                
                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);
                
                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy GET_DASHBOARD_PEP_IP returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_PEP_IP for user: {UserId}", userId);
                return new DataTable();
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_STAND_ALONE_FDR - Gets standalone FDR data
        /// Executes: SP_GET_DASHBOARD_COMPLAINCE_STANDALONE_FDR
        /// </summary>
        public async Task<DataTable> GET_DASHBOARD_STAND_ALONE_FDR(string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_STAND_ALONE_FDR called for user: {UserId}", userId);

                using var connection = new SqlConnection(_connectionString);
                var query = "EXEC dbo.SP_GET_DASHBOARD_COMPLAINCE_STANDALONE_FDR @UserId";
                
                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);
                
                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy GET_DASHBOARD_STAND_ALONE_FDR returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_STAND_ALONE_FDR for user: {UserId}", userId);
                return new DataTable();
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_FATCA - Gets FATCA compliance data
        /// Executes: SP_GET_DASHBOARD_COMPLAINCE_FATCA
        /// </summary>
        public async Task<DataTable> GET_DASHBOARD_FATCA(string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_FATCA called for user: {UserId}", userId);

                using var connection = new SqlConnection(_connectionString);
                var query = "EXEC dbo.SP_GET_DASHBOARD_COMPLAINCE_FATCA @UserId";
                
                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);
                
                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy GET_DASHBOARD_FATCA returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_FATCA for user: {UserId}", userId);
                return new DataTable();
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_AML - Gets AML compliance data
        /// Executes: SP_GET_DASHBOARD_COMPLAINCE_AML_DATA
        /// </summary>
        public async Task<DataTable> GET_DASHBOARD_AML(string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_AML called for user: {UserId}", userId);

                using var connection = new SqlConnection(_connectionString);
                var query = "EXEC dbo.SP_GET_DASHBOARD_COMPLAINCE_AML_DATA @UserId";
                
                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);
                
                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy GET_DASHBOARD_AML returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_AML for user: {UserId}", userId);
                return new DataTable();
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_FCCM_ALERT - Gets FCCM alert data
        /// Executes: SP_GET_DASHBOARD_FCCM_ALERT
        /// </summary>
        public async Task<FCCM_ALERT_DASHBOARD> GET_DASHBOARD_FCCM_ALERT(string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_FCCM_ALERT called for user: {UserId}", userId);

                using var connection = new SqlConnection(_connectionString);
                var query = "EXEC dbo.SP_GET_DASHBOARD_FCCM_ALERT @UserId";

                var result = await connection.QueryFirstOrDefaultAsync<FCCM_ALERT_DASHBOARD>(query, new { UserId = userId ?? string.Empty });

                _logger.LogInformation("Legacy GET_DASHBOARD_FCCM_ALERT completed for user: {UserId}", userId);
                return result ?? new FCCM_ALERT_DASHBOARD();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_FCCM_ALERT for user: {UserId}", userId);
                return new FCCM_ALERT_DASHBOARD();
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS - Gets customer KYC update status
        /// Executes: SP_GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS
        /// </summary>
        public async Task<DataTable> GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS(string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS called for user: {UserId}", userId);

                using var connection = new SqlConnection(_connectionString);
                var query = "EXEC dbo.SP_GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS @UserId";

                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);

                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS for user: {UserId}", userId);
                return new DataTable();
            }
        }

        /// <summary>
        /// Legacy API: GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS - Gets compliance account statistics
        /// Executes: SP_GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS
        /// Note: Legacy spelling "COMPLAINCE" maintained for compatibility
        /// </summary>
        public async Task<DataTable> GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS(string userId)
        {
            try
            {
                _logger.LogInformation("Legacy GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS called for user: {UserId}", userId);

                using var connection = new SqlConnection(_connectionString);
                var query = "EXEC dbo.SP_GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS @UserId";

                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);

                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS for user: {UserId}", userId);
                return new DataTable();
            }
        }

        /// <summary>
        /// Legacy API: SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY - Gets standalone FDR summary
        /// Executes: SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY
        /// </summary>
        public async Task<DataTable> SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY(string userId)
        {
            try
            {
                _logger.LogInformation("Legacy SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY called for user: {UserId}", userId);

                using var connection = new SqlConnection(_connectionString);
                var query = "EXEC dbo.SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY @UserId";

                var dataTable = new DataTable();
                using var command = new SqlCommand(query, connection);
                command.Parameters.AddWithValue("@UserId", userId ?? string.Empty);

                await connection.OpenAsync();
                using var adapter = new SqlDataAdapter(command);
                adapter.Fill(dataTable);

                _logger.LogInformation("Legacy SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY returned {RowCount} rows for user: {UserId}", dataTable.Rows.Count, userId);
                return dataTable;
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error in SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY for user: {UserId}", userId);
                return new DataTable();
            }
        }

        #endregion
    }
}
