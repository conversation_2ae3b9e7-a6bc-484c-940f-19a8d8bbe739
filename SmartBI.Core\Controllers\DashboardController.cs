using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Caching.Memory;
using SmartBI.Core.Services.Interfaces;
using SmartBI.Data.ViewModels.Dashboard;
using SmartBI.Data.ViewModels.RelationshipManager;
using SmartBI.Core.Models.DashboardModels;
using System.Data.SqlClient;
using System.Data;
using Dapper;

namespace SmartBI.Core.Controllers
{
    /// <summary>
    /// Dashboard Controller - Container for multiple dashboard functionalities
    /// CUARM is the first dashboard item - Legacy SmartBI2 compatible
    /// </summary>
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly IDashboardApiClient _dashboardApiClient;
        private readonly IRelationshipManagerApiClient _rmApiClient;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<DashboardController> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;

        public DashboardController(
            IDashboardApiClient dashboardApiClient,
            IRelationshipManagerApiClient rmApiClient,
            IMemoryCache memoryCache,
            ILogger<DashboardController> logger,
            IConfiguration configuration)
        {
            _dashboardApiClient = dashboardApiClient ?? throw new ArgumentNullException(nameof(dashboardApiClient));
            _rmApiClient = rmApiClient ?? throw new ArgumentNullException(nameof(rmApiClient));
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _connectionString = configuration.GetConnectionString("DefaultConnection");
        }

        #region CUARM Dashboard

        /// <summary>
        /// CUARM dashboard page - Legacy SmartBI2 compatible
        /// Simple user dashboard functionality only
        /// </summary>
        /// <returns>CUARM dashboard view with user statistics</returns>
        public async Task<IActionResult> CUARM()
        {
            try
            {
                _logger.LogInformation("Loading CUARM dashboard");

                var userSummary = await _dashboardApiClient.GetDashboardUserSummaryAsync();

                ViewBag.Title = "CUARM";

                return View(userSummary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading CUARM dashboard");
                return View(new DASHBOARD_USER_SUMMARY());
            }
        }

        #endregion

        #region RMDashboard
        /// <summary>
        /// Main RM Dashboard view
        /// </summary>
        public async Task<IActionResult> RMDashboard()
        {
            try
            {
                _logger.LogInformation("Loading RM Dashboard");
                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading RM Dashboard");
                return View();
            }
        }

        /// <summary>
        /// Load Performance Data partial view
        /// </summary>
        public async Task<IActionResult> PerformanceData()
        {
            try
            {
                _logger.LogInformation("Loading Performance Data");
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");
                var data = await _rmApiClient.GetPerformanceDataAsync(userId);
                return PartialView("Partials/_PerformanceData", data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading Performance Data");
                return PartialView("_Error", "Failed to load performance data");
            }
        }

        /// <summary>
        /// Load Performance & Attrition Data partial view
        /// </summary>
        public async Task<IActionResult> PerformanceAttritionData()
        {
            try
            {
                _logger.LogInformation("Loading Performance & Attrition Data");
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");
                var data = await _rmApiClient.GetPerformanceAttritionDataAsync(userId);
                return PartialView("Partials/_PerformanceAttrition", data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading Performance & Attrition Data");
                return PartialView("_Error", "Failed to load attrition data");
            }
        }

        /// <summary>
        /// Load Target Achievement Data partial view
        /// </summary>
        public async Task<IActionResult> TargetAchievementData()
        {
            try
            {
                _logger.LogInformation("Loading Target Achievement Data");
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");
                var data = await _rmApiClient.GetTargetAchievementDataAsync(userId);
                return PartialView("Partials/_TargetAchievement", data);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading Target Achievement Data");
                return PartialView("_Error", "Failed to load target achievement data");
            }
        }
        #endregion
    }
}
