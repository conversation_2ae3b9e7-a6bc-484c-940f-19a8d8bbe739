using Microsoft.AspNetCore.Authorization;
using Microsoft.AspNetCore.Mvc;
using Microsoft.Extensions.Logging;
using Microsoft.Extensions.Caching.Memory;
using Microsoft.Extensions.Configuration;
using SmartBI.Core.Services.Interfaces;
using SmartBI.Data.ViewModels.Dashboard;
using SmartBI.Data.ViewModels.RelationshipManager;
using System;
using System.Collections.Generic;
using System.Net.Http;
using System.Threading.Tasks;
using System.Security.Claims;

namespace SmartBI.Core.Controllers
{
    /// <summary>
    /// Dashboard Controller - Container for multiple dashboard functionalities
    /// CUARM is the first dashboard item - Legacy SmartBI2 compatible
    /// </summary>
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly IDashboardApiClient _dashboardApiClient;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<DashboardController> _logger;
        private readonly IConfiguration _configuration;

        public DashboardController(
            IDashboardApiClient dashboardApiClient,
            IMemoryCache memoryCache,
            ILogger<DashboardController> logger,
            IConfiguration configuration)
        {
            _dashboardApiClient = dashboardApiClient ?? throw new ArgumentNullException(nameof(dashboardApiClient));
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
        }

        #region CUARM Dashboard

        /// <summary>
        /// CUARM dashboard page - Legacy SmartBI2 compatible
        /// Simple user dashboard functionality only
        /// </summary>
        /// <returns>CUARM dashboard view with user statistics</returns>
        public async Task<IActionResult> CUARM()
        {
            try
            {
                _logger.LogInformation("Loading CUARM dashboard");

                var userSummary = await _dashboardApiClient.GetDashboardUserSummaryAsync();

                ViewBag.Title = "CUARM";

                return View(userSummary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading CUARM dashboard");
                return View(new DASHBOARD_USER_SUMMARY());
            }
        }

        #endregion

        #region Relationship Manager Dashboard

        /// <summary>
        /// Main RM Dashboard page with three tabs
        /// </summary>
        /// <returns>RM Dashboard view</returns>
        public IActionResult RMDashboard()
        {
            try
            {
                _logger.LogInformation("Loading RM Dashboard for user: {UserId}", GetCurrentUserId());

                ViewBag.Title = "Relationship Manager Dashboard";
                ViewBag.UserId = GetCurrentUserId();

                // Pass microservice configuration to view for JavaScript
                ViewBag.MicroserviceBaseUrl = _configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000";
                ViewBag.ApiTimeout = _configuration.GetSection("SmartBIMicroService:TimeoutMinutes").Value ?? "10";
                ViewBag.EnableFallbackMode = _configuration.GetSection("SmartBIMicroService:EnableFallbackMode").Value ?? "true";

                return View();
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading RM Dashboard");
                TempData["ErrorMessage"] = "An error occurred while loading the dashboard.";
                return View();
            }
        }

        /// <summary>
        /// AJAX endpoint for Performance data (Tab 1) - Legacy compatible
        /// Uses legacy DashboardRepository.GET_RM_DASHBOARD pattern
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetPerformanceData()
        {
            try
            {
                var userId = GetCurrentUserId();
                var cacheKey = $"rm_dashboard_{userId}_{DateTime.Today:yyyyMMdd}";

                _logger.LogInformation("Getting RM Dashboard data for user: {UserId}", userId);

                // Check cache first
                if (_memoryCache.TryGetValue(cacheKey, out object cachedData))
                {
                    _logger.LogInformation("Returning cached RM Dashboard data for user: {UserId}", userId);
                    return Json(new { success = true, data = cachedData, cached = true });
                }

                // Call legacy API: GET_RM_DASHBOARD
                var rmDashboardData = await CallLegacyAPI<SmartBI.Data.ViewModels.RM_PERFORMANCE_DASHBOARD>(
                    "rm-dashboard", userId, "RM Dashboard");

                // Cache for 4 hours (day-end data doesn't change frequently)
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(4),
                    SlidingExpiration = TimeSpan.FromHours(1),
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(cacheKey, rmDashboardData, cacheOptions);

                return Json(new { success = true, data = rmDashboardData, cached = false });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting RM Dashboard data for user: {UserId}", GetCurrentUserId());
                return Json(new { success = false, error = "Failed to load RM dashboard data" });
            }
        }

        /// <summary>
        /// AJAX endpoint for Performance & Attrition data (Tab 2)
        /// Returns attrition data only - growth data is fetched separately by GetRMGrowthData
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetPerformanceAttritionData()
        {
            try
            {
                var userId = GetCurrentUserId();
                var cacheKey = $"rm_perf_attrition_{userId}_{DateTime.Today:yyyyMMdd}";

                _logger.LogInformation("Getting Performance & Attrition data for user: {UserId}", userId);

                // Check cache first
                if (_memoryCache.TryGetValue(cacheKey, out object cachedData))
                {
                    _logger.LogInformation("Returning cached Performance & Attrition data for user: {UserId}", userId);
                    return Json(new { success = true, data = cachedData, cached = true });
                }

                // Call legacy API: RM_ATTRITION_GROWTH_SUMMARY
                var attritionData = await CallLegacyAPI<List<SmartBI.Data.ViewModels.RM_ATTRITION_GROWTH_SUMMARY>>(
                    "rm-attrition-growth-summary", userId, "RM Attrition Growth Summary");

                // Cache for 4 hours
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(4),
                    SlidingExpiration = TimeSpan.FromHours(1),
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(cacheKey, attritionData, cacheOptions);

                return Json(new { success = true, data = attritionData, cached = false });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Performance & Attrition data for user: {UserId}", GetCurrentUserId());
                return Json(new { success = false, error = "Failed to load performance & attrition data" });
            }
        }

        /// <summary>
        /// AJAX endpoint for Target VS Achievement data (Tab 3)
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetTargetAchievementData()
        {
            try
            {
                var userId = GetCurrentUserId();
                var cacheKey = $"rm_target_achievement_{userId}_{DateTime.Today:yyyyMMdd}";

                _logger.LogInformation("Getting Target VS Achievement data for user: {UserId}", userId);

                // Check cache first
                if (_memoryCache.TryGetValue(cacheKey, out RMTargetAchievementViewModel cachedData))
                {
                    _logger.LogInformation("Returning cached Target VS Achievement data for user: {UserId}", userId);
                    return Json(new { success = true, data = cachedData, cached = true });
                }

                // Call legacy API: GET_RM_TARGET_VS_ACHIEVEMENT
                var targetData = await CallLegacyAPI<SmartBI.Data.ViewModels.RM_TARGET_VS_ACHIEVEMENT>(
                    "rm-target-vs-achievement", userId, "RM Target vs Achievement");

                // Cache for 4 hours
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(4),
                    SlidingExpiration = TimeSpan.FromHours(1),
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(cacheKey, targetData, cacheOptions);

                return Json(new { success = true, data = targetData, cached = false });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting Target VS Achievement data for user: {UserId}", GetCurrentUserId());
                return Json(new { success = false, error = "Failed to load target vs achievement data" });
            }
        }

        /// <summary>
        /// AJAX endpoint for RM Growth data - Legacy compatible
        /// Uses legacy DashboardRepository.GET_RM_GROWTH pattern
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> GetRMGrowthData()
        {
            try
            {
                var userId = GetCurrentUserId();
                var cacheKey = $"rm_growth_{userId}_{DateTime.Today:yyyyMMdd}";

                _logger.LogInformation("Getting RM Growth data for user: {UserId}", userId);

                // Check cache first
                if (_memoryCache.TryGetValue(cacheKey, out object cachedData))
                {
                    _logger.LogInformation("Returning cached RM Growth data for user: {UserId}", userId);
                    return Json(new { success = true, data = cachedData, cached = true });
                }

                // Call legacy API: GET_RM_GROWTH
                var growthData = await CallLegacyAPI<List<SmartBI.Data.ViewModels.RM_PERFORMANCE_GROWTH>>(
                    "rm-growth", userId, "RM Growth");

                // Cache for 4 hours
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromHours(4),
                    SlidingExpiration = TimeSpan.FromHours(1),
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(cacheKey, growthData, cacheOptions);

                return Json(new { success = true, data = growthData, cached = false });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error getting RM Growth data for user: {UserId}", GetCurrentUserId());
                return Json(new { success = false, error = "Failed to load RM growth data" });
            }
        }

        /// <summary>
        /// Test endpoint to verify AJAX connectivity
        /// </summary>
        [HttpGet]
        public IActionResult TestConnection()
        {
            return Json(new { success = true, message = "Connection successful", timestamp = DateTime.Now });
        }

        /// <summary>
        /// Debug endpoint to show URL construction
        /// </summary>
        [HttpGet]
        public IActionResult DebugUrls()
        {
            var userId = GetCurrentUserId();
            var baseUrl = _configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000";

            var urls = new
            {
                userId = userId,
                baseUrl = baseUrl,
                performanceUrl = $"{baseUrl}/api/RelationshipManager/rm-dashboard?userId={Uri.EscapeDataString(userId)}",
                growthUrl = $"{baseUrl}/api/RelationshipManager/rm-growth?userId={Uri.EscapeDataString(userId)}",
                attritionUrl = $"{baseUrl}/api/RelationshipManager/rm-attrition-growth-summary?userId={Uri.EscapeDataString(userId)}",
                targetUrl = $"{baseUrl}/api/RelationshipManager/rm-target-vs-achievement?userId={Uri.EscapeDataString(userId)}"
            };

            return Json(new { success = true, urls = urls });
        }

        /// <summary>
        /// Test direct microservice call
        /// </summary>
        [HttpGet]
        public async Task<IActionResult> TestMicroserviceCall()
        {
            try
            {
                var userId = GetCurrentUserId();
                var result = await CallLegacyAPI<SmartBI.Data.ViewModels.RM_PERFORMANCE_DASHBOARD>(
                    "rm-dashboard", userId, "Test RM Dashboard");

                return Json(new { success = true, data = result, message = "Microservice call successful" });
            }
            catch (Exception ex)
            {
                return Json(new { success = false, error = ex.Message, stackTrace = ex.StackTrace });
            }
        }

        /// <summary>
        /// Clear cache for current user (for testing/refresh purposes)
        /// </summary>
        [HttpPost]
        public IActionResult ClearRMCache()
        {
            try
            {
                var userId = GetCurrentUserId();
                var today = DateTime.Today.ToString("yyyyMMdd");

                var cacheKeys = new[]
                {
                    $"rm_dashboard_{userId}_{today}",
                    $"rm_attrition_growth_{userId}_{today}",
                    $"rm_target_achievement_{userId}_{today}",
                    $"rm_growth_{userId}_{today}"
                };

                foreach (var key in cacheKeys)
                {
                    _memoryCache.Remove(key);
                }

                _logger.LogInformation("Cleared RM cache for user: {UserId}", userId);
                return Json(new { success = true, message = "Cache cleared successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing RM cache for user: {UserId}", GetCurrentUserId());
                return Json(new { success = false, error = "Failed to clear cache" });
            }
        }

        #endregion

        #region Helper Methods

        /// <summary>
        /// Gets the current user ID from claims
        /// </summary>
        private string GetCurrentUserId()
        {
            return User?.Identity?.Name ?? "admin";
        }

        /// <summary>
        /// Helper method to call legacy APIs with fallback to sample data
        /// </summary>
        private async Task<T> CallLegacyAPI<T>(string endpoint, string userId, string dataType) where T : new()
        {
            try
            {
                using var httpClient = new HttpClient();
                httpClient.Timeout = TimeSpan.FromMinutes(2);

                var baseUrl = _configuration.GetSection("SmartBIMicroService:BaseUrl").Value ?? "https://localhost:7000";
                var url = $"{baseUrl}/api/RelationshipManager/{endpoint}?userId={Uri.EscapeDataString(userId)}";

                _logger.LogInformation("Calling legacy API: {Url} for {DataType}", url, dataType);

                var response = await httpClient.GetAsync(url);

                _logger.LogInformation("API Response Status: {StatusCode} for {DataType}", response.StatusCode, dataType);

                if (response.IsSuccessStatusCode)
                {
                    var content = await response.Content.ReadAsStringAsync();
                    _logger.LogInformation("API Response Content Length: {Length} for {DataType}", content?.Length ?? 0, dataType);
                    _logger.LogDebug("API Response Content: {Content}", content);

                    if (string.IsNullOrWhiteSpace(content))
                    {
                        _logger.LogError("Empty response content from API for {DataType} - this indicates an API failure", dataType);
                        throw new InvalidOperationException($"Empty response from {dataType} API");
                    }

                    var result = Newtonsoft.Json.JsonConvert.DeserializeObject<T>(content);
                    _logger.LogInformation("Successfully deserialized {DataType} from legacy API for user: {UserId}", dataType, userId);

                    // Log the actual data for debugging
                    if (result != null)
                    {
                        _logger.LogDebug("Deserialized data: {Data}", Newtonsoft.Json.JsonConvert.SerializeObject(result));
                    }

                    return result ?? throw new InvalidOperationException($"Failed to deserialize {dataType} API response");
                }
                else
                {
                    var errorContent = await response.Content.ReadAsStringAsync();
                    _logger.LogError("Legacy API returned status: {StatusCode} with content: {Content} for {DataType}",
                        response.StatusCode, errorContent, dataType);
                    throw new HttpRequestException($"API call failed with status {response.StatusCode}: {errorContent}");
                }
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error calling legacy API for {DataType}, user: {UserId}, endpoint: {Endpoint}",
                    dataType, userId, endpoint);
                throw; // Re-throw the exception instead of returning empty object
            }
        }

        #endregion
    }
}
