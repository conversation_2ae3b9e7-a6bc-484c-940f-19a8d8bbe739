using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Caching.Memory;
using SmartBI.Core.Services.Interfaces;
using SmartBI.Data.ViewModels.Dashboard;
using SmartBI.Data.ViewModels.RelationshipManager;
using SmartBI.Core.Models.DashboardModels;
using SmartBI.Data.ViewModels;
using System.Data.SqlClient;
using System.Data;
using Dapper;
using Newtonsoft.Json;

namespace SmartBI.Core.Controllers
{
    /// <summary>
    /// Dashboard Controller - Container for multiple dashboard functionalities
    /// CUARM is the first dashboard item - Legacy SmartBI2 compatible
    /// </summary>
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly IDashboardApiClient _dashboardApiClient;
        private readonly IRelationshipManagerApiClient _rmApiClient;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<DashboardController> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;
        private readonly HttpClient _httpClient;
        private readonly string _microserviceBaseUrl;

        public DashboardController(
            IDashboardApiClient dashboardApiClient,
            IRelationshipManagerApiClient rmApiClient,
            IMemoryCache memoryCache,
            ILogger<DashboardController> logger,
            IConfiguration configuration,
            HttpClient httpClient)
        {
            _dashboardApiClient = dashboardApiClient ?? throw new ArgumentNullException(nameof(dashboardApiClient));
            _rmApiClient = rmApiClient ?? throw new ArgumentNullException(nameof(rmApiClient));
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _connectionString = configuration.GetConnectionString("DefaultConnection");
            _microserviceBaseUrl = configuration["MicroserviceSettings:SmartBIMicroServiceAPI:BaseUrl"] ?? "https://localhost:7001";
        }

        #region CUARM Dashboard

        /// <summary>
        /// CUARM dashboard page - Legacy SmartBI2 compatible
        /// Simple user dashboard functionality only
        /// </summary>
        /// <returns>CUARM dashboard view with user statistics</returns>
        public async Task<IActionResult> CUARM()
        {
            try
            {
                _logger.LogInformation("Loading CUARM dashboard");

                var userSummary = await _dashboardApiClient.GetDashboardUserSummaryAsync();

                ViewBag.Title = "CUARM";

                return View(userSummary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading CUARM dashboard");
                return View(new DASHBOARD_USER_SUMMARY());
            }
        }

        #endregion

        #region RMDashboard
        /// <summary>
        /// Main RM Dashboard view - Server-side rendered with all data
        /// </summary>
        public async Task<IActionResult> RMDashboard()
        {
            try
            {
                _logger.LogInformation("Loading RM Dashboard with all data");
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");

                // Load all dashboard data using direct API calls
                var dashboardViewModel = new RM_DASHBOARD_ITEMS();

                // Load performance dashboard data
                try
                {
                    var performanceResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/RelationshipManager/rm-dashboard?userId={Uri.EscapeDataString(userId)}");
                    if (performanceResponse.IsSuccessStatusCode)
                    {
                        var performanceContent = await performanceResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.RM_PERFORMANCE = JsonConvert.DeserializeObject<RM_PERFORMANCE_DASHBOARD>(performanceContent) ?? new RM_PERFORMANCE_DASHBOARD();
                    }
                    else
                    {
                        dashboardViewModel.RM_PERFORMANCE = new RM_PERFORMANCE_DASHBOARD();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading performance data");
                    dashboardViewModel.RM_PERFORMANCE = new RM_PERFORMANCE_DASHBOARD();
                }

                // Load growth data
                try
                {
                    var growthResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/RelationshipManager/rm-growth?userId={Uri.EscapeDataString(userId)}");
                    if (growthResponse.IsSuccessStatusCode)
                    {
                        var growthContent = await growthResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.RM_GROWTH = JsonConvert.DeserializeObject<List<RM_PERFORMANCE_GROWTH>>(growthContent) ?? new List<RM_PERFORMANCE_GROWTH>();
                    }
                    else
                    {
                        dashboardViewModel.RM_GROWTH = new List<RM_PERFORMANCE_GROWTH>();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading growth data");
                    dashboardViewModel.RM_GROWTH = new List<RM_PERFORMANCE_GROWTH>();
                }

                // Load attrition data
                try
                {
                    var attritionResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/RelationshipManager/rm-attrition-growth-summary?userId={Uri.EscapeDataString(userId)}");
                    if (attritionResponse.IsSuccessStatusCode)
                    {
                        var attritionContent = await attritionResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.RM_ATTRITION_GROWTH = JsonConvert.DeserializeObject<List<RM_ATTRITION_GROWTH_SUMMARY>>(attritionContent) ?? new List<RM_ATTRITION_GROWTH_SUMMARY>();
                    }
                    else
                    {
                        dashboardViewModel.RM_ATTRITION_GROWTH = new List<RM_ATTRITION_GROWTH_SUMMARY>();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading attrition data");
                    dashboardViewModel.RM_ATTRITION_GROWTH = new List<RM_ATTRITION_GROWTH_SUMMARY>();
                }

                // Load target achievement data
                try
                {
                    var targetResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/RelationshipManager/rm-target-vs-achievement?userId={Uri.EscapeDataString(userId)}");
                    if (targetResponse.IsSuccessStatusCode)
                    {
                        var targetContent = await targetResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.RM_TARGET_VS_ACHIEVEMENT = JsonConvert.DeserializeObject<RM_TARGET_VS_ACHIEVEMENT>(targetContent) ?? new RM_TARGET_VS_ACHIEVEMENT();
                    }
                    else
                    {
                        dashboardViewModel.RM_TARGET_VS_ACHIEVEMENT = new RM_TARGET_VS_ACHIEVEMENT();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading target achievement data");
                    dashboardViewModel.RM_TARGET_VS_ACHIEVEMENT = new RM_TARGET_VS_ACHIEVEMENT();
                }

                ViewBag.Title = "RM Dashboard";
                return View(dashboardViewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading RM Dashboard");
                // Return empty view model on error
                var emptyViewModel = new RM_DASHBOARD_ITEMS
                {
                    RM_PERFORMANCE = new RM_PERFORMANCE_DASHBOARD(),
                    RM_GROWTH = new List<RM_PERFORMANCE_GROWTH>(),
                    RM_ATTRITION_GROWTH = new List<RM_ATTRITION_GROWTH_SUMMARY>(),
                    RM_TARGET_VS_ACHIEVEMENT = new RM_TARGET_VS_ACHIEVEMENT()
                };
                return View(emptyViewModel);
            }
        }


        #endregion
    }
}
