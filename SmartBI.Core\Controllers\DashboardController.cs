using Microsoft.AspNetCore.Authorization;
using Microsoft.Extensions.Caching.Memory;
using SmartBI.Core.Services.Interfaces;
using SmartBI.Data.ViewModels.Dashboard;
using SmartBI.Data.ViewModels.RelationshipManager;
using SmartBI.Core.Models.DashboardModels;
using SmartBI.Data.ViewModels;
using System.Data.SqlClient;
using System.Data;
using Newtonsoft.Json;

namespace SmartBI.Core.Controllers
{
    /// <summary>
    /// Dashboard Controller - Container for multiple dashboard functionalities
    /// CUARM is the first dashboard item - Legacy SmartBI2 compatible
    /// </summary>
    [Authorize]
    public class DashboardController : Controller
    {
        private readonly IDashboardApiClient _dashboardApiClient;
        private readonly IRelationshipManagerApiClient _rmApiClient;
        private readonly IMemoryCache _memoryCache;
        private readonly ILogger<DashboardController> _logger;
        private readonly IConfiguration _configuration;
        private readonly string _connectionString;
        private readonly HttpClient _httpClient;
        private readonly string _microserviceBaseUrl;

        public DashboardController(
            IDashboardApiClient dashboardApiClient,
            IRelationshipManagerApiClient rmApiClient,
            IMemoryCache memoryCache,
            ILogger<DashboardController> logger,
            IConfiguration configuration,
            HttpClient httpClient)
        {
            _dashboardApiClient = dashboardApiClient ?? throw new ArgumentNullException(nameof(dashboardApiClient));
            _rmApiClient = rmApiClient ?? throw new ArgumentNullException(nameof(rmApiClient));
            _memoryCache = memoryCache ?? throw new ArgumentNullException(nameof(memoryCache));
            _logger = logger ?? throw new ArgumentNullException(nameof(logger));
            _configuration = configuration ?? throw new ArgumentNullException(nameof(configuration));
            _httpClient = httpClient ?? throw new ArgumentNullException(nameof(httpClient));
            _connectionString = configuration.GetConnectionString("DefaultConnection");
            _microserviceBaseUrl = configuration["SmartBIMicroService:BaseUrl"] ?? "https://localhost:7001";
        }

        #region CUARM Dashboard

        /// <summary>
        /// CUARM dashboard page - Legacy SmartBI2 compatible
        /// Simple user dashboard functionality only
        /// </summary>
        /// <returns>CUARM dashboard view with user statistics</returns>
        public async Task<IActionResult> CUARM()
        {
            try
            {
                _logger.LogInformation("Loading CUARM dashboard");

                var userSummary = await _dashboardApiClient.GetDashboardUserSummaryAsync();

                ViewBag.Title = "CUARM";

                return View(userSummary);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading CUARM dashboard");
                return View(new DASHBOARD_USER_SUMMARY());
            }
        }

        #endregion

        #region RMDashboard
        /// <summary>
        /// Main RM Dashboard view - Server-side rendered with all data and user-specific caching
        /// </summary>
        public async Task<IActionResult> RMDashboard()
        {
            try
            {
                _logger.LogInformation("Loading RM Dashboard with all data");
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");

                // Generate user-specific cache key with current date for daily refresh
                var cacheKey = $"RM_DASHBOARD_{userId}_{DateTime.Now:yyyy-MM-dd}";

                // Try to get cached data first
                if (_memoryCache.TryGetValue(cacheKey, out RM_DASHBOARD_ITEMS? cachedData) && cachedData != null)
                {
                    _logger.LogInformation("RM Dashboard data loaded from cache for user: {UserId}", userId);
                    ViewBag.Title = "RM Dashboard";
                    ViewBag.CacheHit = true;
                    return View(cachedData);
                }

                _logger.LogInformation("Cache miss - Loading fresh RM Dashboard data for user: {UserId}", userId);

                // Load all dashboard data using direct API calls
                var dashboardViewModel = new RM_DASHBOARD_ITEMS();

                // Load performance dashboard data
                try
                {
                    var performanceResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/RelationshipManager/rm-dashboard?userId={Uri.EscapeDataString(userId)}");
                    if (performanceResponse.IsSuccessStatusCode)
                    {
                        var performanceContent = await performanceResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.RM_PERFORMANCE = JsonConvert.DeserializeObject<RM_PERFORMANCE_DASHBOARD>(performanceContent) ?? new RM_PERFORMANCE_DASHBOARD();
                    }
                    else
                    {
                        dashboardViewModel.RM_PERFORMANCE = new RM_PERFORMANCE_DASHBOARD();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading performance data");
                    dashboardViewModel.RM_PERFORMANCE = new RM_PERFORMANCE_DASHBOARD();
                }

                // Load growth data
                try
                {
                    var growthResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/RelationshipManager/rm-growth?userId={Uri.EscapeDataString(userId)}");
                    if (growthResponse.IsSuccessStatusCode)
                    {
                        var growthContent = await growthResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.RM_GROWTH = JsonConvert.DeserializeObject<List<RM_PERFORMANCE_GROWTH>>(growthContent) ?? new List<RM_PERFORMANCE_GROWTH>();
                    }
                    else
                    {
                        dashboardViewModel.RM_GROWTH = new List<RM_PERFORMANCE_GROWTH>();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading growth data");
                    dashboardViewModel.RM_GROWTH = new List<RM_PERFORMANCE_GROWTH>();
                }

                // Load attrition data
                try
                {
                    var attritionResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/RelationshipManager/rm-attrition-growth-summary?userId={Uri.EscapeDataString(userId)}");
                    if (attritionResponse.IsSuccessStatusCode)
                    {
                        var attritionContent = await attritionResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.RM_ATTRITION_GROWTH = JsonConvert.DeserializeObject<List<RM_ATTRITION_GROWTH_SUMMARY>>(attritionContent) ?? new List<RM_ATTRITION_GROWTH_SUMMARY>();
                    }
                    else
                    {
                        dashboardViewModel.RM_ATTRITION_GROWTH = new List<RM_ATTRITION_GROWTH_SUMMARY>();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading attrition data");
                    dashboardViewModel.RM_ATTRITION_GROWTH = new List<RM_ATTRITION_GROWTH_SUMMARY>();
                }

                // Load target achievement data
                try
                {
                    var targetResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/RelationshipManager/rm-target-vs-achievement?userId={Uri.EscapeDataString(userId)}");
                    if (targetResponse.IsSuccessStatusCode)
                    {
                        var targetContent = await targetResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.RM_TARGET_VS_ACHIEVEMENT = JsonConvert.DeserializeObject<RM_TARGET_VS_ACHIEVEMENT>(targetContent) ?? new RM_TARGET_VS_ACHIEVEMENT();
                    }
                    else
                    {
                        dashboardViewModel.RM_TARGET_VS_ACHIEVEMENT = new RM_TARGET_VS_ACHIEVEMENT();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading target achievement data");
                    dashboardViewModel.RM_TARGET_VS_ACHIEVEMENT = new RM_TARGET_VS_ACHIEVEMENT();
                }

                // Cache the loaded data with user-specific key and appropriate expiration
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30), // 30 minutes absolute expiration
                    SlidingExpiration = TimeSpan.FromMinutes(15), // 15 minutes sliding expiration
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(cacheKey, dashboardViewModel, cacheOptions);
                _logger.LogInformation("RM Dashboard data cached for user: {UserId} with key: {CacheKey}", userId, cacheKey);

                ViewBag.Title = "RM Dashboard";
                ViewBag.CacheHit = false;
                return View(dashboardViewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading RM Dashboard");
                // Return empty view model on error
                var emptyViewModel = new RM_DASHBOARD_ITEMS
                {
                    RM_PERFORMANCE = new RM_PERFORMANCE_DASHBOARD(),
                    RM_GROWTH = new List<RM_PERFORMANCE_GROWTH>(),
                    RM_ATTRITION_GROWTH = new List<RM_ATTRITION_GROWTH_SUMMARY>(),
                    RM_TARGET_VS_ACHIEVEMENT = new RM_TARGET_VS_ACHIEVEMENT()
                };
                ViewBag.CacheHit = false;
                return View(emptyViewModel);
            }
        }

        /// <summary>
        /// Refresh RM Dashboard data by clearing cache and redirecting back to dashboard
        /// </summary>
        public IActionResult RefreshRMDashboard()
        {
            try
            {
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");

                // Clear user-specific cache for current date
                var cacheKey = $"RM_DASHBOARD_{userId}_{DateTime.Now:yyyy-MM-dd}";
                _memoryCache.Remove(cacheKey);

                _logger.LogInformation("RM Dashboard cache cleared for user: {UserId}", userId);

                // Redirect back to dashboard to load fresh data
                return RedirectToAction("RMDashboard");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing RM Dashboard cache");
                return RedirectToAction("RMDashboard");
            }
        }

        /// <summary>
        /// Clear all RM Dashboard cache entries for the current user
        /// </summary>
        public IActionResult ClearRMCache()
        {
            try
            {
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");

                // Clear cache for multiple days (in case of date changes during session)
                for (int i = 0; i <= 2; i++)
                {
                    var date = DateTime.Now.AddDays(-i);
                    var cacheKey = $"RM_DASHBOARD_{userId}_{date:yyyy-MM-dd}";
                    _memoryCache.Remove(cacheKey);
                }

                _logger.LogInformation("All RM Dashboard cache entries cleared for user: {UserId}", userId);

                return Json(new { success = true, message = "Cache cleared successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing RM Dashboard cache");
                return Json(new { success = false, message = "Error clearing cache" });
            }
        }

        #endregion

        #region Compliance Dashboard
        /// <summary>
        /// Main Compliance Dashboard view - Server-side rendered with all compliance data and user-specific caching
        /// </summary>
        public async Task<IActionResult> Compliance()
        {
            try
            {
                _logger.LogInformation("Loading Compliance Dashboard with all data");
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");

                // Generate user-specific cache key with current date for daily refresh
                var cacheKey = $"COMPLIANCE_DASHBOARD_{userId}_{DateTime.Now:yyyy-MM-dd}";

                // Try to get cached data first
                if (_memoryCache.TryGetValue(cacheKey, out ComplianceDashboardViewModel? cachedData) && cachedData != null)
                {
                    _logger.LogInformation("Compliance Dashboard data loaded from cache for user: {UserId}", userId);
                    ViewBag.Title = "Compliance Dashboard";
                    ViewBag.CacheHit = true;
                    return View(cachedData);
                }

                _logger.LogInformation("Cache miss - Loading fresh Compliance Dashboard data for user: {UserId}", userId);

                // Load all compliance dashboard data using direct API calls
                var dashboardViewModel = new ComplianceDashboardViewModel();

                // Load KYC Overdue data (High Risk)
                try
                {
                    var kycHighResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/kyc-overdue-high?userId={Uri.EscapeDataString(userId)}");
                    if (kycHighResponse.IsSuccessStatusCode)
                    {
                        var kycHighContent = await kycHighResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.KYC_OVERDUE_HIGH = JsonConvert.DeserializeObject<DataTable>(kycHighContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.KYC_OVERDUE_HIGH = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading KYC Overdue High data");
                    dashboardViewModel.KYC_OVERDUE_HIGH = new DataTable();
                }

                // Load KYC Overdue data (Low Risk)
                try
                {
                    var kycLowResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/kyc-overdue-low?userId={Uri.EscapeDataString(userId)}");
                    if (kycLowResponse.IsSuccessStatusCode)
                    {
                        var kycLowContent = await kycLowResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.KYC_OVERDUE_LOW = JsonConvert.DeserializeObject<DataTable>(kycLowContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.KYC_OVERDUE_LOW = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading KYC Overdue Low data");
                    dashboardViewModel.KYC_OVERDUE_LOW = new DataTable();
                }

                // Load KYC Overdue SAF data (High Risk)
                try
                {
                    var kycSafHighResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/kyc-overdue-saf-high?userId={Uri.EscapeDataString(userId)}");
                    if (kycSafHighResponse.IsSuccessStatusCode)
                    {
                        var kycSafHighContent = await kycSafHighResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.KYC_OVERDUE_SAF_HIGH = JsonConvert.DeserializeObject<DataTable>(kycSafHighContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.KYC_OVERDUE_SAF_HIGH = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading KYC Overdue SAF High data");
                    dashboardViewModel.KYC_OVERDUE_SAF_HIGH = new DataTable();
                }

                // Load KYC Overdue SAF data (Low Risk)
                try
                {
                    var kycSafLowResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/kyc-overdue-saf-low?userId={Uri.EscapeDataString(userId)}");
                    if (kycSafLowResponse.IsSuccessStatusCode)
                    {
                        var kycSafLowContent = await kycSafLowResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.KYC_OVERDUE_SAF_LOW = JsonConvert.DeserializeObject<DataTable>(kycSafLowContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.KYC_OVERDUE_SAF_LOW = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading KYC Overdue SAF Low data");
                    dashboardViewModel.KYC_OVERDUE_SAF_LOW = new DataTable();
                }

                // Load NGO/NPO data
                try
                {
                    var ngoNpoResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/ngo-npo?userId={Uri.EscapeDataString(userId)}");
                    if (ngoNpoResponse.IsSuccessStatusCode)
                    {
                        var ngoNpoContent = await ngoNpoResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.NGO_NPO = JsonConvert.DeserializeObject<DataTable>(ngoNpoContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.NGO_NPO = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading NGO/NPO data");
                    dashboardViewModel.NGO_NPO = new DataTable();
                }

                // Load PEP/IP data
                try
                {
                    var pepIpResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/pep-ip?userId={Uri.EscapeDataString(userId)}");
                    if (pepIpResponse.IsSuccessStatusCode)
                    {
                        var pepIpContent = await pepIpResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.PEP_IP = JsonConvert.DeserializeObject<DataTable>(pepIpContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.PEP_IP = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading PEP/IP data");
                    dashboardViewModel.PEP_IP = new DataTable();
                }

                // Load Stand Alone FDR data
                try
                {
                    var standAloneFdrResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/standalone-fdr?userId={Uri.EscapeDataString(userId)}");
                    if (standAloneFdrResponse.IsSuccessStatusCode)
                    {
                        var standAloneFdrContent = await standAloneFdrResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.StandAloneTDR = JsonConvert.DeserializeObject<DataTable>(standAloneFdrContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.StandAloneTDR = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading Stand Alone FDR data");
                    dashboardViewModel.StandAloneTDR = new DataTable();
                }

                // Load FATCA data
                try
                {
                    var fatcaResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/fatca?userId={Uri.EscapeDataString(userId)}");
                    if (fatcaResponse.IsSuccessStatusCode)
                    {
                        var fatcaContent = await fatcaResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.FATCA = JsonConvert.DeserializeObject<DataTable>(fatcaContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.FATCA = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading FATCA data");
                    dashboardViewModel.FATCA = new DataTable();
                }

                // Load AML data
                try
                {
                    var amlResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/aml?userId={Uri.EscapeDataString(userId)}");
                    if (amlResponse.IsSuccessStatusCode)
                    {
                        var amlContent = await amlResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.AML = JsonConvert.DeserializeObject<DataTable>(amlContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.AML = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading AML data");
                    dashboardViewModel.AML = new DataTable();
                }

                // Load FCCM Alert data
                try
                {
                    var fccmResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/fccm-alert?userId={Uri.EscapeDataString(userId)}");
                    if (fccmResponse.IsSuccessStatusCode)
                    {
                        var fccmContent = await fccmResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.FCCM = JsonConvert.DeserializeObject<SmartBI.Data.ViewModels.Dashboard.FCCM_ALERT_DASHBOARD>(fccmContent) ?? new SmartBI.Data.ViewModels.Dashboard.FCCM_ALERT_DASHBOARD();
                    }
                    else
                    {
                        dashboardViewModel.FCCM = new SmartBI.Data.ViewModels.Dashboard.FCCM_ALERT_DASHBOARD();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading FCCM Alert data");
                    dashboardViewModel.FCCM = new SmartBI.Data.ViewModels.Dashboard.FCCM_ALERT_DASHBOARD();
                }

                // Load Customer KYC Update Status data
                try
                {
                    var customerKycResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/customer-kyc-update-status?userId={Uri.EscapeDataString(userId)}");
                    if (customerKycResponse.IsSuccessStatusCode)
                    {
                        var customerKycContent = await customerKycResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.CUSTOMER_KYC_UPDATE_STATUS = JsonConvert.DeserializeObject<DataTable>(customerKycContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.CUSTOMER_KYC_UPDATE_STATUS = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading Customer KYC Update Status data");
                    dashboardViewModel.CUSTOMER_KYC_UPDATE_STATUS = new DataTable();
                }

                // Load Account Statistics data
                try
                {
                    var accountStatsResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/account-statistics?userId={Uri.EscapeDataString(userId)}");
                    if (accountStatsResponse.IsSuccessStatusCode)
                    {
                        var accountStatsContent = await accountStatsResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.AccountStatistics = JsonConvert.DeserializeObject<DataTable>(accountStatsContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.AccountStatistics = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading Account Statistics data");
                    dashboardViewModel.AccountStatistics = new DataTable();
                }

                // Load Stand Alone TDR Summary data
                try
                {
                    var standAloneSummaryResponse = await _httpClient.GetAsync($"{_microserviceBaseUrl}/api/Compliance/standalone-fdr-summary?userId={Uri.EscapeDataString(userId)}");
                    if (standAloneSummaryResponse.IsSuccessStatusCode)
                    {
                        var standAloneSummaryContent = await standAloneSummaryResponse.Content.ReadAsStringAsync();
                        dashboardViewModel.StandAloneTDRSummary = JsonConvert.DeserializeObject<DataTable>(standAloneSummaryContent) ?? new DataTable();
                    }
                    else
                    {
                        dashboardViewModel.StandAloneTDRSummary = new DataTable();
                    }
                }
                catch (Exception ex)
                {
                    _logger.LogError(ex, "Error loading Stand Alone TDR Summary data");
                    dashboardViewModel.StandAloneTDRSummary = new DataTable();
                }

                // Cache the loaded data with user-specific key and appropriate expiration
                var cacheOptions = new MemoryCacheEntryOptions
                {
                    AbsoluteExpirationRelativeToNow = TimeSpan.FromMinutes(30), // 30 minutes absolute expiration
                    SlidingExpiration = TimeSpan.FromMinutes(15), // 15 minutes sliding expiration
                    Priority = CacheItemPriority.Normal
                };

                _memoryCache.Set(cacheKey, dashboardViewModel, cacheOptions);
                _logger.LogInformation("Compliance Dashboard data cached for user: {UserId} with key: {CacheKey}", userId, cacheKey);

                ViewBag.Title = "Compliance Dashboard";
                ViewBag.CacheHit = false;
                return View(dashboardViewModel);
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error loading Compliance Dashboard");
                // Return empty view model on error
                var emptyViewModel = new ComplianceDashboardViewModel();
                ViewBag.CacheHit = false;
                return View(emptyViewModel);
            }
        }

        /// <summary>
        /// Refresh Compliance Dashboard data by clearing cache and redirecting back to dashboard
        /// </summary>
        public IActionResult RefreshComplianceDashboard()
        {
            try
            {
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");

                // Clear user-specific cache for current date
                var cacheKey = $"COMPLIANCE_DASHBOARD_{userId}_{DateTime.Now:yyyy-MM-dd}";
                _memoryCache.Remove(cacheKey);

                _logger.LogInformation("Compliance Dashboard cache cleared for user: {UserId}", userId);

                // Redirect back to dashboard to load fresh data
                return RedirectToAction("Compliance");
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error refreshing Compliance Dashboard cache");
                return RedirectToAction("Compliance");
            }
        }

        /// <summary>
        /// Clear all Compliance Dashboard cache entries for the current user
        /// </summary>
        public IActionResult ClearComplianceCache()
        {
            try
            {
                var userId = User.Identity?.Name ?? throw new InvalidOperationException("User not authenticated");

                // Clear cache for multiple days (in case of date changes during session)
                for (int i = 0; i <= 2; i++)
                {
                    var date = DateTime.Now.AddDays(-i);
                    var cacheKey = $"COMPLIANCE_DASHBOARD_{userId}_{date:yyyy-MM-dd}";
                    _memoryCache.Remove(cacheKey);
                }

                _logger.LogInformation("All Compliance Dashboard cache entries cleared for user: {UserId}", userId);

                return Json(new { success = true, message = "Cache cleared successfully" });
            }
            catch (Exception ex)
            {
                _logger.LogError(ex, "Error clearing Compliance Dashboard cache");
                return Json(new { success = false, message = "Error clearing cache" });
            }
        }

        #endregion
    }
}
