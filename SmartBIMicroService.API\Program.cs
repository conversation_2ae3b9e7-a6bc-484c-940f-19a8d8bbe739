using Microsoft.EntityFrameworkCore;
using SmartBI.Data;
using SmartBIMicroService.API.Services;
using SmartBIMicroService.API.Utilities;

var builder = WebApplication.CreateBuilder(args);

// Add database context - using SmartBI.Data project
builder.Services.AddDbContext<SmartBIContext>(options =>
    options.UseSqlServer(builder.Configuration.GetConnectionString("SmartBIConnection")));

// Register repositories from SmartBI.Data project
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IUserRepository, SmartBI.Data.Repositories.UserRepository>();
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IRoleRepository, SmartBI.Data.Repositories.RoleRepository>();
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IMenuRepository, SmartBI.Data.Repositories.MenuRepository>();

// Register existing report repositories
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IReportRepository, SmartBI.Data.Repositories.ReportRepository>();
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IReportParameterRepository, SmartBI.Data.Repositories.ReportParameterRepository>();
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IAccountRepository, SmartBI.Data.Repositories.AccountRepository>();

// Register branch repository
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IBranchRepository, SmartBI.Data.Repositories.BranchRepository>();

// Register report tree repository
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IReportTreeRepository, SmartBI.Data.Repositories.ReportTreeRepository>();

// Register LOV repository
builder.Services.AddScoped<SmartBI.Data.Repositories.ILovRepository, SmartBI.Data.Repositories.LovRepository>();

// Register File Upload repository
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IFileUploadRepository, SmartBI.Data.Repositories.FileUploadRepository>();

// Register TradeOps repository
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.ITradeOpsRepository, SmartBI.Data.Repositories.TradeOpsRepository>();

// Register Dashboard repository
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IDashboardRepository, SmartBI.Data.Repositories.DashboardRepository>();

// Register Relationship Manager repository
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IRelationshipManagerRepository, SmartBI.Data.Repositories.RelationshipManagerRepository>();

// Register Compliance repository
builder.Services.AddScoped<SmartBI.Data.Repositories.Interfaces.IComplianceRepository, SmartBI.Data.Repositories.ComplianceRepository>();

// Configure API-only services - no MVC views since this is a pure microservice
builder.Services.AddControllers()
    .AddJsonOptions(options => {
        // Configure JSON serialization for DataTable support
        options.JsonSerializerOptions.PropertyNamingPolicy = null;
        options.JsonSerializerOptions.WriteIndented = true;
    });

// Register report service
builder.Services.AddScoped<IReportService, ReportService>();

// Register managers
builder.Services.AddScoped<SmartBIMicroService.API.Managers.ReportParameterManager>();
// Note: ReportManager is now static and initialized at startup, no need for DI registration

// Register HttpContextAccessor for accessing the current HttpContext
builder.Services.AddHttpContextAccessor();

// Add CORS support for cross-origin requests from SmartBI.Core
builder.Services.AddCors(options =>
{
    options.AddPolicy("AllowSmartBICore", policy =>
    {
        if (builder.Environment.IsDevelopment())
        {
            // Allow all origins in development for easier testing
            policy.AllowAnyOrigin()
                  .AllowAnyHeader()
                  .AllowAnyMethod();
        }
        else
        {
            policy.WithOrigins("http://localhost:5019", "https://localhost:7019", "http://localhost:5020", "https://localhost:7020") // SmartBI.Core URLs
                  .AllowAnyHeader()
                  .AllowAnyMethod()
                  .AllowCredentials();
        }
    });
});

// Learn more about configuring Swagger/OpenAPI at https://aka.ms/aspnetcore/swashbuckle
builder.Services.AddEndpointsApiExplorer();
builder.Services.AddSwaggerGen(c =>
{
    c.SwaggerDoc("v1", new Microsoft.OpenApi.Models.OpenApiInfo
    {
        Title = "SmartBI MicroService API",
        Version = "v1",
        Description = "Microservice for SmartBI"
    });
});

var app = builder.Build();

// Set the service provider in the Invoker class for dynamic method invocation
Invoker.SetServiceProvider(app.Services);

// Initialize the static ReportManager with required dependencies
// Pass the root service provider instead of a scoped DbContext
var logger = app.Services.GetRequiredService<ILogger<SmartBI2.Models.ReportManager>>();
var configuration = app.Services.GetRequiredService<IConfiguration>();
SmartBI2.Models.ReportManager.Initialize(app.Services, logger, configuration);

// Configure the HTTP request pipeline.
// Enable serving static files from wwwroot
app.UseStaticFiles();

if (app.Environment.IsDevelopment())
{
    app.UseSwagger();
    
    // Configure Swagger UI at /swagger path
    app.UseSwaggerUI(c =>
    {
        c.SwaggerEndpoint("/swagger/v1/swagger.json", "SmartBI Microservice API v1");
        c.RoutePrefix = "swagger";
    });
    
    // Redirect root to Swagger UI
    app.MapGet("/", () => Results.Redirect("/swagger"));
    
    // Redirect /index.html to Swagger UI
    app.MapGet("/index.html", () => Results.Redirect("/swagger"));
    
    // Create a custom endpoint for any path that might try to access views
    app.MapGet("/Views/{**path}", () => Results.Content(
        "<!DOCTYPE html><html><head><title>API Only</title></head><body><h1>This is an API-only microservice</h1><p>MVC views are not supported.</p></body></html>",
        "text/html",
        System.Text.Encoding.UTF8,
        404));
}

app.UseHttpsRedirection();

// Enable CORS
app.UseCors("AllowSmartBICore");

app.UseAuthorization();

// Add health check endpoint
app.MapGet("/api/health", () => Results.Ok(new { status = "Healthy", timestamp = DateTime.UtcNow }));

app.MapControllers();

// Log startup information
app.Logger.LogInformation("SmartBI Microservice API started successfully");
app.Logger.LogInformation("Swagger UI available at: {BaseUrl}", app.Environment.IsDevelopment() ? "http://localhost:5000" : "N/A");

app.Run();
