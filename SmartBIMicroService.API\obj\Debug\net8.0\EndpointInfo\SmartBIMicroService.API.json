{"openapi": "3.0.1", "info": {"title": "SmartBI MicroService API", "description": "Microservice for SmartBI", "version": "v1"}, "paths": {"/api/Account/authenticate": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserAuthenticationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserAuthenticationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserAuthenticationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/info/{userId}": {"get": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/companies/{userId}": {"get": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/user-roles/{userId}": {"get": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/validate/{userId}": {"get": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/branch/{branchCode}": {"get": {"tags": ["Account"], "parameters": [{"name": "branchCode", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/userprofile/{userId}": {"get": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/changepassword": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/roles": {"get": {"tags": ["Account"], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Role"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/roles/{id}": {"get": {"tags": ["Account"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Account"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/Role"}}, "text/json": {"schema": {"$ref": "#/components/schemas/Role"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/Role"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Account"], "parameters": [{"name": "id", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/users": {"get": {"tags": ["Account"], "parameters": [{"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 10}}], "responses": {"200": {"description": "OK"}}}, "post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserCreateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/users/all": {"get": {"tags": ["Account"], "responses": {"200": {"description": "OK"}}}}, "/api/Account/users/{userId}": {"get": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}, "put": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UserUpdateRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/role-privileges/{roleId}": {"get": {"tags": ["Account"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/menu-tree": {"get": {"tags": ["Account"], "responses": {"200": {"description": "OK"}}}}, "/api/Account/role-privileges": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateRolePrivilegeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateRolePrivilegeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateRolePrivilegeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/report-tree/by-role/{roleId}": {"get": {"tags": ["Account"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}}}}}}}}, "/api/Account/report-tree/by-user/{username}": {"get": {"tags": ["Account"], "parameters": [{"name": "username", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}}}}}}}}, "/api/Account/report-tree/structure": {"get": {"tags": ["Account"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}}}}}}}}, "/api/Account/report-privileges": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateReportPrivilegeRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateReportPrivilegeRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateReportPrivilegeRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Account/unapproved-users": {"get": {"tags": ["Account"], "responses": {"200": {"description": "OK"}}}}, "/api/Account/approve-user/{userId}": {"post": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/reject-user/{userId}": {"post": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/branches": {"get": {"tags": ["Account"], "responses": {"200": {"description": "OK"}}}}, "/api/Account/user-company-access/{userId}": {"get": {"tags": ["Account"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Account/user-company-access": {"post": {"tags": ["Account"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/UpdateUserCompanyAccessRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UpdateUserCompanyAccessRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/UpdateUserCompanyAccessRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Compliance/kyc-overdue-high": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/kyc-overdue-low": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/kyc-overdue-saf-high": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/kyc-overdue-saf-low": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/ngo-npo": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/pep-ip": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/standalone-fdr": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/fatca": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/aml": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/fccm-alert": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FCCM_ALERT_DASHBOARD"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FCCM_ALERT_DASHBOARD"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FCCM_ALERT_DASHBOARD"}}}}}}}, "/api/Compliance/customer-kyc-update-status": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/account-statistics": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Compliance/standalone-fdr-summary": {"get": {"tags": ["Compliance"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "application/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}, "text/json": {"schema": {"$ref": "#/components/schemas/DataTable"}}}}}}}, "/api/Dashboard/user-summary": {"get": {"tags": ["Dashboard"], "responses": {"200": {"description": "OK"}}}}, "/api/FileUpload/upload": {"post": {"tags": ["FileUpload"], "requestBody": {"content": {"multipart/form-data": {"schema": {"type": "object", "properties": {"file": {"type": "string", "format": "binary"}, "dataType": {"type": "string"}, "uploadedBy": {"type": "string"}, "description": {"type": "string"}}}, "encoding": {"file": {"style": "form"}, "dataType": {"style": "form"}, "uploadedBy": {"style": "form"}, "description": {"style": "form"}}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}}}}}}, "/api/FileUpload/history": {"get": {"tags": ["FileUpload"], "parameters": [{"name": "FileName", "in": "query", "schema": {"type": "string"}}, {"name": "UploadedBy", "in": "query", "schema": {"type": "string"}}, {"name": "DataType", "in": "query", "schema": {"type": "string"}}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SupervisionStatus", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IsDataProcessed", "in": "query", "schema": {"type": "boolean"}}, {"name": "PageNumber", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "SortBy", "in": "query", "schema": {"type": "string"}}, {"name": "SortDirection", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadHistoryViewModelPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadHistoryViewModelPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadHistoryViewModelPaginatedResult"}}}}}}}, "/api/FileUpload/pending": {"get": {"tags": ["FileUpload"], "parameters": [{"name": "dataType", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileUploadAuthorizationViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileUploadAuthorizationViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/FileUploadAuthorizationViewModel"}}}}}}}}, "/api/FileUpload/authorize": {"post": {"tags": ["FileUpload"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/FileUploadAuthorizationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadAuthorizationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/FileUploadAuthorizationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadResponse"}}}}}}}, "/api/FileUpload/statistics": {"get": {"tags": ["FileUpload"], "parameters": [{"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "username", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadStatistics"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadStatistics"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadStatistics"}}}}}}}, "/api/FileUpload/validate": {"get": {"tags": ["FileUpload"], "parameters": [{"name": "username", "in": "query", "schema": {"type": "string"}}, {"name": "fileSize", "in": "query", "schema": {"type": "integer", "format": "int64"}}, {"name": "dataType", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/FileUploadValidationResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/FileUploadValidationResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/FileUploadValidationResult"}}}}}}}, "/api/Lov/{entityType}": {"get": {"tags": ["<PERSON>v"], "parameters": [{"name": "entityType", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "query", "schema": {"type": "string"}}, {"name": "filter", "in": "query", "schema": {"type": "string"}}, {"name": "includeInactive", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "includeAllOption", "in": "query", "schema": {"type": "boolean", "default": false}}, {"name": "pageNumber", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 1}}, {"name": "pageSize", "in": "query", "schema": {"type": "integer", "format": "int32", "default": 100}}], "responses": {"200": {"description": "OK"}}}}, "/api/Lov/branches": {"get": {"tags": ["<PERSON>v"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}, {"name": "includeAllOption", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/Lov/roles": {"get": {"tags": ["<PERSON>v"], "parameters": [{"name": "includeAllOption", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/Lov/departments": {"get": {"tags": ["<PERSON>v"], "parameters": [{"name": "includeAllOption", "in": "query", "schema": {"type": "boolean", "default": false}}], "responses": {"200": {"description": "OK"}}}}, "/api/Lov/entity-types": {"get": {"tags": ["<PERSON>v"], "responses": {"200": {"description": "OK"}}}}, "/api/Menu/user-menu-items/{roleId}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItemViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItemViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/MenuItemViewModel"}}}}}}}}, "/api/Menu/main-menus": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/Menu/sub-menus": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/Menu/sub-menus/by-main-menu/{mainMenuId}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "mainMenuId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/Menu/privileges": {"get": {"tags": ["<PERSON><PERSON>"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/Menu/privileges/by-role/{roleId}": {"get": {"tags": ["<PERSON><PERSON>"], "parameters": [{"name": "roleId", "in": "path", "required": true, "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {}}}, "application/json": {"schema": {"type": "array", "items": {}}}, "text/json": {"schema": {"type": "array", "items": {}}}}}}}}, "/api/RelationshipManager/rm-dashboard": {"get": {"tags": ["RelationshipManager"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RM_PERFORMANCE_DASHBOARD"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RM_PERFORMANCE_DASHBOARD"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RM_PERFORMANCE_DASHBOARD"}}}}}}}, "/api/RelationshipManager/rm-growth": {"get": {"tags": ["RelationshipManager"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RM_PERFORMANCE_GROWTH"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RM_PERFORMANCE_GROWTH"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RM_PERFORMANCE_GROWTH"}}}}}}}}, "/api/RelationshipManager/rm-attrition-growth-summary": {"get": {"tags": ["RelationshipManager"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RM_ATTRITION_GROWTH_SUMMARY"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RM_ATTRITION_GROWTH_SUMMARY"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/RM_ATTRITION_GROWTH_SUMMARY"}}}}}}}}, "/api/RelationshipManager/rm-target-vs-achievement": {"get": {"tags": ["RelationshipManager"], "parameters": [{"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/RM_TARGET_VS_ACHIEVEMENT"}}, "application/json": {"schema": {"$ref": "#/components/schemas/RM_TARGET_VS_ACHIEVEMENT"}}, "text/json": {"schema": {"$ref": "#/components/schemas/RM_TARGET_VS_ACHIEVEMENT"}}}}}}}, "/api/RelationshipManager/health": {"get": {"tags": ["RelationshipManager"], "responses": {"200": {"description": "OK"}}}}, "/api/report/param-controls/{Id}/{userId}": {"get": {"tags": ["Report"], "parameters": [{"name": "Id", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/report/tree/{userName}": {"get": {"tags": ["Report"], "parameters": [{"name": "userName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/report/data/{reportId}": {"post": {"tags": ["Report"], "parameters": [{"name": "reportId", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "text/json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}, "application/*+json": {"schema": {"type": "object", "additionalProperties": {"type": "string"}}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/report/path/{reportId}": {"get": {"tags": ["Report"], "parameters": [{"name": "reportId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/report/generate": {"post": {"tags": ["Report"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReportRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ReportRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Reports/generate": {"post": {"tags": ["Reports"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ReportGenerationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ReportGenerationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ReportGenerationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/Reports/available/{userId}": {"get": {"tags": ["Reports"], "parameters": [{"name": "userId", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Reports/export/{format}": {"get": {"tags": ["Reports"], "parameters": [{"name": "format", "in": "path", "required": true, "schema": {"type": "string"}}, {"name": "cache<PERSON>ey", "in": "query", "schema": {"type": "string"}}, {"name": "userId", "in": "query", "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/Reports/health": {"get": {"tags": ["Reports"], "responses": {"200": {"description": "OK"}}}}, "/": {"get": {"tags": ["SmartBIMicroService.API"], "responses": {"200": {"description": "OK"}}}}, "/index.html": {"get": {"tags": ["SmartBIMicroService.API"], "responses": {"200": {"description": "OK"}}}}, "/Views/{path}": {"get": {"tags": ["SmartBIMicroService.API"], "responses": {"200": {"description": "OK"}}}}, "/api/health": {"get": {"tags": ["SmartBIMicroService.API"], "responses": {"200": {"description": "OK"}}}}, "/api/TradeOps/bills": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "EXP_NO", "in": "query", "schema": {"type": "string"}}, {"name": "REASON_ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "LEGAL_ACTION_INITIATOR_ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "INPUTTER", "in": "query", "schema": {"type": "string"}}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SUPERVISIONSTATUS", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ISREPORTED", "in": "query", "schema": {"type": "boolean"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModelPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModelPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModelPaginatedResult"}}}}}}, "post": {"tags": ["TradeOps"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillCreateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillCreateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillCreateRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TradeOps/bills/consolidated": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "EXP_NO", "in": "query", "schema": {"type": "string"}}, {"name": "REASON_ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "LEGAL_ACTION_INITIATOR_ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "INPUTTER", "in": "query", "schema": {"type": "string"}}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "SUPERVISIONSTATUS", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "ISREPORTED", "in": "query", "schema": {"type": "boolean"}}, {"name": "SearchTerm", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModelPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModelPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModelPaginatedResult"}}}}}}}, "/api/TradeOps/bills/{expNo}": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "expNo", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModel"}}}}}}, "put": {"tags": ["TradeOps"], "parameters": [{"name": "expNo", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillUpdateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillUpdateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillUpdateRequest"}}}}, "responses": {"200": {"description": "OK"}}}, "delete": {"tags": ["TradeOps"], "parameters": [{"name": "expNo", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradeOps/bills/{expNo}/exists": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "expNo", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/TradeOps/bills/pending": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "REASON_ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "LEGAL_ACTION_INITIATOR_ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "INPUTTER", "in": "query", "schema": {"type": "string"}}, {"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOverdueExportBillAuthorizationViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOverdueExportBillAuthorizationViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOverdueExportBillAuthorizationViewModel"}}}}}}}}, "/api/TradeOps/bills/{expNo}/authorize": {"post": {"tags": ["TradeOps"], "parameters": [{"name": "expNo", "in": "path", "required": true, "schema": {"type": "string"}}], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TradeOpsAuthorizationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOpsAuthorizationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TradeOpsAuthorizationRequest"}}}}, "responses": {"200": {"description": "OK"}}}}, "/api/TradeOps/bills/{expNo}/history": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "expNo", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOpsAuthorizationHistoryViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOpsAuthorizationHistoryViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOpsAuthorizationHistoryViewModel"}}}}}}}}, "/api/TradeOps/bills/reporting": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "REASON_ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "LEGAL_ACTION_INITIATOR_ID", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "IncludeReported", "in": "query", "schema": {"type": "boolean"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOverdueExportBillReportingViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOverdueExportBillReportingViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOverdueExportBillReportingViewModel"}}}}}}}}, "/api/TradeOps/export": {"post": {"tags": ["TradeOps"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TradeOpsExportRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOpsExportRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TradeOpsExportRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TradeOpsExportResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TradeOpsExportResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOpsExportResult"}}}}}}}, "/api/TradeOps/download/{fileName}": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "fileName", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK"}}}}, "/api/TradeOps/export/history": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "FromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ToDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "ExportedBy", "in": "query", "schema": {"type": "string"}}, {"name": "Status", "in": "query", "schema": {"type": "string"}}, {"name": "Page", "in": "query", "schema": {"type": "integer", "format": "int32"}}, {"name": "PageSize", "in": "query", "schema": {"type": "integer", "format": "int32"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TradeOpsExportHistoryViewModelPaginatedResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TradeOpsExportHistoryViewModelPaginatedResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOpsExportHistoryViewModelPaginatedResult"}}}}}}}, "/api/TradeOps/reasons": {"get": {"tags": ["TradeOps"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItemViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItemViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItemViewModel"}}}}}}}}, "/api/TradeOps/initiators": {"get": {"tags": ["TradeOps"], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItemViewModel"}}}, "application/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItemViewModel"}}}, "text/json": {"schema": {"type": "array", "items": {"$ref": "#/components/schemas/SelectListItemViewModel"}}}}}}}}, "/api/TradeOps/statistics": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TradeOpsStatisticsViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TradeOpsStatisticsViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOpsStatisticsViewModel"}}}}}}}, "/api/TradeOps/authorization-statistics": {"get": {"tags": ["TradeOps"], "parameters": [{"name": "fromDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}, {"name": "toDate", "in": "query", "schema": {"type": "string", "format": "date-time"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TradeOpsAuthorizationStatisticsViewModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TradeOpsAuthorizationStatisticsViewModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOpsAuthorizationStatisticsViewModel"}}}}}}}, "/api/TradeOps/validate": {"post": {"tags": ["TradeOps"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillCreateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillCreateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/TradeOverdueExportBillCreateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TradeOpsValidationResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TradeOpsValidationResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOpsValidationResult"}}}}}}}, "/api/TradeOps/validate-authorization": {"post": {"tags": ["TradeOps"], "requestBody": {"content": {"application/json": {"schema": {}}, "text/json": {"schema": {}}, "application/*+json": {"schema": {}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/TradeOpsValidationResult"}}, "application/json": {"schema": {"$ref": "#/components/schemas/TradeOpsValidationResult"}}, "text/json": {"schema": {"$ref": "#/components/schemas/TradeOpsValidationResult"}}}}}}}, "/api/TradeOps/bills/generate-legacy-xml": {"post": {"tags": ["TradeOps"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/LegacyXmlGenerationRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LegacyXmlGenerationRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/LegacyXmlGenerationRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "string"}}, "application/json": {"schema": {"type": "string"}}, "text/json": {"schema": {"type": "string"}}}}}}}, "/api/User/authenticate": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/AuthenticateRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/AuthenticateRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/AuthenticateRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/LoginModels"}}, "application/json": {"schema": {"$ref": "#/components/schemas/LoginModels"}}, "text/json": {"schema": {"$ref": "#/components/schemas/LoginModels"}}}}}}}, "/api/User/by-username/{username}": {"get": {"tags": ["User"], "parameters": [{"name": "username", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserModel"}}}}}}}, "/api/User/by-email/{email}": {"get": {"tags": ["User"], "parameters": [{"name": "email", "in": "path", "required": true, "schema": {"type": "string"}}], "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"$ref": "#/components/schemas/UserModel"}}, "application/json": {"schema": {"$ref": "#/components/schemas/UserModel"}}, "text/json": {"schema": {"$ref": "#/components/schemas/UserModel"}}}}}}}, "/api/User/change-password": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ChangePasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}, "/api/User/reset-password": {"post": {"tags": ["User"], "requestBody": {"content": {"application/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "text/json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}, "application/*+json": {"schema": {"$ref": "#/components/schemas/ResetPasswordRequest"}}}}, "responses": {"200": {"description": "OK", "content": {"text/plain": {"schema": {"type": "boolean"}}, "application/json": {"schema": {"type": "boolean"}}, "text/json": {"schema": {"type": "boolean"}}}}}}}}, "components": {"schemas": {"Assembly": {"type": "object", "properties": {"DefinedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/TypeInfo"}, "nullable": true, "readOnly": true}, "ExportedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "CodeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "EntryPoint": {"$ref": "#/components/schemas/MethodInfo"}, "FullName": {"type": "string", "nullable": true, "readOnly": true}, "ImageRuntimeVersion": {"type": "string", "nullable": true, "readOnly": true}, "IsDynamic": {"type": "boolean", "readOnly": true}, "Location": {"type": "string", "nullable": true, "readOnly": true}, "ReflectionOnly": {"type": "boolean", "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "IsFullyTrusted": {"type": "boolean", "readOnly": true}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "EscapedCodeBase": {"type": "string", "nullable": true, "readOnly": true, "deprecated": true}, "ManifestModule": {"$ref": "#/components/schemas/Module"}, "Modules": {"type": "array", "items": {"$ref": "#/components/schemas/Module"}, "nullable": true, "readOnly": true}, "GlobalAssemblyCache": {"type": "boolean", "readOnly": true, "deprecated": true}, "HostContext": {"type": "integer", "format": "int64", "readOnly": true}, "SecurityRuleSet": {"$ref": "#/components/schemas/SecurityRuleSet"}}, "additionalProperties": false}, "AuthenticateRequest": {"type": "object", "properties": {"Username": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Calendar": {"type": "object", "properties": {"MinSupportedDateTime": {"type": "string", "format": "date-time", "readOnly": true}, "MaxSupportedDateTime": {"type": "string", "format": "date-time", "readOnly": true}, "AlgorithmType": {"$ref": "#/components/schemas/CalendarAlgorithmType"}, "IsReadOnly": {"type": "boolean", "readOnly": true}, "Eras": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true, "readOnly": true}, "TwoDigitYearMax": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "CalendarAlgorithmType": {"enum": [0, 1, 2, 3], "type": "integer", "format": "int32"}, "CalendarWeekRule": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "CallingConventions": {"enum": [1, 2, 3, 32, 64], "type": "integer", "format": "int32"}, "ChangePasswordRequest": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "OldPassword": {"type": "string", "nullable": true}, "NewPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "CompareInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "Version": {"$ref": "#/components/schemas/SortVersion"}, "LCID": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "ConstructorInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "Attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "MethodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "CallingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsConstructor": {"type": "boolean", "readOnly": true}, "IsFinal": {"type": "boolean", "readOnly": true}, "IsHideBySig": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsStatic": {"type": "boolean", "readOnly": true}, "IsVirtual": {"type": "boolean", "readOnly": true}, "IsAssembly": {"type": "boolean", "readOnly": true}, "IsFamily": {"type": "boolean", "readOnly": true}, "IsFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "IsFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "IsPrivate": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "MethodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}}, "additionalProperties": false}, "CultureInfo": {"type": "object", "properties": {"Parent": {"$ref": "#/components/schemas/CultureInfo"}, "LCID": {"type": "integer", "format": "int32", "readOnly": true}, "KeyboardLayoutId": {"type": "integer", "format": "int32", "readOnly": true}, "Name": {"type": "string", "nullable": true}, "IetfLanguageTag": {"type": "string", "nullable": true, "readOnly": true}, "DisplayName": {"type": "string", "nullable": true, "readOnly": true}, "NativeName": {"type": "string", "nullable": true, "readOnly": true}, "EnglishName": {"type": "string", "nullable": true, "readOnly": true}, "TwoLetterISOLanguageName": {"type": "string", "nullable": true, "readOnly": true}, "ThreeLetterISOLanguageName": {"type": "string", "nullable": true, "readOnly": true}, "ThreeLetterWindowsLanguageName": {"type": "string", "nullable": true, "readOnly": true}, "CompareInfo": {"$ref": "#/components/schemas/CompareInfo"}, "TextInfo": {"$ref": "#/components/schemas/TextInfo"}, "IsNeutralCulture": {"type": "boolean", "readOnly": true}, "CultureTypes": {"$ref": "#/components/schemas/CultureTypes"}, "NumberFormat": {"$ref": "#/components/schemas/NumberFormatInfo"}, "DateTimeFormat": {"$ref": "#/components/schemas/DateTimeFormatInfo"}, "Calendar": {"$ref": "#/components/schemas/Calendar"}, "OptionalCalendars": {"type": "array", "items": {"$ref": "#/components/schemas/Calendar"}, "nullable": true, "readOnly": true}, "UseUserOverride": {"type": "boolean", "readOnly": true}, "IsReadOnly": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "CultureTypes": {"enum": [1, 2, 4, 7, 8, 16, 32, 64], "type": "integer", "format": "int32"}, "CustomAttributeData": {"type": "object", "properties": {"AttributeType": {"$ref": "#/components/schemas/Type"}, "Constructor": {"$ref": "#/components/schemas/ConstructorInfo"}, "ConstructorArguments": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeTypedArgument"}, "nullable": true, "readOnly": true}, "NamedArguments": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeNamedArgument"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "CustomAttributeNamedArgument": {"type": "object", "properties": {"MemberInfo": {"$ref": "#/components/schemas/MemberInfo"}, "TypedValue": {"$ref": "#/components/schemas/CustomAttributeTypedArgument"}, "MemberName": {"type": "string", "nullable": true, "readOnly": true}, "IsField": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "CustomAttributeTypedArgument": {"type": "object", "properties": {"ArgumentType": {"$ref": "#/components/schemas/Type"}, "Value": {"nullable": true}}, "additionalProperties": false}, "DailyUploadStat": {"type": "object", "properties": {"Date": {"type": "string", "format": "date-time"}, "Count": {"type": "integer", "format": "int32"}, "TotalSize": {"type": "integer", "format": "int64"}}, "additionalProperties": false}, "DataColumn": {"type": "object", "properties": {"Site": {"$ref": "#/components/schemas/ISite"}, "Container": {"$ref": "#/components/schemas/IContainer"}, "DesignMode": {"type": "boolean", "readOnly": true}, "AllowDBNull": {"type": "boolean", "default": true}, "AutoIncrement": {"type": "boolean", "default": false}, "AutoIncrementSeed": {"type": "integer", "format": "int64", "default": 0}, "AutoIncrementStep": {"type": "integer", "format": "int64", "default": 1}, "Caption": {"type": "string", "nullable": true}, "ColumnName": {"type": "string", "default": "", "nullable": true}, "Prefix": {"type": "string", "default": "", "nullable": true}, "DataType": {"$ref": "#/components/schemas/Type"}, "DateTimeMode": {"$ref": "#/components/schemas/DataSetDateTime"}, "DefaultValue": {"nullable": true}, "Expression": {"type": "string", "default": "", "nullable": true}, "ExtendedProperties": {"type": "object", "additionalProperties": {}, "nullable": true, "readOnly": true}, "MaxLength": {"type": "integer", "format": "int32", "default": -1}, "Namespace": {"type": "string", "nullable": true}, "Ordinal": {"type": "integer", "format": "int32", "readOnly": true}, "ReadOnly": {"type": "boolean", "default": false}, "Table": {"$ref": "#/components/schemas/DataTable"}, "Unique": {"type": "boolean", "default": false}, "ColumnMapping": {"$ref": "#/components/schemas/MappingType"}}, "additionalProperties": false}, "DataSet": {"type": "object", "properties": {"Container": {"$ref": "#/components/schemas/IContainer"}, "DesignMode": {"type": "boolean", "readOnly": true}, "RemotingFormat": {"$ref": "#/components/schemas/SerializationFormat"}, "SchemaSerializationMode": {"$ref": "#/components/schemas/SchemaSerializationMode"}, "CaseSensitive": {"type": "boolean", "default": false}, "DefaultViewManager": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "EnforceConstraints": {"type": "boolean", "default": true}, "DataSetName": {"type": "string", "default": "", "nullable": true}, "Namespace": {"type": "string", "default": "", "nullable": true}, "Prefix": {"type": "string", "default": "", "nullable": true}, "ExtendedProperties": {"type": "object", "additionalProperties": {}, "nullable": true, "readOnly": true}, "HasErrors": {"type": "boolean", "readOnly": true}, "IsInitialized": {"type": "boolean", "readOnly": true}, "Locale": {"$ref": "#/components/schemas/CultureInfo"}, "Site": {"$ref": "#/components/schemas/ISite"}, "Relations": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "Tables": {"type": "array", "items": {}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "DataSetDateTime": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "DataTable": {"type": "object", "properties": {"Container": {"$ref": "#/components/schemas/IContainer"}, "DesignMode": {"type": "boolean", "readOnly": true}, "CaseSensitive": {"type": "boolean"}, "IsInitialized": {"type": "boolean", "readOnly": true}, "RemotingFormat": {"$ref": "#/components/schemas/SerializationFormat"}, "ChildRelations": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "Columns": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "Constraints": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "DataSet": {"$ref": "#/components/schemas/DataSet"}, "DefaultView": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "DisplayExpression": {"type": "string", "default": "", "nullable": true}, "ExtendedProperties": {"type": "object", "additionalProperties": {}, "nullable": true, "readOnly": true}, "HasErrors": {"type": "boolean", "readOnly": true}, "Locale": {"$ref": "#/components/schemas/CultureInfo"}, "MinimumCapacity": {"type": "integer", "format": "int32", "default": 50}, "ParentRelations": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "PrimaryKey": {"type": "array", "items": {"$ref": "#/components/schemas/DataColumn"}, "nullable": true}, "Rows": {"type": "array", "items": {}, "nullable": true, "readOnly": true}, "TableName": {"type": "string", "default": "", "nullable": true}, "Namespace": {"type": "string", "nullable": true}, "Prefix": {"type": "string", "default": "", "nullable": true}, "Site": {"$ref": "#/components/schemas/ISite"}}, "additionalProperties": false}, "DateTimeFormatInfo": {"type": "object", "properties": {"AMDesignator": {"type": "string", "nullable": true}, "Calendar": {"$ref": "#/components/schemas/Calendar"}, "DateSeparator": {"type": "string", "nullable": true}, "FirstDayOfWeek": {"$ref": "#/components/schemas/DayOfWeek"}, "CalendarWeekRule": {"$ref": "#/components/schemas/CalendarWeekRule"}, "FullDateTimePattern": {"type": "string", "nullable": true}, "LongDatePattern": {"type": "string", "nullable": true}, "LongTimePattern": {"type": "string", "nullable": true}, "MonthDayPattern": {"type": "string", "nullable": true}, "PMDesignator": {"type": "string", "nullable": true}, "RFC1123Pattern": {"type": "string", "nullable": true, "readOnly": true}, "ShortDatePattern": {"type": "string", "nullable": true}, "ShortTimePattern": {"type": "string", "nullable": true}, "SortableDateTimePattern": {"type": "string", "nullable": true, "readOnly": true}, "TimeSeparator": {"type": "string", "nullable": true}, "UniversalSortableDateTimePattern": {"type": "string", "nullable": true, "readOnly": true}, "YearMonthPattern": {"type": "string", "nullable": true}, "AbbreviatedDayNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "ShortestDayNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "DayNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "AbbreviatedMonthNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "MonthNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "IsReadOnly": {"type": "boolean", "readOnly": true}, "NativeCalendarName": {"type": "string", "nullable": true, "readOnly": true}, "AbbreviatedMonthGenitiveNames": {"type": "array", "items": {"type": "string"}, "nullable": true}, "MonthGenitiveNames": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "DayOfWeek": {"enum": [0, 1, 2, 3, 4, 5, 6], "type": "integer", "format": "int32"}, "DigitShapes": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "EventAttributes": {"enum": [0, 512, 1024], "type": "integer", "format": "int32"}, "EventInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Attributes": {"$ref": "#/components/schemas/EventAttributes"}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "AddMethod": {"$ref": "#/components/schemas/MethodInfo"}, "RemoveMethod": {"$ref": "#/components/schemas/MethodInfo"}, "RaiseMethod": {"$ref": "#/components/schemas/MethodInfo"}, "IsMulticast": {"type": "boolean", "readOnly": true}, "EventHandlerType": {"$ref": "#/components/schemas/Type"}}, "additionalProperties": false}, "FCCM_ALERT_DASHBOARD": {"type": "object", "properties": {"TOTAL_ALERTS": {"type": "integer", "format": "int32"}, "TOTAL_PENDING": {"type": "integer", "format": "int32"}, "TOTAL_CLOSED": {"type": "integer", "format": "int32"}, "PERCENTAGE": {"type": "number", "format": "double"}}, "additionalProperties": false}, "FieldAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 16, 32, 64, 128, 256, 512, 1024, 4096, 8192, 32768, 38144], "type": "integer", "format": "int32"}, "FieldInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Attributes": {"$ref": "#/components/schemas/FieldAttributes"}, "FieldType": {"$ref": "#/components/schemas/Type"}, "IsInitOnly": {"type": "boolean", "readOnly": true}, "IsLiteral": {"type": "boolean", "readOnly": true}, "IsNotSerialized": {"type": "boolean", "readOnly": true, "deprecated": true}, "IsPinvokeImpl": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsStatic": {"type": "boolean", "readOnly": true}, "IsAssembly": {"type": "boolean", "readOnly": true}, "IsFamily": {"type": "boolean", "readOnly": true}, "IsFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "IsFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "IsPrivate": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "FieldHandle": {"$ref": "#/components/schemas/RuntimeFieldHandle"}}, "additionalProperties": false}, "FileUploadAuthorizationRequest": {"type": "object", "properties": {"Id": {"type": "number", "format": "double"}, "SupervisedBy": {"type": "string", "nullable": true}, "SupervisionStatus": {"type": "integer", "format": "int32"}, "Comments": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileUploadAuthorizationViewModel": {"required": ["NewSupervisionStatus"], "type": "object", "properties": {"Id": {"type": "number", "format": "double"}, "FileName": {"type": "string", "nullable": true}, "UploadedBy": {"type": "string", "nullable": true}, "UploadTime": {"type": "string", "format": "date-time"}, "DataType": {"type": "string", "nullable": true}, "FileSize": {"type": "integer", "format": "int64"}, "Description": {"type": "string", "nullable": true}, "IsSupervised": {"type": "boolean"}, "SupervisionStatus": {"type": "integer", "format": "int32"}, "SupervisedBy": {"type": "string", "nullable": true}, "SupervisionDate": {"type": "string", "format": "date-time", "nullable": true}, "SupervisionComments": {"type": "string", "nullable": true}, "NewSupervisionStatus": {"type": "integer", "format": "int32"}, "Comments": {"maxLength": 1000, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "FileUploadHistoryViewModel": {"type": "object", "properties": {"Id": {"type": "number", "format": "double"}, "FileName": {"type": "string", "nullable": true}, "UploadedBy": {"type": "string", "nullable": true}, "UploadTime": {"type": "string", "format": "date-time"}, "DataType": {"type": "string", "nullable": true}, "FileSize": {"type": "integer", "format": "int64"}, "FormattedFileSize": {"type": "string", "nullable": true}, "IsSupervised": {"type": "boolean"}, "SupervisionStatusText": {"type": "string", "nullable": true}, "SupervisedBy": {"type": "string", "nullable": true}, "SupervisionDate": {"type": "string", "format": "date-time", "nullable": true}, "IsDataProcessed": {"type": "boolean"}, "DataProcessMsg": {"type": "string", "nullable": true}, "DataProcessedTime": {"type": "string", "format": "date-time", "nullable": true}, "StatusBadgeClass": {"type": "string", "nullable": true}, "StatusIcon": {"type": "string", "nullable": true}}, "additionalProperties": false}, "FileUploadHistoryViewModelPaginatedResult": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/components/schemas/FileUploadHistoryViewModel"}, "nullable": true}, "TotalCount": {"type": "integer", "format": "int32"}, "Page": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "TotalPages": {"type": "integer", "format": "int32", "readOnly": true}, "HasPreviousPage": {"type": "boolean", "readOnly": true}, "HasNextPage": {"type": "boolean", "readOnly": true}, "StartIndex": {"type": "integer", "format": "int32", "readOnly": true}, "EndIndex": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "FileUploadResponse": {"type": "object", "properties": {"Success": {"type": "boolean"}, "Message": {"type": "string", "nullable": true}, "FileUploadId": {"type": "number", "format": "double", "nullable": true}, "FileName": {"type": "string", "nullable": true}, "FileSize": {"type": "integer", "format": "int64"}, "DataType": {"type": "string", "nullable": true}, "RequiresApproval": {"type": "boolean"}, "Errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "FileUploadStatistics": {"type": "object", "properties": {"TotalUploads": {"type": "integer", "format": "int32"}, "PendingApproval": {"type": "integer", "format": "int32"}, "ApprovedUploads": {"type": "integer", "format": "int32"}, "DeclinedUploads": {"type": "integer", "format": "int32"}, "ProcessedFiles": {"type": "integer", "format": "int32"}, "FailedProcessing": {"type": "integer", "format": "int32"}, "TotalFileSize": {"type": "integer", "format": "int64"}, "FormattedTotalFileSize": {"type": "string", "nullable": true}, "UploadsByDataType": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "UploadsByUser": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "DailyStats": {"type": "array", "items": {"$ref": "#/components/schemas/DailyUploadStat"}, "nullable": true}}, "additionalProperties": false}, "FileUploadValidationResult": {"type": "object", "properties": {"IsValid": {"type": "boolean"}, "Errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}, "MaxAllowedSize": {"type": "integer", "format": "int64"}, "AllowedExtensions": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "GenericParameterAttributes": {"enum": [0, 1, 2, 3, 4, 8, 16, 28], "type": "integer", "format": "int32"}, "IComponent": {"type": "object", "properties": {"Site": {"$ref": "#/components/schemas/ISite"}}, "additionalProperties": false}, "IContainer": {"type": "object", "properties": {"Components": {"type": "array", "items": {}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "ICustomAttributeProvider": {"type": "object", "additionalProperties": false}, "ISite": {"type": "object", "properties": {"Component": {"$ref": "#/components/schemas/IComponent"}, "Container": {"$ref": "#/components/schemas/IContainer"}, "DesignMode": {"type": "boolean", "readOnly": true}, "Name": {"type": "string", "nullable": true}}, "additionalProperties": false}, "IntPtr": {"type": "object", "additionalProperties": false}, "LayoutKind": {"enum": [0, 2, 3], "type": "integer", "format": "int32"}, "LegacyXmlGenerationRequest": {"type": "object", "properties": {"FolderPath": {"type": "string", "nullable": true}, "Bills": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "LoginModels": {"required": ["Password", "UserId", "UserName"], "type": "object", "properties": {"UserId": {"minLength": 1, "type": "string"}, "UserName": {"minLength": 1, "type": "string"}, "Password": {"minLength": 1, "type": "string", "format": "password"}, "RememberMe": {"type": "boolean"}, "UserRoleId": {"type": "string", "nullable": true}, "RoleName": {"type": "string", "nullable": true}, "IsEnabled": {"type": "boolean"}, "ApprovalStatus": {"type": "integer", "format": "int32"}, "IsApproved": {"type": "boolean"}, "IsFirstLogin": {"type": "boolean"}, "Id": {"type": "integer", "format": "int32"}, "FullName": {"type": "string", "nullable": true}, "BranchID": {"type": "string", "nullable": true}, "BranchName": {"type": "string", "nullable": true}, "EmailAddress": {"type": "string", "nullable": true}, "RoleID": {"type": "integer", "format": "int32"}, "EmployeeCode": {"type": "string", "nullable": true}}, "additionalProperties": false}, "MappingType": {"enum": [1, 2, 3, 4], "type": "integer", "format": "int32"}, "MemberInfo": {"type": "object", "properties": {"MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "MemberTypes": {"enum": [1, 2, 4, 8, 16, 32, 64, 128, 191], "type": "integer", "format": "int32"}, "MenuItemViewModel": {"type": "object", "properties": {"MainMenuId": {"type": "string", "nullable": true}, "MainMenuName": {"type": "string", "nullable": true}, "MainMenuIcon": {"type": "string", "nullable": true}, "SubMenus": {"type": "array", "items": {"$ref": "#/components/schemas/SubMenuViewModel"}, "nullable": true}}, "additionalProperties": false}, "MethodAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192, 16384, 32768, 53248], "type": "integer", "format": "int32"}, "MethodBase": {"type": "object", "properties": {"MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "Attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "MethodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "CallingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsConstructor": {"type": "boolean", "readOnly": true}, "IsFinal": {"type": "boolean", "readOnly": true}, "IsHideBySig": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsStatic": {"type": "boolean", "readOnly": true}, "IsVirtual": {"type": "boolean", "readOnly": true}, "IsAssembly": {"type": "boolean", "readOnly": true}, "IsFamily": {"type": "boolean", "readOnly": true}, "IsFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "IsFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "IsPrivate": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "MethodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "MethodImplAttributes": {"enum": [0, 1, 2, 3, 4, 8, 16, 32, 64, 128, 256, 512, 4096, 65535], "type": "integer", "format": "int32"}, "MethodInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "Attributes": {"$ref": "#/components/schemas/MethodAttributes"}, "MethodImplementationFlags": {"$ref": "#/components/schemas/MethodImplAttributes"}, "CallingConvention": {"$ref": "#/components/schemas/CallingConventions"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsConstructor": {"type": "boolean", "readOnly": true}, "IsFinal": {"type": "boolean", "readOnly": true}, "IsHideBySig": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsStatic": {"type": "boolean", "readOnly": true}, "IsVirtual": {"type": "boolean", "readOnly": true}, "IsAssembly": {"type": "boolean", "readOnly": true}, "IsFamily": {"type": "boolean", "readOnly": true}, "IsFamilyAndAssembly": {"type": "boolean", "readOnly": true}, "IsFamilyOrAssembly": {"type": "boolean", "readOnly": true}, "IsPrivate": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsConstructedGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethod": {"type": "boolean", "readOnly": true}, "IsGenericMethodDefinition": {"type": "boolean", "readOnly": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "MethodHandle": {"$ref": "#/components/schemas/RuntimeMethodHandle"}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "ReturnParameter": {"$ref": "#/components/schemas/ParameterInfo"}, "ReturnType": {"$ref": "#/components/schemas/Type"}, "ReturnTypeCustomAttributes": {"$ref": "#/components/schemas/ICustomAttributeProvider"}}, "additionalProperties": false}, "Module": {"type": "object", "properties": {"Assembly": {"$ref": "#/components/schemas/Assembly"}, "FullyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "Name": {"type": "string", "nullable": true, "readOnly": true}, "MDStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}, "ModuleVersionId": {"type": "string", "format": "uuid", "readOnly": true}, "ScopeName": {"type": "string", "nullable": true, "readOnly": true}, "ModuleHandle": {"$ref": "#/components/schemas/ModuleHandle"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "ModuleHandle": {"type": "object", "properties": {"MDStreamVersion": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "NumberFormatInfo": {"type": "object", "properties": {"CurrencyDecimalDigits": {"type": "integer", "format": "int32"}, "CurrencyDecimalSeparator": {"type": "string", "nullable": true}, "IsReadOnly": {"type": "boolean", "readOnly": true}, "CurrencyGroupSizes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "NumberGroupSizes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "PercentGroupSizes": {"type": "array", "items": {"type": "integer", "format": "int32"}, "nullable": true}, "CurrencyGroupSeparator": {"type": "string", "nullable": true}, "CurrencySymbol": {"type": "string", "nullable": true}, "NaNSymbol": {"type": "string", "nullable": true}, "CurrencyNegativePattern": {"type": "integer", "format": "int32"}, "NumberNegativePattern": {"type": "integer", "format": "int32"}, "PercentPositivePattern": {"type": "integer", "format": "int32"}, "PercentNegativePattern": {"type": "integer", "format": "int32"}, "NegativeInfinitySymbol": {"type": "string", "nullable": true}, "NegativeSign": {"type": "string", "nullable": true}, "NumberDecimalDigits": {"type": "integer", "format": "int32"}, "NumberDecimalSeparator": {"type": "string", "nullable": true}, "NumberGroupSeparator": {"type": "string", "nullable": true}, "CurrencyPositivePattern": {"type": "integer", "format": "int32"}, "PositiveInfinitySymbol": {"type": "string", "nullable": true}, "PositiveSign": {"type": "string", "nullable": true}, "PercentDecimalDigits": {"type": "integer", "format": "int32"}, "PercentDecimalSeparator": {"type": "string", "nullable": true}, "PercentGroupSeparator": {"type": "string", "nullable": true}, "PercentSymbol": {"type": "string", "nullable": true}, "PerMilleSymbol": {"type": "string", "nullable": true}, "NativeDigits": {"type": "array", "items": {"type": "string"}, "nullable": true}, "DigitSubstitution": {"$ref": "#/components/schemas/DigitShapes"}}, "additionalProperties": false}, "ParameterAttributes": {"enum": [0, 1, 2, 4, 8, 16, 4096, 8192, 16384, 32768, 61440], "type": "integer", "format": "int32"}, "ParameterInfo": {"type": "object", "properties": {"Attributes": {"$ref": "#/components/schemas/ParameterAttributes"}, "Member": {"$ref": "#/components/schemas/MemberInfo"}, "Name": {"type": "string", "nullable": true, "readOnly": true}, "ParameterType": {"$ref": "#/components/schemas/Type"}, "Position": {"type": "integer", "format": "int32", "readOnly": true}, "IsIn": {"type": "boolean", "readOnly": true}, "IsLcid": {"type": "boolean", "readOnly": true}, "IsOptional": {"type": "boolean", "readOnly": true}, "IsOut": {"type": "boolean", "readOnly": true}, "IsRetval": {"type": "boolean", "readOnly": true}, "DefaultValue": {"nullable": true, "readOnly": true}, "RawDefaultValue": {"nullable": true, "readOnly": true}, "HasDefaultValue": {"type": "boolean", "readOnly": true}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "PrivilegeUpdateItem": {"type": "object", "properties": {"MenuId": {"type": "string", "nullable": true}, "SubMenuId": {"type": "string", "nullable": true}, "CanView": {"type": "boolean"}, "CanAdd": {"type": "boolean"}, "CanEdit": {"type": "boolean"}, "CanDelete": {"type": "boolean"}}, "additionalProperties": false}, "PropertyAttributes": {"enum": [0, 512, 1024, 4096, 8192, 16384, 32768, 62464], "type": "integer", "format": "int32"}, "PropertyInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "Module": {"$ref": "#/components/schemas/Module"}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "PropertyType": {"$ref": "#/components/schemas/Type"}, "Attributes": {"$ref": "#/components/schemas/PropertyAttributes"}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "CanRead": {"type": "boolean", "readOnly": true}, "CanWrite": {"type": "boolean", "readOnly": true}, "GetMethod": {"$ref": "#/components/schemas/MethodInfo"}, "SetMethod": {"$ref": "#/components/schemas/MethodInfo"}}, "additionalProperties": false}, "RM_ATTRITION_GROWTH_SUMMARY": {"type": "object", "properties": {"RM_CODE": {"type": "string", "nullable": true}, "PRODUCT_TYPE": {"type": "string", "nullable": true}, "ATTRITION": {"type": "number", "format": "double"}, "GROWTH": {"type": "number", "format": "double"}, "NET_GROWTH": {"type": "number", "format": "double"}, "PRIORITY": {"type": "integer", "format": "int32"}}, "additionalProperties": false}, "RM_PERFORMANCE_DASHBOARD": {"type": "object", "properties": {"RM_CODE": {"type": "string", "nullable": true}, "RM_NAME": {"type": "string", "nullable": true}, "CA_AC": {"type": "integer", "format": "int32"}, "SA_AC": {"type": "integer", "format": "int32"}, "FDR_AC": {"type": "integer", "format": "int32"}, "DPS_AC": {"type": "integer", "format": "int32"}, "CA_BAL": {"type": "number", "format": "double"}, "SA_BAL": {"type": "number", "format": "double"}, "FDR_BAL": {"type": "number", "format": "double"}, "DPS_BAL": {"type": "number", "format": "double"}, "CA_AVG_BAL": {"type": "number", "format": "double"}, "SA_AVG_BAL": {"type": "number", "format": "double"}, "FDR_AVG_BAL": {"type": "number", "format": "double"}, "DPS_AVG_BAL": {"type": "number", "format": "double"}, "NO_OF_DORMANT_ACCOUNTS": {"type": "integer", "format": "int32"}, "BALANCE_OF_DORMANT_ACCOUNTS": {"type": "number", "format": "double"}, "NO_OF_ZERO_ACCOUNTS": {"type": "integer", "format": "int32"}, "BALANCE_OF_ZERO_ACCOUNTS": {"type": "number", "format": "double"}, "TOTAL_AC": {"type": "integer", "format": "int32"}, "TOTAL_BAL": {"type": "number", "format": "double"}, "AVG_BAL": {"type": "number", "format": "double"}, "COD": {"type": "number", "format": "double"}}, "additionalProperties": false}, "RM_PERFORMANCE_GROWTH": {"type": "object", "properties": {"SL": {"type": "integer", "format": "int32"}, "PSL": {"type": "integer", "format": "int32"}, "GROWTH_TYPE": {"type": "string", "nullable": true}, "PRODUCT_TYPE": {"type": "string", "nullable": true}, "RM_CODE": {"type": "string", "nullable": true}, "NO_OF_ACCOUNS": {"type": "integer", "format": "int32"}, "TOTAL_BALANCES": {"type": "number", "format": "double"}}, "additionalProperties": false}, "RM_TARGET_VS_ACHIEVEMENT": {"type": "object", "properties": {"RM_CODE": {"type": "string", "nullable": true}, "MONTHLY_TARGET_ACCOUNTS": {"type": "integer", "format": "int32"}, "MONTHLY_TARGET_AMOUNTS": {"type": "number", "format": "double"}, "TARGET_YEAR": {"type": "integer", "format": "int32"}, "YTD_TARGET_ACCOUNTS": {"type": "integer", "format": "int32"}, "YEARLY_TARGET_ACCOUNTS": {"type": "integer", "format": "int32"}, "YEARLY_TARGET_AMOUNTS": {"type": "number", "format": "double"}, "YTD_TARGET_AMOUNTS": {"type": "number", "format": "double"}, "YTD_ACHIEVED_ACCOUNTS": {"type": "integer", "format": "int32"}, "YTD_ACHIEVED_AMOUNTS": {"type": "number", "format": "double"}, "MTD_ACHIEVED_ACCOUNTS": {"type": "integer", "format": "int32"}, "MTD_ACHIEVED_AMOUNTS": {"type": "number", "format": "double"}, "MTD_VARIANCE_ACCOUNTS": {"type": "integer", "format": "int32"}, "MTD_VARIANCE_AMOUNS": {"type": "number", "format": "double"}, "YTD_VARIANCE_ACCOUNTS": {"type": "integer", "format": "int32"}, "YTD_VARIANCE_AMOUNTS": {"type": "number", "format": "double"}, "ACC_PERFORMANCE": {"type": "number", "format": "double"}, "VOL_PERFORMANCE": {"type": "number", "format": "double"}}, "additionalProperties": false}, "ReportGenerationRequest": {"required": ["Parameters", "ReportSlNo"], "type": "object", "properties": {"ReportSlNo": {"minLength": 1, "type": "string"}, "Parameters": {"type": "object", "additionalProperties": {"type": "string"}}, "Format": {"type": "string", "nullable": true}, "UserId": {"type": "string", "nullable": true}, "SessionId": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ReportMetadata": {"type": "object", "properties": {"SlNo": {"type": "string", "nullable": true}, "ReportName": {"type": "string", "nullable": true}, "QryType": {"type": "string", "nullable": true}, "Qryname": {"type": "string", "nullable": true}, "Para1": {"type": "string", "nullable": true}, "Para2": {"type": "string", "nullable": true}, "Para3": {"type": "string", "nullable": true}, "Para4": {"type": "string", "nullable": true}, "Para5": {"type": "string", "nullable": true}, "Para6": {"type": "string", "nullable": true}}, "additionalProperties": false}, "ReportPrivilegeItem": {"type": "object", "properties": {"RoleId": {"type": "integer", "format": "int32"}, "ReportId": {"type": "string", "nullable": true}, "Status": {"type": "boolean"}}, "additionalProperties": false}, "ReportRequest": {"type": "object", "properties": {"ReportSlNo": {"type": "string", "nullable": true}, "Parameters": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}, "Format": {"type": "string", "nullable": true}, "ReportData": {"$ref": "#/components/schemas/DataTable"}, "SessionId": {"type": "string", "nullable": true}, "UserId": {"type": "string", "nullable": true}, "Metadata": {"$ref": "#/components/schemas/ReportMetadata"}}, "additionalProperties": false}, "ResetPasswordRequest": {"type": "object", "properties": {"Username": {"type": "string", "nullable": true}, "NewPassword": {"type": "string", "nullable": true}}, "additionalProperties": false}, "Role": {"required": ["RoleName"], "type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "RoleName": {"maxLength": 50, "minLength": 0, "type": "string"}, "RoleCode": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}, "ControllerName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "ActionName": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "RuntimeFieldHandle": {"type": "object", "properties": {"Value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "RuntimeMethodHandle": {"type": "object", "properties": {"Value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "RuntimeTypeHandle": {"type": "object", "properties": {"Value": {"$ref": "#/components/schemas/IntPtr"}}, "additionalProperties": false}, "SchemaSerializationMode": {"enum": [1, 2], "type": "integer", "format": "int32"}, "SecurityRuleSet": {"enum": [0, 1, 2], "type": "integer", "format": "int32"}, "SelectListItemViewModel": {"type": "object", "properties": {"Value": {"type": "string", "nullable": true}, "Text": {"type": "string", "nullable": true}, "Selected": {"type": "boolean"}}, "additionalProperties": false}, "SerializationFormat": {"enum": [0, 1], "type": "integer", "format": "int32"}, "SortVersion": {"type": "object", "properties": {"FullVersion": {"type": "integer", "format": "int32"}, "SortId": {"type": "string", "format": "uuid"}}, "additionalProperties": false}, "StructLayoutAttribute": {"type": "object", "properties": {"TypeId": {"nullable": true, "readOnly": true}, "Value": {"$ref": "#/components/schemas/LayoutKind"}}, "additionalProperties": false}, "SubMenuViewModel": {"type": "object", "properties": {"SubMenuId": {"type": "string", "nullable": true}, "SubMenuName": {"type": "string", "nullable": true}, "ControllerName": {"type": "string", "nullable": true}, "ActionName": {"type": "string", "nullable": true}, "IsActive": {"type": "boolean"}}, "additionalProperties": false}, "TextInfo": {"type": "object", "properties": {"ANSICodePage": {"type": "integer", "format": "int32", "readOnly": true}, "OEMCodePage": {"type": "integer", "format": "int32", "readOnly": true}, "MacCodePage": {"type": "integer", "format": "int32", "readOnly": true}, "EBCDICCodePage": {"type": "integer", "format": "int32", "readOnly": true}, "LCID": {"type": "integer", "format": "int32", "readOnly": true}, "CultureName": {"type": "string", "nullable": true, "readOnly": true}, "IsReadOnly": {"type": "boolean", "readOnly": true}, "ListSeparator": {"type": "string", "nullable": true}, "IsRightToLeft": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "TradeOpsAuthorizationHistoryViewModel": {"type": "object", "properties": {"EXP_NO": {"type": "string", "nullable": true}, "SUPERVISEDBY": {"type": "string", "nullable": true}, "SUPERVISION_TIME": {"type": "string", "format": "date-time", "nullable": true}, "SUPERVISIONSTATUS": {"type": "integer", "format": "int32", "nullable": true}, "Comments": {"type": "string", "nullable": true}, "StatusText": {"type": "string", "nullable": true}, "FormattedSupervisionTime": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TradeOpsAuthorizationRequest": {"required": ["EXP_NO", "SupervisedBy", "SupervisionStatus"], "type": "object", "properties": {"EXP_NO": {"minLength": 1, "type": "string"}, "SupervisedBy": {"minLength": 1, "type": "string"}, "SupervisionStatus": {"maximum": 2, "minimum": 1, "type": "integer", "format": "int32"}, "Comments": {"maxLength": 500, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "TradeOpsAuthorizationStatisticsViewModel": {"type": "object", "properties": {"TotalAuthorizations": {"type": "integer", "format": "int32"}, "ApprovedCount": {"type": "integer", "format": "int32"}, "DeclinedCount": {"type": "integer", "format": "int32"}, "PendingCount": {"type": "integer", "format": "int32"}, "AuthorizationsBySupervisor": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "AuthorizationsByReason": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "AuthorizationsByInitiator": {"type": "object", "additionalProperties": {"type": "integer", "format": "int32"}, "nullable": true}, "ApprovalPercentage": {"type": "number", "format": "double", "readOnly": true}, "DeclinePercentage": {"type": "number", "format": "double", "readOnly": true}}, "additionalProperties": false}, "TradeOpsExportHistoryViewModel": {"type": "object", "properties": {"Id": {"type": "integer", "format": "int32"}, "FileName": {"type": "string", "nullable": true}, "ExportDate": {"type": "string", "format": "date-time"}, "ExportedBy": {"type": "string", "nullable": true}, "BillCount": {"type": "integer", "format": "int32"}, "Status": {"type": "string", "nullable": true}, "FormattedExportDate": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TradeOpsExportHistoryViewModelPaginatedResult": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOpsExportHistoryViewModel"}, "nullable": true}, "TotalCount": {"type": "integer", "format": "int32"}, "Page": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "TotalPages": {"type": "integer", "format": "int32", "readOnly": true}, "HasPreviousPage": {"type": "boolean", "readOnly": true}, "HasNextPage": {"type": "boolean", "readOnly": true}, "StartIndex": {"type": "integer", "format": "int32", "readOnly": true}, "EndIndex": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "TradeOpsExportRequest": {"required": ["ExpNos"], "type": "object", "properties": {"ExpNos": {"type": "array", "items": {"type": "string"}}, "ExportFormat": {"type": "string", "nullable": true}, "IncludeHeaders": {"type": "boolean"}, "ExportedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "TradeOpsExportResult": {"type": "object", "properties": {"Success": {"type": "boolean"}, "FileName": {"type": "string", "nullable": true}, "FilePath": {"type": "string", "nullable": true}, "RecordCount": {"type": "integer", "format": "int32"}, "ExportDate": {"type": "string", "format": "date-time"}, "ExportedBy": {"type": "string", "nullable": true}, "ErrorMessage": {"type": "string", "nullable": true}, "Warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TradeOpsStatisticsViewModel": {"type": "object", "properties": {"TotalBills": {"type": "integer", "format": "int32"}, "PendingAuthorization": {"type": "integer", "format": "int32"}, "ApprovedBills": {"type": "integer", "format": "int32"}, "DeclinedBills": {"type": "integer", "format": "int32"}, "ReportedBills": {"type": "integer", "format": "int32"}, "UnreportedBills": {"type": "integer", "format": "int32"}, "LastExportDate": {"type": "string", "format": "date-time", "nullable": true}, "LastExportBy": {"type": "string", "nullable": true}, "ApprovalRate": {"type": "number", "format": "double", "readOnly": true}, "PendingRate": {"type": "number", "format": "double", "readOnly": true}, "FormattedLastExportDate": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TradeOpsValidationResult": {"type": "object", "properties": {"IsValid": {"type": "boolean"}, "Errors": {"type": "array", "items": {"type": "string"}, "nullable": true}, "Warnings": {"type": "array", "items": {"type": "string"}, "nullable": true}, "FieldErrors": {"type": "object", "additionalProperties": {"type": "string"}, "nullable": true}}, "additionalProperties": false}, "TradeOverdueExportBillAuthorizationViewModel": {"type": "object", "properties": {"EXP_NO": {"type": "string", "nullable": true}, "ReasonDetail": {"type": "string", "nullable": true}, "ACTION_BY_BANK": {"type": "string", "nullable": true}, "ACTION_TAKING_DATE": {"type": "string", "format": "date-time"}, "InitiatorDetail": {"type": "string", "nullable": true}, "INPUTTER": {"type": "string", "nullable": true}, "INPUT_TIME": {"type": "string", "format": "date-time", "nullable": true}, "SUPERVISIONSTATUS": {"type": "integer", "format": "int32", "nullable": true}, "SUPERVISEDBY": {"type": "string", "nullable": true}, "SUPERVISION_TIME": {"type": "string", "format": "date-time", "nullable": true}, "FormattedInputTime": {"type": "string", "nullable": true, "readOnly": true}, "FormattedActionDate": {"type": "string", "nullable": true, "readOnly": true}, "CanAuthorize": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "TradeOverdueExportBillCreateRequest": {"required": ["ACTION_BY_BANK", "ACTION_TAKING_DATE", "EXP_NO", "LEGAL_ACTION_INITIATOR_ID", "REASON_ID"], "type": "object", "properties": {"EXP_NO": {"maxLength": 18, "minLength": 0, "type": "string"}, "REASON_ID": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}, "ACTION_BY_BANK": {"maxLength": 200, "minLength": 0, "type": "string"}, "ACTION_TAKING_DATE": {"type": "string", "format": "date"}, "LEGAL_ACTION_INITIATOR_ID": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}, "INPUTTER": {"type": "string", "nullable": true}, "INPUT_TIME": {"type": "string", "format": "date-time", "nullable": true}, "ISSUPERVISED": {"type": "boolean", "nullable": true}, "SUPERVISIONSTATUS": {"type": "integer", "format": "int32", "nullable": true}, "SUPERVISEDBY": {"type": "string", "nullable": true}, "SUPERVISION_TIME": {"type": "string", "format": "date-time", "nullable": true}, "ISREPORTED": {"type": "boolean", "nullable": true}}, "additionalProperties": false}, "TradeOverdueExportBillReportingViewModel": {"type": "object", "properties": {"EXP_NO": {"type": "string", "nullable": true}, "REASON_ID": {"type": "integer", "format": "int32"}, "ReasonDetail": {"type": "string", "nullable": true}, "ACTION_BY_BANK": {"type": "string", "nullable": true}, "ACTION_TAKING_DATE": {"type": "string", "format": "date-time"}, "LEGAL_ACTION_INITIATOR_ID": {"type": "integer", "format": "int32"}, "InitiatorDetail": {"type": "string", "nullable": true}, "SUPERVISEDBY": {"type": "string", "nullable": true}, "SUPERVISION_TIME": {"type": "string", "format": "date-time", "nullable": true}, "ISREPORTED": {"type": "boolean", "nullable": true}, "FormattedActionDate": {"type": "string", "nullable": true, "readOnly": true}, "FormattedSupervisionTime": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TradeOverdueExportBillUpdateRequest": {"required": ["ACTION_BY_BANK", "ACTION_TAKING_DATE", "LEGAL_ACTION_INITIATOR_ID", "REASON_ID"], "type": "object", "properties": {"REASON_ID": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}, "ACTION_BY_BANK": {"maxLength": 200, "minLength": 0, "type": "string"}, "ACTION_TAKING_DATE": {"type": "string", "format": "date"}, "LEGAL_ACTION_INITIATOR_ID": {"maximum": **********, "minimum": 1, "type": "integer", "format": "int32"}, "INPUTTER": {"type": "string", "nullable": true}, "INPUT_TIME": {"type": "string", "format": "date-time", "nullable": true}}, "additionalProperties": false}, "TradeOverdueExportBillViewModel": {"type": "object", "properties": {"EXP_NO": {"type": "string", "nullable": true}, "REASON_ID": {"type": "integer", "format": "int32"}, "ReasonDetail": {"type": "string", "nullable": true}, "ACTION_BY_BANK": {"type": "string", "nullable": true}, "ACTION_TAKING_DATE": {"type": "string", "format": "date-time"}, "LEGAL_ACTION_INITIATOR_ID": {"type": "integer", "format": "int32"}, "InitiatorDetail": {"type": "string", "nullable": true}, "INPUTTER": {"type": "string", "nullable": true}, "INPUT_TIME": {"type": "string", "format": "date-time", "nullable": true}, "ISSUPERVISED": {"type": "boolean", "nullable": true}, "SUPERVISIONSTATUS": {"type": "integer", "format": "int32", "nullable": true}, "SUPERVISEDBY": {"type": "string", "nullable": true}, "SUPERVISION_TIME": {"type": "string", "format": "date-time", "nullable": true}, "ISREPORTED": {"type": "boolean", "nullable": true}, "STATUS": {"type": "string", "nullable": true}, "StatusBadgeClass": {"type": "string", "nullable": true, "readOnly": true}, "StatusIcon": {"type": "string", "nullable": true, "readOnly": true}, "StatusText": {"type": "string", "nullable": true, "readOnly": true}}, "additionalProperties": false}, "TradeOverdueExportBillViewModelPaginatedResult": {"type": "object", "properties": {"Items": {"type": "array", "items": {"$ref": "#/components/schemas/TradeOverdueExportBillViewModel"}, "nullable": true}, "TotalCount": {"type": "integer", "format": "int32"}, "Page": {"type": "integer", "format": "int32"}, "PageSize": {"type": "integer", "format": "int32"}, "TotalPages": {"type": "integer", "format": "int32", "readOnly": true}, "HasPreviousPage": {"type": "boolean", "readOnly": true}, "HasNextPage": {"type": "boolean", "readOnly": true}, "StartIndex": {"type": "integer", "format": "int32", "readOnly": true}, "EndIndex": {"type": "integer", "format": "int32", "readOnly": true}}, "additionalProperties": false}, "TreeViewNode": {"type": "object", "properties": {"id": {"type": "string", "nullable": true}, "text": {"type": "string", "nullable": true}, "parent": {"type": "string", "nullable": true}, "icon": {"type": "string", "nullable": true}, "selected": {"type": "boolean"}, "children": {"type": "array", "items": {"$ref": "#/components/schemas/TreeViewNode"}, "nullable": true}, "state": {"$ref": "#/components/schemas/TreeViewNodeState"}}, "additionalProperties": false}, "TreeViewNodeState": {"type": "object", "properties": {"opened": {"type": "boolean"}, "disabled": {"type": "boolean"}, "selected": {"type": "boolean"}}, "additionalProperties": false}, "Type": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "IsInterface": {"type": "boolean", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Namespace": {"type": "string", "nullable": true, "readOnly": true}, "AssemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "FullName": {"type": "string", "nullable": true, "readOnly": true}, "Assembly": {"$ref": "#/components/schemas/Assembly"}, "Module": {"$ref": "#/components/schemas/Module"}, "IsNested": {"type": "boolean", "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "DeclaringMethod": {"$ref": "#/components/schemas/MethodBase"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "UnderlyingSystemType": {"$ref": "#/components/schemas/Type"}, "IsTypeDefinition": {"type": "boolean", "readOnly": true}, "IsArray": {"type": "boolean", "readOnly": true}, "IsByRef": {"type": "boolean", "readOnly": true}, "IsPointer": {"type": "boolean", "readOnly": true}, "IsConstructedGenericType": {"type": "boolean", "readOnly": true}, "IsGenericParameter": {"type": "boolean", "readOnly": true}, "IsGenericTypeParameter": {"type": "boolean", "readOnly": true}, "IsGenericMethodParameter": {"type": "boolean", "readOnly": true}, "IsGenericType": {"type": "boolean", "readOnly": true}, "IsGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "IsSZArray": {"type": "boolean", "readOnly": true}, "IsVariableBoundArray": {"type": "boolean", "readOnly": true}, "IsByRefLike": {"type": "boolean", "readOnly": true}, "IsFunctionPointer": {"type": "boolean", "readOnly": true}, "IsUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "HasElementType": {"type": "boolean", "readOnly": true}, "GenericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "GenericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "GenericParameterAttributes": {"$ref": "#/components/schemas/GenericParameterAttributes"}, "Attributes": {"$ref": "#/components/schemas/TypeAttributes"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsImport": {"type": "boolean", "readOnly": true}, "IsSealed": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsClass": {"type": "boolean", "readOnly": true}, "IsNestedAssembly": {"type": "boolean", "readOnly": true}, "IsNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "IsNestedFamily": {"type": "boolean", "readOnly": true}, "IsNestedFamORAssem": {"type": "boolean", "readOnly": true}, "IsNestedPrivate": {"type": "boolean", "readOnly": true}, "IsNestedPublic": {"type": "boolean", "readOnly": true}, "IsNotPublic": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsAutoLayout": {"type": "boolean", "readOnly": true}, "IsExplicitLayout": {"type": "boolean", "readOnly": true}, "IsLayoutSequential": {"type": "boolean", "readOnly": true}, "IsAnsiClass": {"type": "boolean", "readOnly": true}, "IsAutoClass": {"type": "boolean", "readOnly": true}, "IsUnicodeClass": {"type": "boolean", "readOnly": true}, "IsCOMObject": {"type": "boolean", "readOnly": true}, "IsContextful": {"type": "boolean", "readOnly": true}, "IsEnum": {"type": "boolean", "readOnly": true}, "IsMarshalByRef": {"type": "boolean", "readOnly": true}, "IsPrimitive": {"type": "boolean", "readOnly": true}, "IsValueType": {"type": "boolean", "readOnly": true}, "IsSignatureType": {"type": "boolean", "readOnly": true}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "StructLayoutAttribute": {"$ref": "#/components/schemas/StructLayoutAttribute"}, "TypeInitializer": {"$ref": "#/components/schemas/ConstructorInfo"}, "TypeHandle": {"$ref": "#/components/schemas/RuntimeTypeHandle"}, "GUID": {"type": "string", "format": "uuid", "readOnly": true}, "BaseType": {"$ref": "#/components/schemas/Type"}, "IsSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "IsVisible": {"type": "boolean", "readOnly": true}}, "additionalProperties": false}, "TypeAttributes": {"enum": [0, 1, 2, 3, 4, 5, 6, 7, 8, 16, 24, 32, 128, 256, 1024, 2048, 4096, 8192, 16384, 65536, 131072, 196608, 262144, 264192, 1048576, 12582912], "type": "integer", "format": "int32"}, "TypeInfo": {"type": "object", "properties": {"Name": {"type": "string", "nullable": true, "readOnly": true}, "CustomAttributes": {"type": "array", "items": {"$ref": "#/components/schemas/CustomAttributeData"}, "nullable": true, "readOnly": true}, "IsCollectible": {"type": "boolean", "readOnly": true}, "MetadataToken": {"type": "integer", "format": "int32", "readOnly": true}, "IsInterface": {"type": "boolean", "readOnly": true}, "MemberType": {"$ref": "#/components/schemas/MemberTypes"}, "Namespace": {"type": "string", "nullable": true, "readOnly": true}, "AssemblyQualifiedName": {"type": "string", "nullable": true, "readOnly": true}, "FullName": {"type": "string", "nullable": true, "readOnly": true}, "Assembly": {"$ref": "#/components/schemas/Assembly"}, "Module": {"$ref": "#/components/schemas/Module"}, "IsNested": {"type": "boolean", "readOnly": true}, "DeclaringType": {"$ref": "#/components/schemas/Type"}, "DeclaringMethod": {"$ref": "#/components/schemas/MethodBase"}, "ReflectedType": {"$ref": "#/components/schemas/Type"}, "UnderlyingSystemType": {"$ref": "#/components/schemas/Type"}, "IsTypeDefinition": {"type": "boolean", "readOnly": true}, "IsArray": {"type": "boolean", "readOnly": true}, "IsByRef": {"type": "boolean", "readOnly": true}, "IsPointer": {"type": "boolean", "readOnly": true}, "IsConstructedGenericType": {"type": "boolean", "readOnly": true}, "IsGenericParameter": {"type": "boolean", "readOnly": true}, "IsGenericTypeParameter": {"type": "boolean", "readOnly": true}, "IsGenericMethodParameter": {"type": "boolean", "readOnly": true}, "IsGenericType": {"type": "boolean", "readOnly": true}, "IsGenericTypeDefinition": {"type": "boolean", "readOnly": true}, "IsSZArray": {"type": "boolean", "readOnly": true}, "IsVariableBoundArray": {"type": "boolean", "readOnly": true}, "IsByRefLike": {"type": "boolean", "readOnly": true}, "IsFunctionPointer": {"type": "boolean", "readOnly": true}, "IsUnmanagedFunctionPointer": {"type": "boolean", "readOnly": true}, "HasElementType": {"type": "boolean", "readOnly": true}, "GenericTypeArguments": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "GenericParameterPosition": {"type": "integer", "format": "int32", "readOnly": true}, "GenericParameterAttributes": {"$ref": "#/components/schemas/GenericParameterAttributes"}, "Attributes": {"$ref": "#/components/schemas/TypeAttributes"}, "IsAbstract": {"type": "boolean", "readOnly": true}, "IsImport": {"type": "boolean", "readOnly": true}, "IsSealed": {"type": "boolean", "readOnly": true}, "IsSpecialName": {"type": "boolean", "readOnly": true}, "IsClass": {"type": "boolean", "readOnly": true}, "IsNestedAssembly": {"type": "boolean", "readOnly": true}, "IsNestedFamANDAssem": {"type": "boolean", "readOnly": true}, "IsNestedFamily": {"type": "boolean", "readOnly": true}, "IsNestedFamORAssem": {"type": "boolean", "readOnly": true}, "IsNestedPrivate": {"type": "boolean", "readOnly": true}, "IsNestedPublic": {"type": "boolean", "readOnly": true}, "IsNotPublic": {"type": "boolean", "readOnly": true}, "IsPublic": {"type": "boolean", "readOnly": true}, "IsAutoLayout": {"type": "boolean", "readOnly": true}, "IsExplicitLayout": {"type": "boolean", "readOnly": true}, "IsLayoutSequential": {"type": "boolean", "readOnly": true}, "IsAnsiClass": {"type": "boolean", "readOnly": true}, "IsAutoClass": {"type": "boolean", "readOnly": true}, "IsUnicodeClass": {"type": "boolean", "readOnly": true}, "IsCOMObject": {"type": "boolean", "readOnly": true}, "IsContextful": {"type": "boolean", "readOnly": true}, "IsEnum": {"type": "boolean", "readOnly": true}, "IsMarshalByRef": {"type": "boolean", "readOnly": true}, "IsPrimitive": {"type": "boolean", "readOnly": true}, "IsValueType": {"type": "boolean", "readOnly": true}, "IsSignatureType": {"type": "boolean", "readOnly": true}, "IsSecurityCritical": {"type": "boolean", "readOnly": true}, "IsSecuritySafeCritical": {"type": "boolean", "readOnly": true}, "IsSecurityTransparent": {"type": "boolean", "readOnly": true}, "StructLayoutAttribute": {"$ref": "#/components/schemas/StructLayoutAttribute"}, "TypeInitializer": {"$ref": "#/components/schemas/ConstructorInfo"}, "TypeHandle": {"$ref": "#/components/schemas/RuntimeTypeHandle"}, "GUID": {"type": "string", "format": "uuid", "readOnly": true}, "BaseType": {"$ref": "#/components/schemas/Type"}, "IsSerializable": {"type": "boolean", "readOnly": true, "deprecated": true}, "ContainsGenericParameters": {"type": "boolean", "readOnly": true}, "IsVisible": {"type": "boolean", "readOnly": true}, "GenericTypeParameters": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}, "DeclaredConstructors": {"type": "array", "items": {"$ref": "#/components/schemas/ConstructorInfo"}, "nullable": true, "readOnly": true}, "DeclaredEvents": {"type": "array", "items": {"$ref": "#/components/schemas/EventInfo"}, "nullable": true, "readOnly": true}, "DeclaredFields": {"type": "array", "items": {"$ref": "#/components/schemas/FieldInfo"}, "nullable": true, "readOnly": true}, "DeclaredMembers": {"type": "array", "items": {"$ref": "#/components/schemas/MemberInfo"}, "nullable": true, "readOnly": true}, "DeclaredMethods": {"type": "array", "items": {"$ref": "#/components/schemas/MethodInfo"}, "nullable": true, "readOnly": true}, "DeclaredNestedTypes": {"type": "array", "items": {"$ref": "#/components/schemas/TypeInfo"}, "nullable": true, "readOnly": true}, "DeclaredProperties": {"type": "array", "items": {"$ref": "#/components/schemas/PropertyInfo"}, "nullable": true, "readOnly": true}, "ImplementedInterfaces": {"type": "array", "items": {"$ref": "#/components/schemas/Type"}, "nullable": true, "readOnly": true}}, "additionalProperties": false}, "UpdateReportPrivilegeRequest": {"type": "object", "properties": {"RoleId": {"type": "integer", "format": "int32"}, "ReportPrivileges": {"type": "array", "items": {"$ref": "#/components/schemas/ReportPrivilegeItem"}, "nullable": true}}, "additionalProperties": false}, "UpdateRolePrivilegeRequest": {"required": ["RoleId"], "type": "object", "properties": {"RoleId": {"type": "integer", "format": "int32"}, "Privileges": {"type": "array", "items": {"$ref": "#/components/schemas/PrivilegeUpdateItem"}, "nullable": true}}, "additionalProperties": false}, "UpdateUserCompanyAccessRequest": {"required": ["BranchIDs", "UserID"], "type": "object", "properties": {"UserID": {"minLength": 1, "type": "string"}, "BranchIDs": {"type": "array", "items": {"type": "string"}}, "ModifiedBy": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserAuthenticationRequest": {"type": "object", "properties": {"UserId": {"type": "string", "nullable": true}, "Password": {"type": "string", "nullable": true}}, "additionalProperties": false}, "UserCreateRequest": {"required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "FullName", "RoleID", "UserID"], "type": "object", "properties": {"UserID": {"maxLength": 20, "minLength": 0, "type": "string"}, "FullName": {"maxLength": 150, "minLength": 0, "type": "string"}, "EmailAddress": {"maxLength": 150, "minLength": 0, "type": "string", "format": "email"}, "ContactNo": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "BranchID": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "RoleID": {"type": "array", "items": {"type": "string"}}, "IsEnabled": {"type": "boolean"}, "CreatedBy": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}, "UserModel": {"required": ["BranchID", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "FullName", "RoleID", "UserID"], "type": "object", "properties": {"BranchID": {"minLength": 1, "type": "string"}, "BranchName": {"type": "string", "nullable": true}, "UserID": {"minLength": 1, "type": "string"}, "FullName": {"minLength": 1, "type": "string"}, "EmailAddress": {"minLength": 1, "type": "string", "format": "email"}, "ContactNo": {"type": "string", "nullable": true}, "IsEnabled": {"type": "boolean"}, "RoleID": {"minLength": 1, "type": "string"}, "RoleName": {"type": "string", "nullable": true}, "CreatedBy": {"type": "string", "nullable": true}, "ModifiedBy": {"type": "string", "nullable": true}, "ModificationDate": {"type": "string", "format": "date-time"}, "LastPasswordChangedOn": {"type": "string", "format": "date-time"}}, "additionalProperties": false}, "UserUpdateRequest": {"required": ["<PERSON><PERSON><PERSON><PERSON><PERSON>", "FullName", "RoleID"], "type": "object", "properties": {"FullName": {"maxLength": 150, "minLength": 0, "type": "string"}, "EmailAddress": {"maxLength": 150, "minLength": 0, "type": "string", "format": "email"}, "ContactNo": {"maxLength": 50, "minLength": 0, "type": "string", "nullable": true}, "BranchID": {"maxLength": 10, "minLength": 0, "type": "string", "nullable": true}, "RoleID": {"maxLength": 50, "minLength": 0, "type": "string"}, "IsEnabled": {"type": "boolean"}, "ModifiedBy": {"maxLength": 20, "minLength": 0, "type": "string", "nullable": true}}, "additionalProperties": false}}}}