@model List<SmartBI.Data.ViewModels.RM_PERFORMANCE_GROWTH>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header card-header-rose">
                <h4 class="card-title">RM Performance Growth</h4>
                <p class="card-category">Growth analysis by product type</p>
            </div>
            <div class="card-body">
                @if (Model != null && Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Growth Type</th>
                                    <th>Product Type</th>
                                    <th>Number of Accounts</th>
                                    <th>Total Balances</th>
                                    <th>Average Balance</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    <tr>
                                        <td>
                                            @if (!string.IsNullOrEmpty(item.GROWTH_TYPE))
                                            {
                                                if (item.GROWTH_TYPE.ToUpper().Contains("POSITIVE"))
                                                {
                                                    <span class="badge badge-success">@item.GROWTH_TYPE</span>
                                                }
                                                else if (item.GROWTH_TYPE.ToUpper().Contains("NEGATIVE"))
                                                {
                                                    <span class="badge badge-danger">@item.GROWTH_TYPE</span>
                                                }
                                                else
                                                {
                                                    <span class="badge badge-info">@item.GROWTH_TYPE</span>
                                                }
                                            }
                                            else
                                            {
                                                <span class="text-muted">N/A</span>
                                            }
                                        </td>
                                        <td>@(item.PRODUCT_TYPE ?? "N/A")</td>
                                        <td>@item.NO_OF_ACCOUNS.ToString("N0")</td>
                                        <td>BDT @item.TOTAL_BALANCES.ToString("N2")</td>
                                        <td>BDT @((item.NO_OF_ACCOUNS > 0 ? item.TOTAL_BALANCES / item.NO_OF_ACCOUNS : 0).ToString("N2"))</td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No Growth Data Available</h4>
                        <p class="text-muted">No performance growth data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@if (Model != null && Model.Any())
{
    <!-- Growth Summary Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-success card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">account_balance_wallet</i>
                    </div>
                    <p class="card-category">Total Growth Accounts</p>
                    <h3 class="card-title">@Model.Sum(x => x.NO_OF_ACCOUNS).ToString("N0")</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-success">info</i>
                        Combined account growth
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-info card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">account_balance</i>
                    </div>
                    <p class="card-category">Total Growth Balances</p>
                    <h3 class="card-title">BDT @Model.Sum(x => x.TOTAL_BALANCES).ToString("N2")</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-info">info</i>
                        Combined balance growth
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-warning card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">trending_up</i>
                    </div>
                    <p class="card-category">Average Growth Balance</p>
                    <h3 class="card-title">BDT @((Model.Sum(x => x.NO_OF_ACCOUNS) > 0 ? Model.Sum(x => x.TOTAL_BALANCES) / Model.Sum(x => x.NO_OF_ACCOUNS) : 0).ToString("N2"))</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-warning">info</i>
                        Per account average
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-rose card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">category</i>
                    </div>
                    <p class="card-category">Product Categories</p>
                    <h3 class="card-title">@Model.Select(x => x.PRODUCT_TYPE).Distinct().Count()</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-rose">info</i>
                        Unique product types
                    </div>
                </div>
            </div>
        </div>
    </div>

    <!-- Product Type Breakdown -->
    <div class="row">
        <div class="col-md-12">
            <div class="card">
                <div class="card-header card-header-info">
                    <h4 class="card-title">Product Type Summary</h4>
                    <p class="card-category">Growth breakdown by product category</p>
                </div>
                <div class="card-body">
                    <div class="row">
                        @{
                            var productGroups = Model.GroupBy(x => x.PRODUCT_TYPE).ToList();
                        }
                        @foreach (var group in productGroups)
                        {
                            <div class="col-lg-4 col-md-6 mb-3">
                                <div class="card card-stats">
                                    <div class="card-header card-header-primary card-header-icon">
                                        <div class="card-icon">
                                            <i class="material-icons">pie_chart</i>
                                        </div>
                                        <p class="card-category">@(group.Key ?? "Unknown")</p>
                                        <h3 class="card-title">@group.Sum(x => x.NO_OF_ACCOUNS).ToString("N0")</h3>
                                    </div>
                                    <div class="card-footer">
                                        <div class="stats">
                                            <i class="material-icons text-primary">info</i>
                                            BDT @group.Sum(x => x.TOTAL_BALANCES).ToString("N0")
                                        </div>
                                    </div>
                                </div>
                            </div>
                        }
                    </div>
                </div>
            </div>
        </div>
    </div>
}
