{"Version": 1, "Hash": "9OSMf8KUJ95I2O2j12qMYGKK+2KfMxqMkX93Ojd4OLY=", "Source": "SmartBI.Core", "BasePath": "/", "Mode": "<PERSON><PERSON><PERSON>", "ManifestType": "Build", "ReferencedProjectsConfiguration": [], "DiscoveryPatterns": [{"Name": "SmartBI.Core\\wwwroot", "Source": "SmartBI.Core", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "Pattern": "**"}], "Assets": [{"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\bundle\\SmartBI.Core.styles.css", "SourceId": "SmartBI.Core", "SourceType": "Computed", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\bundle\\", "BasePath": "/", "RelativePath": "SmartBI.Core#[.{fingerprint}]?.styles.css", "AssetKind": "All", "AssetMode": "CurrentProject", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ApplicationBundle", "Fingerprint": "8cpw0666k3", "Integrity": "SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\bundle\\SmartBI.Core.styles.css", "FileLength": 1111, "LastWriteTime": "2025-06-17T05:17:50+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\SmartBI.Core.bundle.scp.css", "SourceId": "SmartBI.Core", "SourceType": "Computed", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\", "BasePath": "/", "RelativePath": "SmartBI.Core#[.{fingerprint}]!.bundle.scp.css", "AssetKind": "All", "AssetMode": "Reference", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "ScopedCss", "AssetTraitValue": "ProjectBundle", "Fingerprint": "8cpw0666k3", "Integrity": "SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\SmartBI.Core.bundle.scp.css", "FileLength": 1111, "LastWriteTime": "2025-06-17T05:17:50+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\css\\material-dashboard.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/css/material-dashboard.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "caks9akf9x", "Integrity": "tGb1izvVI/8yoOsJTAz1XXSP9mLE6uoYanVq2eG/mLs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\css\\material-dashboard.min.css", "FileLength": 510314, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\demo\\demo.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/demo/demo#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j9o6ft6t9u", "Integrity": "V2OCzdd/cVsRD9+GDq6zl2pC2eYExMK8yYf/S2HOdRk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\demo\\demo.css", "FileLength": 846, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\demo\\demo.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/demo/demo#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "354obk1sri", "Integrity": "9/VAbH59DmEMlEl0xc/H3edE45YL7yohC+H/a43R07k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\demo\\demo.js", "FileLength": 33222, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\demo\\jquery.sharrre.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/demo/jquery.sharrre#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9ic6jpultn", "Integrity": "L2SCHU+/bxDGmp8izqjvlfW4acosVyX4uAMszQtFv24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\demo\\jquery.sharrre.js", "FileLength": 25403, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\apple-icon.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/apple-icon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5vxk3yf90w", "Integrity": "jzxI3w03y2/rbD4IlK9mBTr8iipnEcR0LEasSoythP8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\apple-icon.png", "FileLength": 2446, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\bg-pricing.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/bg-pricing#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wphb465enx", "Integrity": "jKWDDZXZ6bzUN0HuSkgoT5H71UzDAQg95meLOaeZios=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\bg-pricing.jpg", "FileLength": 725542, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\card-1.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/card-1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "zgtxmg0xkw", "Integrity": "jZb/JcmQc4nNoZPnHBRvc4v89WaxEA6xipV9yr8eI7Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\card-1.jpg", "FileLength": 233583, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\card-2.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/card-2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "naznnc21oz", "Integrity": "b5nOIvGjayk1c7+0Gc1VsTRUALR3gkakBPbrSdkZaDk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\card-2.jpg", "FileLength": 251125, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\card-3.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/card-3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "044x3xumdp", "Integrity": "EPpBgzDKnChY3lXoXF5ssiGCwz6NWGMKajHU1mb/bnI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\card-3.jpg", "FileLength": 123917, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\clint-mckoy.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/clint-mckoy#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sh3bsj7358", "Integrity": "kCFHoo1+00GvTsESsteimZQTFHRE9XzLu9/ufsRDu6Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\clint-mckoy.jpg", "FileLength": 307193, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\default-avatar.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/default-avatar#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vdblgpfca6", "Integrity": "4zASlHZ8cnc2SZsIuhZmp2tzgWyUnfdV4W6qNvSxTwY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\default-avatar.png", "FileLength": 2864, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\faces\\avatar.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/faces/avatar#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "40vlznypoo", "Integrity": "NK9x+jG3V65wSfrmP7e50hdMEzJBVkbjQ1L3NnhcmaE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\faces\\avatar.jpg", "FileLength": 85214, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\faces\\card-profile1-square.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/faces/card-profile1-square#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c9ufb2enf1", "Integrity": "EOZXeW64C+HWjfkninbjiNdZE187UISE9fNJGicxWw8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\faces\\card-profile1-square.jpg", "FileLength": 74370, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\faces\\marc.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/faces/marc#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ri90un01rk", "Integrity": "zfXQMlSus3TGFULCvzA/DrxPjo9OUAOpSAP0mCUAVfU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\faces\\marc.jpg", "FileLength": 54633, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\favicon.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/favicon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b3o3hrm6bc", "Integrity": "t3lRlqdYsSFqKN4/OctPjVF+bvJui4hZi0M3ojeLBAk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\favicon.png", "FileLength": 2761, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\AU.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/flags/AU#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "izuvgmyjg2", "Integrity": "nrYxpZzGRPlTWe6nOVtM+DUPxQ7NgXc+mHc+yX6wSQE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\flags\\AU.png", "FileLength": 3488, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\BR.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/flags/BR#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fyhwl7falv", "Integrity": "50P93VSov4FCbT1H7K9OC/2JAUSf0wDAqGWWolvTuyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\flags\\BR.png", "FileLength": 3594, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\DE.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/flags/DE#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "sk7vbm1zt0", "Integrity": "5PT2VlpKgZecxnR3xHVmc+B6J+GgK6sIyRyTF21RYI4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\flags\\DE.png", "FileLength": 3109, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\GB.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/flags/GB#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0l4h0mfn80", "Integrity": "dPlnhNwFvVNw8lsNhQ0P16RVQizvr79SRQiZq6XKjNA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\flags\\GB.png", "FileLength": 3543, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\RO.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/flags/RO#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "deo58m671w", "Integrity": "xUq6vFlNkU8HntMCQn5LMcJK91QgXwEy8PU4QxmqrpE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\flags\\RO.png", "FileLength": 3298, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\US.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/flags/US#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xdruoe7wo0", "Integrity": "25l0fXukLYXDZuyjRTFZ+F2qqan+VxwCNtaArN49GWQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\flags\\US.png", "FileLength": 3310, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\image_placeholder.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/image_placeholder#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9ag3fgu5jm", "Integrity": "z7vcj9j2QtDCNzMHdGLGwbHNDjGTPA0itLcULFi5YEU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\image_placeholder.jpg", "FileLength": 44534, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\lock.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/lock#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cj7xm7a593", "Integrity": "aNaol4P7J+wUmCb2bCT0Z2h8Fm//Q+DiBw8byqqWTew=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\lock.jpg", "FileLength": 1006309, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\login.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/login#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "u7y13t8v4m", "Integrity": "ehqB+ggitdIzswr0+FMKafv28GdESoYcNyxaSvYDekM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\login.jpg", "FileLength": 547677, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\placeholder.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/placeholder#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "l8zcwmw9uc", "Integrity": "ZbW3USZZ7URX1dRQEGZBZMsTtYCbF7fxPtH4z4oQ5r0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\placeholder.jpg", "FileLength": 18015, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\product1.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/product1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fl6f6boglu", "Integrity": "3d7LCq6OjSr3ykqg+U0k0VP5B6fNUIc617PxmfrSyM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\product1.jpg", "FileLength": 12146, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\product2.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/product2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1kfwu09a04", "Integrity": "P/thAvbw1LnY0F7Y+n3OqSXif1bUsT/wAjWIB0Fwk2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\product2.jpg", "FileLength": 9677, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\product3.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/product3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "oityib5ljs", "Integrity": "8Lekzy0sN2dRFotjK5ATUSuz6vSPaf6wz5zC0gBMLY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\product3.jpg", "FileLength": 21624, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\register.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/register#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c14zu2kyvj", "Integrity": "MOXbuwMJvIsonWECeP8dLUyQiUsRxxArzVfILb8hgG8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\register.jpg", "FileLength": 545914, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-1.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/sidebar-1#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9g5eqnvc3f", "Integrity": "GRmmooKm4U9amJHIL4unLLzMCwgb3ScuseJhgB6AAGY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\sidebar-1.jpg", "FileLength": 189310, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-2.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/sidebar-2#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "p2xdqlcugr", "Integrity": "sfbfYBML03cnBuMH3tlV8dyWst8g/oupl1ZDYNSzAkg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\sidebar-2.jpg", "FileLength": 393121, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-3.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/sidebar-3#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "q3hkib9lo2", "Integrity": "lsHjy58jaUMJhm7dnzUWYHVKzlWW4lDLoYguFtY/7VY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\sidebar-3.jpg", "FileLength": 377600, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-4.jpg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/img/sidebar-4#[.{fingerprint}]?.jpg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2pbm105vcv", "Integrity": "Smr5o2sxf0/dySEb6fNnxzy6XyBFuhzziY9WYU1NFzI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\img\\sidebar-4.jpg", "FileLength": 386464, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\material-dashboard.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/material-dashboard.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7yry9pyep8", "Integrity": "/w6a41SiizcbtUWuJr6tuKgPkxv+1DBlHxRq31aqBa0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\material-dashboard.min.js", "FileLength": 11942, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\arrive.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/arrive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wv1fh4f76g", "Integrity": "TKikMGzFMPdZPL/vRa0FZflEy5bP6D4sPgHQ/PPh+ss=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\arrive.min.js", "FileLength": 5091, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-datetimepicker.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/bootstrap-datetimepicker.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lh15wxqkcf", "Integrity": "yXozLHG4zXJz4wI/C/EKq1bg42evpBQYzC4zVZO0I7c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\bootstrap-datetimepicker.min.js", "FileLength": 40248, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-notify.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/bootstrap-notify#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nexbo68se3", "Integrity": "IRIPV/8L/RXy6fZiaVbKuDkIsonzfALPabfYEFI2skk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\bootstrap-notify.js", "FileLength": 16935, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-selectpicker.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/bootstrap-selectpicker#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hay3z6e75s", "Integrity": "p7SglDuNQuTycwUFPJX9JbbLH2zlQV69e1ciW7PbvpM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\bootstrap-selectpicker.js", "FileLength": 95046, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-tagsinput.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/bootstrap-tagsinput#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jn76hyvc52", "Integrity": "P9VMZ+YS7WYtSicYoqVouHWczPdXgaxQ2LmwI10mvi8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\bootstrap-tagsinput.js", "FileLength": 22293, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\chartist.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/chartist.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "32mebuojjq", "Integrity": "K8omIIjKNHAvHgZfw9xI9+HoypjiLDr8HhN3MUlWUXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\chartist.min.js", "FileLength": 40174, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\fullcalendar.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/fullcalendar.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gr8nfsd9b7", "Integrity": "HPsVxOHjSoFxW4GXTppL59Q3yjo7AnEjo7Fq2yVYHIc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\fullcalendar.min.js", "FileLength": 213775, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jasny-bootstrap.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/jasny-bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ibzoq4ggt8", "Integrity": "zrKYjrV5tdhLTivmOO9TAI5x6i5dcMVO4YOi/zUAqrk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\jasny-bootstrap.min.js", "FileLength": 16780, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery.bootstrap-wizard.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/jquery.bootstrap-wizard#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "n1y09h6ijo", "Integrity": "pea1hrFL3G3NTw+WkiM8LOiwrjtDqvsXFBU48D3N40s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\jquery.bootstrap-wizard.js", "FileLength": 14594, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery.dataTables.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/jquery.dataTables.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ti3sp3yx4y", "Integrity": "K/pY0C5JwJnCTDpMZYh9MS+G019rFNvawH5HrCYip50=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\jquery.dataTables.min.js", "FileLength": 2183500, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery.validate.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m64ca5mr57", "Integrity": "Lj47JmDL+qxf6/elCzHQSUFZmJYmqEECssN5LP/ifRM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\jquery.validate.min.js", "FileLength": 21090, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery-jvectormap.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/jquery-jvectormap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s8frxs2lby", "Integrity": "FGmYM0oJCu3L/583JPu4WGlaWxXMTeFtUY88Zf1Xay0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\jquery-jvectormap.js", "FileLength": 269400, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\moment.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/moment.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xmlvivfnb8", "Integrity": "t6K97JKAnxSnB126XGEiWEQsaC+JPPO6mDJ5aa3IFug=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\moment.min.js", "FileLength": 58687, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\nouislider.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/nouislider.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "itc35f2ukd", "Integrity": "WjsMa1Nc2pi7iNUPSi/IwsAM1/HvFZOxrJq8gRcf9XM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\nouislider.min.js", "FileLength": 21163, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\perfect-scrollbar.jquery.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/perfect-scrollbar.jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j1rmxb6kr8", "Integrity": "maBqL1yKR1eyJOI0j6Ns5b5XvNnRtih0udc0TLUJXQQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\perfect-scrollbar.jquery.min.js", "FileLength": 25332, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\sweetalert2.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "assets/js/plugins/sweetalert2#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x521yyi2ct", "Integrity": "y3TyVu7YdsKONZZG+bpY6L/VoWpp73V4XpZybzNLyJk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\assets\\js\\plugins\\sweetalert2.js", "FileLength": 116600, "LastWriteTime": "2019-01-24T14:57:01+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "Content/image/logo#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c1xu3sjlm1", "Integrity": "QLpvAHYFV23XX4qzhRt03Hl73CF5JzWTlo4YhcelGXo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Content\\image\\logo.png", "FileLength": 11410, "LastWriteTime": "2015-06-11T12:52:33+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo_icon.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "Content/image/logo_icon#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pw4d012y51", "Integrity": "52HAvvTLLvONX12i+8u7LXknSKw6bGGx+ukpA9bUZIM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Content\\image\\logo_icon.png", "FileLength": 4306, "LastWriteTime": "2022-01-26T05:10:11+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\Logo_red.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "Content/image/Logo_red#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jfiov0noex", "Integrity": "sZ3hH8DzjmPhxmMHLZ0tUkp4V+iC98K/ArADvkV2QDM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Content\\image\\Logo_red.png", "FileLength": 5563, "LastWriteTime": "2016-01-19T07:33:55+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo-conv.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "Content/image/logo-conv#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cr3isdb5hq", "Integrity": "eDyltiXOE/avROZ0SrlbeEuiZhcUMaHRsrGhLVAhpiA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Content\\image\\logo-conv.png", "FileLength": 18897, "LastWriteTime": "2015-08-11T11:32:14+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo-islamic.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "Content/image/logo-islamic#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qe10b765u5", "Integrity": "Ig64Vc/zGcIw1NR/eKjAzT+2iMUK/IVULSZoBcy2Kis=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Content\\image\\logo-islamic.png", "FileLength": 3175, "LastWriteTime": "2022-01-23T14:22:07+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\user.png", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "Content/image/user#[.{fingerprint}]?.png", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "coz1cln6ig", "Integrity": "JKUZOsuXyZyqRf8Wvovt7e/cRFuAkhRaL2vp2QV1N4Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\Content\\image\\user.png", "FileLength": 4333, "LastWriteTime": "2021-10-05T08:23:04+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\brand-colors.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "css/brand-colors#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1oy9d1ws5g", "Integrity": "wej0CiXqDk0ZPlyR+auxgyWVXV5fsKVliJWr/O3nUOc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\brand-colors.css", "FileLength": 1118, "LastWriteTime": "2025-06-12T10:59:00+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\custom.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "css/custom#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8m9ey7kn3p", "Integrity": "0PmXkEXwKULiVEZhhR9eW/hlyrZna2/ImQ1NAXFB1kw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\custom.css", "FileLength": 1046, "LastWriteTime": "2025-06-12T18:34:03+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\login.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "css/login#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "77io4isa0i", "Integrity": "3UGc3Hw4H12fKe/fTGd6Tn+4h+FaOTCkXEr4ergroFw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\login.css", "FileLength": 11353, "LastWriteTime": "2025-06-12T10:53:29+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\material-forms.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "css/material-forms#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "47j1v6ljqt", "Integrity": "cH6kiK1PczVOjrBEB2F3oEAZUilnaxuw3Gs1A+n+dgc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\material-forms.css", "FileLength": 6185, "LastWriteTime": "2025-06-12T09:48:33+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\site.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "css/site#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dmfl13e5hp", "Integrity": "qxDol8xSbUXuRHH9OPDNx62IBCJqoQV5Aha/vWFVpPA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\site.css", "FileLength": 5254, "LastWriteTime": "2025-06-16T10:59:47+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\user-profile.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "css/user-profile#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "blv4wxc6j0", "Integrity": "EQNMGyq9A5HKjPnl1I5HY5KhAppes42E8RlX1noujDs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\css\\user-profile.css", "FileLength": 7931, "LastWriteTime": "2025-06-11T18:31:14+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\favicon.ico", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "favicon#[.{fingerprint}]?.ico", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "61n19gt1b8", "Integrity": "Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\favicon.ico", "FileLength": 5430, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\js\\dashboard.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "js/dashboard#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yu78tspv7y", "Integrity": "Nqnn8clbgv+5l0PgxcTOldg8mkMKrFn4TvPL+rYUUGg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\dashboard.js", "FileLength": 1, "LastWriteTime": "2025-06-16T07:16:45+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\js\\signalr-connection.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "js/signalr-connection#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fvm3yzzppj", "Integrity": "i9SWhBUof2EkDlorK+CW1k4wgM1gBwlhRhWWHH0fLKk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\signalr-connection.js", "FileLength": 2090, "LastWriteTime": "2025-05-29T10:55:39+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\js\\site.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "js/site#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5c6j43bk15", "Integrity": "KzlEx3RWIWD5yoXxoQh01bgKfn3dGHpLTe3GNQQC8oc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\js\\site.js", "FileLength": 2930, "LastWriteTime": "2025-06-16T04:42:28+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\css\\bootstrap.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/css/bootstrap.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dxst0jyaol", "Integrity": "T/zFmO5s/0aSwc6ics2KLxlfbewyRz6UNw1s3Ppf5gE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\css\\bootstrap.min.css", "FileLength": 161409, "LastWriteTime": "2025-05-21T18:05:14+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "37tfw0ft22", "Integrity": "j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "FileLength": 280259, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hrwsygsryq", "Integrity": "3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "FileLength": 679615, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "pk9g2wxc8p", "Integrity": "h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "FileLength": 232911, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ft3s53vfgj", "Integrity": "rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "FileLength": 589087, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "bqjiyaj88i", "Integrity": "Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "FileLength": 70329, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2j<PERSON><PERSON><PERSON><PERSON>", "Integrity": "xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "FileLength": 203221, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "erw9l3u2r3", "Integrity": "5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "FileLength": 51795, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aexeepp0ev", "Integrity": "kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "FileLength": 115986, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "d7shbmvgxk", "Integrity": "CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "FileLength": 70403, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ausgxo2sd3", "Integrity": "/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "FileLength": 203225, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k8d9w2qqmf", "Integrity": "vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "FileLength": 51870, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "cosvhxvwiu", "Integrity": "7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "FileLength": 116063, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ub07r2b239", "Integrity": "lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "FileLength": 12065, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fvhpjtyr6v", "Integrity": "RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "FileLength": 129371, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "b7pk76d08c", "Integrity": "l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "FileLength": 10126, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fsbi9cje9m", "Integrity": "0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "FileLength": 51369, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "rzd6atqjts", "Integrity": "V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "FileLength": 12058, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ee0r1s7dh0", "Integrity": "OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "FileLength": 129386, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "dxx9fxp4il", "Integrity": "/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "FileLength": 10198, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jd9uben2k1", "Integrity": "910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "FileLength": 63943, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "khv3u5hwcm", "Integrity": "2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "FileLength": 107823, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "r4e9w2rdcm", "Integrity": "Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "FileLength": 267535, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lcd1t2u6c8", "Integrity": "KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "FileLength": 85352, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "c2oey78nd0", "Integrity": "rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "FileLength": 180381, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "tdbxkamptv", "Integrity": "H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "FileLength": 107691, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j5mq2jizvt", "Integrity": "p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "FileLength": 267476, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "06098lyss8", "Integrity": "GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "FileLength": 85281, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "nvvlpmu67g", "Integrity": "o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "FileLength": 180217, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6cfz1n2cew", "Integrity": "mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "FileLength": 207819, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6pdc2jztkx", "Integrity": "Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "FileLength": 444579, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "493y06b0oq", "Integrity": "CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "FileLength": 80721, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.bundle.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iovd86k7lj", "Integrity": "Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "FileLength": 332090, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vr1egmr9el", "Integrity": "exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "FileLength": 135829, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "kbrnm935zg", "Integrity": "EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "FileLength": 305438, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jj8uyg4cgr", "Integrity": "QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "FileLength": 73935, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.esm.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "y7v9cxd14o", "Integrity": "Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "FileLength": 222455, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "notf2xhcfb", "Integrity": "+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "FileLength": 145401, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "h1s4sie4z3", "Integrity": "9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "FileLength": 306606, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "63fj8s7r0e", "Integrity": "3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "FileLength": 60635, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/dist/js/bootstrap.min.js#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0j3bgjxly4", "Integrity": "ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "FileLength": 220561, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\js\\bootstrap.bundle.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/js/bootstrap.bundle.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "8u741w00kw", "Integrity": "sCElQ8xaSgoxwbWp0eiXMmGZIRa0z94+ffzzO06BqXs=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\js\\bootstrap.bundle.min.js", "FileLength": 84378, "LastWriteTime": "2025-05-21T18:05:18+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\LICENSE", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap/LICENSE#[.{fingerprint}]?", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "81b7ukuj9c", "Integrity": "ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap\\LICENSE", "FileLength": 1153, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap-datepicker\\css\\bootstrap-datepicker.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap-datepicker/css/bootstrap-datepicker.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "yw<PERSON>retzxeo", "Integrity": "bj/NnRFZeLLz+xTKmDMuoXaAzgtz+D8xYKFKX/YpbgE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap-datepicker\\css\\bootstrap-datepicker.min.css", "FileLength": 15727, "LastWriteTime": "2025-05-24T06:29:33+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap-datepicker\\js\\bootstrap-datepicker.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/bootstrap-datepicker/js/bootstrap-datepicker.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ooq100mrm4", "Integrity": "WF1Td1ZzGSctvURD28bCDv8CYBJsuqs8ZC+698TCRdE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\bootstrap-datepicker\\js\\bootstrap-datepicker.min.js", "FileLength": 10830, "LastWriteTime": "2025-05-24T06:28:12+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\datatables\\bs4\\css\\dataTables.bootstrap4.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/datatables/bs4/css/dataTables.bootstrap4.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "44c5h2nspz", "Integrity": "lDWLG10paq84N0F/7818mEj3YW5d6LCSBmIj2LirkYo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\datatables\\bs4\\css\\dataTables.bootstrap4.min.css", "FileLength": 7496, "LastWriteTime": "2025-05-21T18:05:32+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\datatables\\bs4\\js\\dataTables.bootstrap4.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/datatables/bs4/js/dataTables.bootstrap4.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "gueg8vqske", "Integrity": "2MzaecCGkwO775PvRJkqMTd4sR6cuRiQlkT2iUeCsSU=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\datatables\\bs4\\js\\dataTables.bootstrap4.min.js", "FileLength": 4520, "LastWriteTime": "2025-05-21T18:05:32+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\datatables\\js\\jquery.dataTables.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/datatables/js/jquery.dataTables.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "aow88z2agc", "Integrity": "lpQbyCSrPqrv7IZbdk1u4zJ3Ft/DUAIfZElc0Zi25Kw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\datatables\\js\\jquery.dataTables.min.js", "FileLength": 88048, "LastWriteTime": "2025-05-21T18:05:32+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\css\\all.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/font-awesome/css/all.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vm77v644vc", "Integrity": "mUZM63G8m73Mcidfrv5E+Y61y7a12O5mW4ezU3bxqW4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\font-awesome\\css\\all.min.css", "FileLength": 59305, "LastWriteTime": "2025-05-21T18:05:04+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-brands-400.woff", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/font-awesome/webfonts/fa-brands-400#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j5wh9ughv7", "Integrity": "+SF/ZodLDAHNjBC2opXbxPYJrLb1rcQcN9pGZBtX6wI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\font-awesome\\webfonts\\fa-brands-400.woff", "FileLength": 89988, "LastWriteTime": "2025-05-22T04:49:49+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-brands-400.woff2", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/font-awesome/webfonts/fa-brands-400#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "6fdyj7j1sx", "Integrity": "jqh5F1SRWomKMQDmPjKXim0XY75t+Oc6OdOpDWkc3u8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\font-awesome\\webfonts\\fa-brands-400.woff2", "FileLength": 76736, "LastWriteTime": "2025-05-22T04:49:48+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-regular-400.woff", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/font-awesome/webfonts/fa-regular-400#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "hp61lh7kro", "Integrity": "y56eaTGSQTzeKx8hwdwdRLb+eyfMK0WOizWdGPn/j04=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\font-awesome\\webfonts\\fa-regular-400.woff", "FileLength": 16276, "LastWriteTime": "2025-05-22T04:49:41+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-regular-400.woff2", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/font-awesome/webfonts/fa-regular-400#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "40m2n68m7w", "Integrity": "5CqIRERIrD1gVJzHwf8sipyschA0wHPYChSkTnlzDMo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\font-awesome\\webfonts\\fa-regular-400.woff2", "FileLength": 13224, "LastWriteTime": "2025-05-22T04:49:35+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-solid-900.woff", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/font-awesome/webfonts/fa-solid-900#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jba8vm33uh", "Integrity": "P200iM9lN09vZ2wxU0CwrCvoMr1VJAyAlEjjbvm5YyY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\font-awesome\\webfonts\\fa-solid-900.woff", "FileLength": 101648, "LastWriteTime": "2025-05-22T04:49:34+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-solid-900.woff2", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/font-awesome/webfonts/fa-solid-900#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "k51ry5602j", "Integrity": "mDS4KtJuKjdYPSJnahLdLrD+fIA1aiEU0NsaqLOJlTc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\font-awesome\\webfonts\\fa-solid-900.woff2", "FileLength": 78268, "LastWriteTime": "2025-05-22T04:49:29+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/fonts/source-sans-pro/source-sans-pro#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "jnm0qub8ui", "Integrity": "UUtYe9HXy0ub5OcGvMgAoki4hrsFDhx9ZOvBhohTgVw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro.css", "FileLength": 1140, "LastWriteTime": "2025-05-21T18:00:28+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-300.woff", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-300#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0chuq1odcr", "Integrity": "wI76kXgYZdGi6fywMPisVcLY6tv4giwuolFVYzP5nZw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-300.woff", "FileLength": 20096, "LastWriteTime": "2025-05-22T04:49:17+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-300.woff2", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-300#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "7agu212shz", "Integrity": "77PNxeRYL9Z9/6tvxuUGIHTOP4xRdHNGr5ROl3Sdwwk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-300.woff2", "FileLength": 15948, "LastWriteTime": "2025-05-22T04:49:15+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-700.woff", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-700#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4l6isozb0a", "Integrity": "2N0N5jgpPrYtuhWm5BD7Cvmls2w13yJiN7G2CdVzxj4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-700.woff", "FileLength": 19896, "LastWriteTime": "2025-05-22T04:49:12+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-700.woff2", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-700#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "on17fdnzzh", "Integrity": "JPfjl/rseeYsN/8vALFw9twVV/tGrBafnxiXqdZB3QM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-700.woff2", "FileLength": 15764, "LastWriteTime": "2025-05-22T04:49:08+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-italic.woff", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-italic#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wxz1f1ij2p", "Integrity": "NF/QvWIlxTxNKKolZ5jW2KoNI+3ifkKTO2JZn95wLnw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-italic.woff", "FileLength": 19408, "LastWriteTime": "2025-05-22T04:49:25+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-italic.woff2", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-italic#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "j82urxxfh7", "Integrity": "01WcgWr2QOg4KynQLU+9jHIl/PAwLPJE2LLXz12y/dE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-italic.woff2", "FileLength": 15316, "LastWriteTime": "2025-05-22T04:49:19+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-regular.woff", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-regular#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "klzwlqew5m", "Integrity": "ODme/nB6j/wSNZoAhuc0AxW0IZShD9Lh0SiL4S2p45w=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-regular.woff", "FileLength": 20180, "LastWriteTime": "2025-05-22T04:49:10+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-regular.woff2", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-regular#[.{fingerprint}]?.woff2", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xe5pesne3m", "Integrity": "qZUPpcqc9HBydwkA0lm89neKoRGWUtLnBtXrkt8lQZk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-regular.woff2", "FileLength": 16112, "LastWriteTime": "2025-05-22T04:49:06+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\css\\ionicons.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/ionicons/css/ionicons.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "iz7wz4ptva", "Integrity": "kqxQgiD1u2DslOB2UFKOtmYl+CpHQK2gaM3gU2V4EoY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\ionicons\\css\\ionicons.min.css", "FileLength": 51284, "LastWriteTime": "2025-05-21T18:05:07+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.eot", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/ionicons/fonts/ionicons#[.{fingerprint}]?.eot", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ygayrii7su", "Integrity": "fjMNxTOru4beuavPT1OkJjkV8oh/oOwCbF3jbH2xo20=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\ionicons\\fonts\\ionicons.eot", "FileLength": 120724, "LastWriteTime": "2025-05-22T04:50:40+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.svg", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/ionicons/fonts/ionicons#[.{fingerprint}]?.svg", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "f726s6newv", "Integrity": "VUvwsesnAE/NKYGhjIfWgbcQfNpR2Srq+WXQrOKxLX8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\ionicons\\fonts\\ionicons.svg", "FileLength": 333834, "LastWriteTime": "2025-05-22T04:50:53+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.ttf", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/ionicons/fonts/ionicons#[.{fingerprint}]?.ttf", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "qdbrmunew0", "Integrity": "XnAINewFKTo9D541Tn0DgxnTRSHNJ554IZjf9tHdWPI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\ionicons\\fonts\\ionicons.ttf", "FileLength": 188508, "LastWriteTime": "2025-05-22T04:50:31+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.woff", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/ionicons/fonts/ionicons#[.{fingerprint}]?.woff", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xatgdrjce6", "Integrity": "p144ECbs7UT06NbqTcQOKOamTdlT6MC2wjnRrIRMSi0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\ionicons\\fonts\\ionicons.woff", "FileLength": 67904, "LastWriteTime": "2025-05-22T04:50:34+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0i3buxo5is", "Integrity": "eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.js", "FileLength": 285314, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.js", "FileLength": 87533, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ttgo8qnofa", "Integrity": "z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.min.map", "FileLength": 134755, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.slim#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "2z0ns9nrw6", "Integrity": "UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "FileLength": 232015, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "muycvpuwrr", "Integrity": "kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "FileLength": 70264, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/dist/jquery.slim.min#[.{fingerprint}]?.map", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "87fc7y1x7t", "Integrity": "9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "FileLength": 107143, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\jquery-3.7.1.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/jquery-3.7.1.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "o1o13a6vjx", "Integrity": "/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\jquery-3.7.1.min.js", "FileLength": 87533, "LastWriteTime": "2025-05-24T18:23:04+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\LICENSE.txt", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mlv21k5csn", "Integrity": "hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery\\LICENSE.txt", "FileLength": 1117, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-ui\\jquery-ui.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-ui/jquery-ui.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "5ovtr2a0tm", "Integrity": "lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-ui\\jquery-ui.min.js", "FileLength": 255084, "LastWriteTime": "2025-05-21T18:05:31+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/dist/additional-methods#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "83jwlth58m", "Integrity": "XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "FileLength": 53033, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/dist/additional-methods.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mrlpezrjn3", "Integrity": "jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "FileLength": 22125, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/dist/jquery.validate#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lzl9nlhx6b", "Integrity": "kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "FileLength": 52536, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/dist/jquery.validate.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ag7o75518u", "Integrity": "umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "FileLength": 25308, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation/LICENSE#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "x0q3zqp4vz", "Integrity": "geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation\\LICENSE.md", "FileLength": 1117, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "47otxtyo56", "Integrity": "wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "FileLength": 19385, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4v8eqarkd7", "Integrity": "YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "FileLength": 5824, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jquery-validation-unobtrusive/LICENSE#[.{fingerprint}]?.txt", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "356vix0kms", "Integrity": "16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "FileLength": 1139, "LastWriteTime": "2025-05-19T08:03:46+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jstree\\jstree.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jstree/jstree.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "0zstsvc49u", "Integrity": "FJAUH4nh8wC0HFIQs2PRp+uGz5rjkuOU0IRDa/qUMh8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jstree\\jstree.min.js", "FileLength": 9512, "LastWriteTime": "2025-05-24T06:23:42+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jstree\\themes\\default\\style.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/jstree/themes/default/style.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "btr2h80lz8", "Integrity": "TwoJXEiJfHQg0aDknoywqfQVLWhUPG6Xt2cyaVtS7lk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\jstree\\themes\\default\\style.min.css", "FileLength": 11870, "LastWriteTime": "2025-05-24T06:26:02+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\microsoft\\signalr\\dist\\browser\\signalr.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/microsoft/signalr/dist/browser/signalr.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "m3i9f6419i", "Integrity": "4opyCjWbN8sBV1jVQ/kIcw7Vu+R42wlQa7aIfxgxNTg=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\microsoft\\signalr\\dist\\browser\\signalr.min.js", "FileLength": 47647, "LastWriteTime": "2025-05-29T10:53:18+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\modern-toast\\toast.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/modern-toast/toast#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "vwcq5cpvuf", "Integrity": "IRch838g6Dbq3+oGARMd1NH82wyEYX4C7X4YvJuUFUA=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\modern-toast\\toast.js", "FileLength": 10198, "LastWriteTime": "2025-05-24T18:17:16+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\moment\\moment.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/moment/moment.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "xnrazl4vv0", "Integrity": "c95CVJWVMOTR2b7FhjeRhPlrSVPaz5zV5eK917/s7vc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\moment\\moment.min.js", "FileLength": 58862, "LastWriteTime": "2025-05-21T18:05:33+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\README.md", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/README#[.{fingerprint}]?.md", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "lrtqx9pgo3", "Integrity": "z9GuL/khvJXCh93DEjyv3rl4hvfH2ukClQKVH/2BrRc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\README.md", "FileLength": 2403, "LastWriteTime": "2025-05-22T10:21:16+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/select2/css/select2.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "fzeltrmjmf", "Integrity": "5Rc+BOfd3BmHW7CAHZHc3Yqi83NNVIzDVwu15+HyEyE=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\css\\select2.min.css", "FileLength": 14153, "LastWriteTime": "2025-05-23T18:41:07+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap4.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/select2/css/select2-bootstrap4.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "y26l3lv3fk", "Integrity": "ChnouFUvWwco4SA0QlMptNRDv5qCcUdEgcwVG+RiqK8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\css\\select2-bootstrap4.min.css", "FileLength": 4277, "LastWriteTime": "2025-05-23T18:41:36+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/select2/css/select2-bootstrap-5-theme#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9t6rxls2rs", "Integrity": "PdA95MFDvPdYlnfpIczR/wkxW/Us9YI/lQR3OkU80OY=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.css", "FileLength": 33700, "LastWriteTime": "2022-05-07T19:11:18+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/select2/css/select2-bootstrap-5-theme.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "wfe5v7adcw", "Integrity": "XLNUEfzPGHBeStES2DbLUURZ3e793BablwzJlYj6W2Q=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.min.css", "FileLength": 31223, "LastWriteTime": "2022-05-07T19:11:18+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.rtl.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/select2/css/select2-bootstrap-5-theme.rtl#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "4a7ercfsxc", "Integrity": "XLffXXWZpoYoOoO7GfXAoBD7n/KAY+IzKgxoJkQ+7MM=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.rtl.css", "FileLength": 33691, "LastWriteTime": "2022-05-07T19:11:18+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.rtl.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/select2/css/select2-bootstrap-5-theme.rtl.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "f9k4fvkj2a", "Integrity": "bXXMs+0o746W3FX9DzwopnPEHG3bX9Ar53Wo7rv908c=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.rtl.min.css", "FileLength": 31216, "LastWriteTime": "2022-05-07T19:11:18+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\js\\select2.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/select2/js/select2.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "9nt46wh0zh", "Integrity": "FcVIknBiVRk5KLQeIBb9VQdtFRMqwffXyZ+D8q0gQro=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\select2\\js\\select2.min.js", "FileLength": 74922, "LastWriteTime": "2022-07-28T05:40:49+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\toastr\\css\\toastr.min.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/toastr/css/toastr.min#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "s50x20xwuu", "Integrity": "ENFZrbVzylNbgnXx0n3I1g//2WeO47XxoPe0vkp3NC8=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\toastr\\css\\toastr.min.css", "FileLength": 6741, "LastWriteTime": "2025-05-21T18:05:34+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\toastr\\js\\toastr.min.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/toastr/js/toastr.min#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "30edegnhg3", "Integrity": "3blsJd4Hli/7wCQ+bmgXfOdK7p/ZUMtPXY08jmxSSgk=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\toastr\\js\\toastr.min.js", "FileLength": 5537, "LastWriteTime": "2025-05-21T18:05:33+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\tree-js\\tree.css", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/tree-js/tree#[.{fingerprint}]?.css", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "mv3unr996a", "Integrity": "IgLYyHXd4toYJUsbYGNNMke1b+NW9dHhfVZWsv6D/bo=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tree-js\\tree.css", "FileLength": 5487, "LastWriteTime": "2025-05-24T18:05:32+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\tree-js\\tree.js", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "lib/tree-js/tree#[.{fingerprint}]?.js", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "1fi7ptg5iw", "Integrity": "/eVkZGlXmiRW9Mk4Dfhp2v23oQLuxtiCxHm0zlOSTRc=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\lib\\tree-js\\tree.js", "FileLength": 8093, "LastWriteTime": "2025-05-24T18:08:29+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\UploadedFiles\\20250612_194827_RMTarget.xlsx", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "UploadedFiles/20250612_194827_RMTarget#[.{fingerprint}]?.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ln9cugo920", "Integrity": "H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\UploadedFiles\\20250612_194827_RMTarget.xlsx", "FileLength": 9434, "LastWriteTime": "2025-06-12T13:48:27+00:00"}, {"Identity": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\UploadedFiles\\20250612_195042_RMTarget.xlsx", "SourceId": "SmartBI.Core", "SourceType": "Discovered", "ContentRoot": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\", "BasePath": "/", "RelativePath": "UploadedFiles/20250612_195042_RMTarget#[.{fingerprint}]?.xlsx", "AssetKind": "All", "AssetMode": "All", "AssetRole": "Primary", "AssetMergeBehavior": "", "AssetMergeSource": "", "RelatedAsset": "", "AssetTraitName": "", "AssetTraitValue": "", "Fingerprint": "ln9cugo920", "Integrity": "H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI=", "CopyToOutputDirectory": "Never", "CopyToPublishDirectory": "PreserveNewest", "OriginalItemSpec": "wwwroot\\UploadedFiles\\20250612_195042_RMTarget.xlsx", "FileLength": 9434, "LastWriteTime": "2025-06-12T13:50:42+00:00"}], "Endpoints": [{"Route": "assets/css/material-dashboard.min.caks9akf9x.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\css\\material-dashboard.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "510314"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tGb1izvVI/8yoOsJTAz1XXSP9mLE6uoYanVq2eG/mLs=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "caks9akf9x"}, {"Name": "label", "Value": "assets/css/material-dashboard.min.css"}, {"Name": "integrity", "Value": "sha256-tGb1izvVI/8yoOsJTAz1XXSP9mLE6uoYanVq2eG/mLs="}]}, {"Route": "assets/css/material-dashboard.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\css\\material-dashboard.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "510314"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"tGb1izvVI/8yoOsJTAz1XXSP9mLE6uoYanVq2eG/mLs=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-tGb1izvVI/8yoOsJTAz1XXSP9mLE6uoYanVq2eG/mLs="}]}, {"Route": "assets/demo/demo.354obk1sri.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\demo\\demo.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33222"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9/VAbH59DmEMlEl0xc/H3edE45YL7yohC+H/a43R07k=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "354obk1sri"}, {"Name": "label", "Value": "assets/demo/demo.js"}, {"Name": "integrity", "Value": "sha256-9/VAbH59DmEMlEl0xc/H3edE45YL7yohC+H/a43R07k="}]}, {"Route": "assets/demo/demo.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\demo\\demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "846"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V2OCzdd/cVsRD9+GDq6zl2pC2eYExMK8yYf/S2HOdRk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V2OCzdd/cVsRD9+GDq6zl2pC2eYExMK8yYf/S2HOdRk="}]}, {"Route": "assets/demo/demo.j9o6ft6t9u.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\demo\\demo.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "846"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V2OCzdd/cVsRD9+GDq6zl2pC2eYExMK8yYf/S2HOdRk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j9o6ft6t9u"}, {"Name": "label", "Value": "assets/demo/demo.css"}, {"Name": "integrity", "Value": "sha256-V2OCzdd/cVsRD9+GDq6zl2pC2eYExMK8yYf/S2HOdRk="}]}, {"Route": "assets/demo/demo.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\demo\\demo.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33222"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"9/VAbH59DmEMlEl0xc/H3edE45YL7yohC+H/a43R07k=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9/VAbH59DmEMlEl0xc/H3edE45YL7yohC+H/a43R07k="}]}, {"Route": "assets/demo/jquery.sharrre.9ic6jpultn.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\demo\\jquery.sharrre.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25403"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2SCHU+/bxDGmp8izqjvlfW4acosVyX4uAMszQtFv24=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ic6jpultn"}, {"Name": "label", "Value": "assets/demo/jquery.sharrre.js"}, {"Name": "integrity", "Value": "sha256-L2SCHU+/bxDGmp8izqjvlfW4acosVyX4uAMszQtFv24="}]}, {"Route": "assets/demo/jquery.sharrre.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\demo\\jquery.sharrre.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25403"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"L2SCHU+/bxDGmp8izqjvlfW4acosVyX4uAMszQtFv24=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-L2SCHU+/bxDGmp8izqjvlfW4acosVyX4uAMszQtFv24="}]}, {"Route": "assets/img/apple-icon.5vxk3yf90w.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\apple-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2446"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"jzxI3w03y2/rbD4IlK9mBTr8iipnEcR0LEasSoythP8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5vxk3yf90w"}, {"Name": "label", "Value": "assets/img/apple-icon.png"}, {"Name": "integrity", "Value": "sha256-jzxI3w03y2/rbD4IlK9mBTr8iipnEcR0LEasSoythP8="}]}, {"Route": "assets/img/apple-icon.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\apple-icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2446"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"jzxI3w03y2/rbD4IlK9mBTr8iipnEcR0LEasSoythP8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jzxI3w03y2/rbD4IlK9mBTr8iipnEcR0LEasSoythP8="}]}, {"Route": "assets/img/bg-pricing.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\bg-pricing.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "725542"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"jKWDDZXZ6bzUN0HuSkgoT5H71UzDAQg95meLOaeZios=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jKWDDZXZ6bzUN0HuSkgoT5H71UzDAQg95meLOaeZios="}]}, {"Route": "assets/img/bg-pricing.wphb465enx.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\bg-pricing.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "725542"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"jKWDDZXZ6bzUN0HuSkgoT5H71UzDAQg95meLOaeZios=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wphb465enx"}, {"Name": "label", "Value": "assets/img/bg-pricing.jpg"}, {"Name": "integrity", "Value": "sha256-jKWDDZXZ6bzUN0HuSkgoT5H71UzDAQg95meLOaeZios="}]}, {"Route": "assets/img/card-1.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\card-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "233583"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"jZb/JcmQc4nNoZPnHBRvc4v89WaxEA6xipV9yr8eI7Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jZb/JcmQc4nNoZPnHBRvc4v89WaxEA6xipV9yr8eI7Q="}]}, {"Route": "assets/img/card-1.zgtxmg0xkw.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\card-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "233583"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"jZb/JcmQc4nNoZPnHBRvc4v89WaxEA6xipV9yr8eI7Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "zgtxmg0xkw"}, {"Name": "label", "Value": "assets/img/card-1.jpg"}, {"Name": "integrity", "Value": "sha256-jZb/JcmQc4nNoZPnHBRvc4v89WaxEA6xipV9yr8eI7Q="}]}, {"Route": "assets/img/card-2.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\card-2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "251125"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b5nOIvGjayk1c7+0Gc1VsTRUALR3gkakBPbrSdkZaDk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-b5nOIvGjayk1c7+0Gc1VsTRUALR3gkakBPbrSdkZaDk="}]}, {"Route": "assets/img/card-2.naznnc21oz.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\card-2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "251125"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"b5nOIvGjayk1c7+0Gc1VsTRUALR3gkakBPbrSdkZaDk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "naznnc21oz"}, {"Name": "label", "Value": "assets/img/card-2.jpg"}, {"Name": "integrity", "Value": "sha256-b5nOIvGjayk1c7+0Gc1VsTRUALR3gkakBPbrSdkZaDk="}]}, {"Route": "assets/img/card-3.044x3xumdp.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\card-3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "123917"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EPpBgzDKnChY3lXoXF5ssiGCwz6NWGMKajHU1mb/bnI=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "044x3xumdp"}, {"Name": "label", "Value": "assets/img/card-3.jpg"}, {"Name": "integrity", "Value": "sha256-EPpBgzDKnChY3lXoXF5ssiGCwz6NWGMKajHU1mb/bnI="}]}, {"Route": "assets/img/card-3.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\card-3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "123917"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EPpBgzDKnChY3lXoXF5ssiGCwz6NWGMKajHU1mb/bnI=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EPpBgzDKnChY3lXoXF5ssiGCwz6NWGMKajHU1mb/bnI="}]}, {"Route": "assets/img/clint-mckoy.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\clint-mckoy.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307193"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"kCFHoo1+00GvTsESsteimZQTFHRE9XzLu9/ufsRDu6Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kCFHoo1+00GvTsESsteimZQTFHRE9XzLu9/ufsRDu6Q="}]}, {"Route": "assets/img/clint-mckoy.sh3bsj7358.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\clint-mckoy.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "307193"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"kCFHoo1+00GvTsESsteimZQTFHRE9XzLu9/ufsRDu6Q=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sh3bsj7358"}, {"Name": "label", "Value": "assets/img/clint-mckoy.jpg"}, {"Name": "integrity", "Value": "sha256-kCFHoo1+00GvTsESsteimZQTFHRE9XzLu9/ufsRDu6Q="}]}, {"Route": "assets/img/default-avatar.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\default-avatar.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2864"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4zASlHZ8cnc2SZsIuhZmp2tzgWyUnfdV4W6qNvSxTwY=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4zASlHZ8cnc2SZsIuhZmp2tzgWyUnfdV4W6qNvSxTwY="}]}, {"Route": "assets/img/default-avatar.vdblgpfca6.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\default-avatar.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2864"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"4zASlHZ8cnc2SZsIuhZmp2tzgWyUnfdV4W6qNvSxTwY=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vdblgpfca6"}, {"Name": "label", "Value": "assets/img/default-avatar.png"}, {"Name": "integrity", "Value": "sha256-4zASlHZ8cnc2SZsIuhZmp2tzgWyUnfdV4W6qNvSxTwY="}]}, {"Route": "assets/img/faces/avatar.40vlznypoo.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\faces\\avatar.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85214"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"NK9x+jG3V65wSfrmP7e50hdMEzJBVkbjQ1L3NnhcmaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "40vlznypoo"}, {"Name": "label", "Value": "assets/img/faces/avatar.jpg"}, {"Name": "integrity", "Value": "sha256-NK9x+jG3V65wSfrmP7e50hdMEzJBVkbjQ1L3NnhcmaE="}]}, {"Route": "assets/img/faces/avatar.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\faces\\avatar.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85214"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"NK9x+jG3V65wSfrmP7e50hdMEzJBVkbjQ1L3NnhcmaE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NK9x+jG3V65wSfrmP7e50hdMEzJBVkbjQ1L3NnhcmaE="}]}, {"Route": "assets/img/faces/card-profile1-square.c9ufb2enf1.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\faces\\card-profile1-square.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74370"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EOZXeW64C+HWjfkninbjiNdZE187UISE9fNJGicxWw8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c9ufb2enf1"}, {"Name": "label", "Value": "assets/img/faces/card-profile1-square.jpg"}, {"Name": "integrity", "Value": "sha256-EOZXeW64C+HWjfkninbjiNdZE187UISE9fNJGicxWw8="}]}, {"Route": "assets/img/faces/card-profile1-square.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\faces\\card-profile1-square.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74370"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"EOZXeW64C+HWjfkninbjiNdZE187UISE9fNJGicxWw8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EOZXeW64C+HWjfkninbjiNdZE187UISE9fNJGicxWw8="}]}, {"Route": "assets/img/faces/marc.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\faces\\marc.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54633"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"zfXQMlSus3TGFULCvzA/DrxPjo9OUAOpSAP0mCUAVfU=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zfXQMlSus3TGFULCvzA/DrxPjo9OUAOpSAP0mCUAVfU="}]}, {"Route": "assets/img/faces/marc.ri90un01rk.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\faces\\marc.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "54633"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"zfXQMlSus3TGFULCvzA/DrxPjo9OUAOpSAP0mCUAVfU=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ri90un01rk"}, {"Name": "label", "Value": "assets/img/faces/marc.jpg"}, {"Name": "integrity", "Value": "sha256-zfXQMlSus3TGFULCvzA/DrxPjo9OUAOpSAP0mCUAVfU="}]}, {"Route": "assets/img/favicon.b3o3hrm6bc.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2761"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"t3lRlqdYsSFqKN4/OctPjVF+bvJui4hZi0M3ojeLBAk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b3o3hrm6bc"}, {"Name": "label", "Value": "assets/img/favicon.png"}, {"Name": "integrity", "Value": "sha256-t3lRlqdYsSFqKN4/OctPjVF+bvJui4hZi0M3ojeLBAk="}]}, {"Route": "assets/img/favicon.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\favicon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2761"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"t3lRlqdYsSFqKN4/OctPjVF+bvJui4hZi0M3ojeLBAk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-t3lRlqdYsSFqKN4/OctPjVF+bvJui4hZi0M3ojeLBAk="}]}, {"Route": "assets/img/flags/AU.izuvgmyjg2.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\AU.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3488"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nrYxpZzGRPlTWe6nOVtM+DUPxQ7NgXc+mHc+yX6wSQE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "izuvgmyjg2"}, {"Name": "label", "Value": "assets/img/flags/AU.png"}, {"Name": "integrity", "Value": "sha256-nrYxpZzGRPlTWe6nOVtM+DUPxQ7NgXc+mHc+yX6wSQE="}]}, {"Route": "assets/img/flags/AU.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\AU.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3488"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"nrYxpZzGRPlTWe6nOVtM+DUPxQ7NgXc+mHc+yX6wSQE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-nrYxpZzGRPlTWe6nOVtM+DUPxQ7NgXc+mHc+yX6wSQE="}]}, {"Route": "assets/img/flags/BR.fyhwl7falv.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\BR.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3594"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"50P93VSov4FCbT1H7K9OC/2JAUSf0wDAqGWWolvTuyE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fyhwl7falv"}, {"Name": "label", "Value": "assets/img/flags/BR.png"}, {"Name": "integrity", "Value": "sha256-50P93VSov4FCbT1H7K9OC/2JAUSf0wDAqGWWolvTuyE="}]}, {"Route": "assets/img/flags/BR.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\BR.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3594"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"50P93VSov4FCbT1H7K9OC/2JAUSf0wDAqGWWolvTuyE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-50P93VSov4FCbT1H7K9OC/2JAUSf0wDAqGWWolvTuyE="}]}, {"Route": "assets/img/flags/DE.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\DE.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3109"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"5PT2VlpKgZecxnR3xHVmc+B6J+GgK6sIyRyTF21RYI4=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5PT2VlpKgZecxnR3xHVmc+B6J+GgK6sIyRyTF21RYI4="}]}, {"Route": "assets/img/flags/DE.sk7vbm1zt0.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\DE.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3109"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"5PT2VlpKgZecxnR3xHVmc+B6J+GgK6sIyRyTF21RYI4=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "sk7vbm1zt0"}, {"Name": "label", "Value": "assets/img/flags/DE.png"}, {"Name": "integrity", "Value": "sha256-5PT2VlpKgZecxnR3xHVmc+B6J+GgK6sIyRyTF21RYI4="}]}, {"Route": "assets/img/flags/GB.0l4h0mfn80.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\GB.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3543"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"dPlnhNwFvVNw8lsNhQ0P16RVQizvr79SRQiZq6XKjNA=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0l4h0mfn80"}, {"Name": "label", "Value": "assets/img/flags/GB.png"}, {"Name": "integrity", "Value": "sha256-dPlnhNwFvVNw8lsNhQ0P16RVQizvr79SRQiZq6XKjNA="}]}, {"Route": "assets/img/flags/GB.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\GB.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3543"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"dPlnhNwFvVNw8lsNhQ0P16RVQizvr79SRQiZq6XKjNA=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-dPlnhNwFvVNw8lsNhQ0P16RVQizvr79SRQiZq6XKjNA="}]}, {"Route": "assets/img/flags/RO.deo58m671w.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\RO.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3298"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xUq6vFlNkU8HntMCQn5LMcJK91QgXwEy8PU4QxmqrpE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "deo58m671w"}, {"Name": "label", "Value": "assets/img/flags/RO.png"}, {"Name": "integrity", "Value": "sha256-xUq6vFlNkU8HntMCQn5LMcJK91QgXwEy8PU4QxmqrpE="}]}, {"Route": "assets/img/flags/RO.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\RO.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3298"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"xUq6vFlNkU8HntMCQn5LMcJK91QgXwEy8PU4QxmqrpE=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xUq6vFlNkU8HntMCQn5LMcJK91QgXwEy8PU4QxmqrpE="}]}, {"Route": "assets/img/flags/US.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\US.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3310"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"25l0fXukLYXDZuyjRTFZ+F2qqan+VxwCNtaArN49GWQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-25l0fXukLYXDZuyjRTFZ+F2qqan+VxwCNtaArN49GWQ="}]}, {"Route": "assets/img/flags/US.xdruoe7wo0.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\flags\\US.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3310"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"25l0fXukLYXDZuyjRTFZ+F2qqan+VxwCNtaArN49GWQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xdruoe7wo0"}, {"Name": "label", "Value": "assets/img/flags/US.png"}, {"Name": "integrity", "Value": "sha256-25l0fXukLYXDZuyjRTFZ+F2qqan+VxwCNtaArN49GWQ="}]}, {"Route": "assets/img/image_placeholder.9ag3fgu5jm.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\image_placeholder.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44534"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"z7vcj9j2QtDCNzMHdGLGwbHNDjGTPA0itLcULFi5YEU=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9ag3fgu5jm"}, {"Name": "label", "Value": "assets/img/image_placeholder.jpg"}, {"Name": "integrity", "Value": "sha256-z7vcj9j2QtDCNzMHdGLGwbHNDjGTPA0itLcULFi5YEU="}]}, {"Route": "assets/img/image_placeholder.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\image_placeholder.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "44534"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"z7vcj9j2QtDCNzMHdGLGwbHNDjGTPA0itLcULFi5YEU=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z7vcj9j2QtDCNzMHdGLGwbHNDjGTPA0itLcULFi5YEU="}]}, {"Route": "assets/img/lock.cj7xm7a593.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\lock.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1006309"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"aNaol4P7J+wUmCb2bCT0Z2h8Fm//Q+DiBw8byqqWTew=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cj7xm7a593"}, {"Name": "label", "Value": "assets/img/lock.jpg"}, {"Name": "integrity", "Value": "sha256-aNaol4P7J+wUmCb2bCT0Z2h8Fm//Q+DiBw8byqqWTew="}]}, {"Route": "assets/img/lock.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\lock.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1006309"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"aNaol4P7J+wUmCb2bCT0Z2h8Fm//Q+DiBw8byqqWTew=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-aNaol4P7J+wUmCb2bCT0Z2h8Fm//Q+DiBw8byqqWTew="}]}, {"Route": "assets/img/login.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\login.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "547677"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ehqB+ggitdIzswr0+FMKafv28GdESoYcNyxaSvYDekM=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ehqB+ggitdIzswr0+FMKafv28GdESoYcNyxaSvYDekM="}]}, {"Route": "assets/img/login.u7y13t8v4m.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\login.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "547677"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ehqB+ggitdIzswr0+FMKafv28GdESoYcNyxaSvYDekM=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "u7y13t8v4m"}, {"Name": "label", "Value": "assets/img/login.jpg"}, {"Name": "integrity", "Value": "sha256-ehqB+ggitdIzswr0+FMKafv28GdESoYcNyxaSvYDekM="}]}, {"Route": "assets/img/placeholder.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\placeholder.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18015"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZbW3USZZ7URX1dRQEGZBZMsTtYCbF7fxPtH4z4oQ5r0=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZbW3USZZ7URX1dRQEGZBZMsTtYCbF7fxPtH4z4oQ5r0="}]}, {"Route": "assets/img/placeholder.l8zcwmw9uc.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\placeholder.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18015"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"ZbW3USZZ7URX1dRQEGZBZMsTtYCbF7fxPtH4z4oQ5r0=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "l8zcwmw9uc"}, {"Name": "label", "Value": "assets/img/placeholder.jpg"}, {"Name": "integrity", "Value": "sha256-ZbW3USZZ7URX1dRQEGZBZMsTtYCbF7fxPtH4z4oQ5r0="}]}, {"Route": "assets/img/product1.fl6f6boglu.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\product1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12146"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"3d7LCq6OjSr3ykqg+U0k0VP5B6fNUIc617PxmfrSyM8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fl6f6boglu"}, {"Name": "label", "Value": "assets/img/product1.jpg"}, {"Name": "integrity", "Value": "sha256-3d7LCq6OjSr3ykqg+U0k0VP5B6fNUIc617PxmfrSyM8="}]}, {"Route": "assets/img/product1.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\product1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12146"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"3d7LCq6OjSr3ykqg+U0k0VP5B6fNUIc617PxmfrSyM8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3d7LCq6OjSr3ykqg+U0k0VP5B6fNUIc617PxmfrSyM8="}]}, {"Route": "assets/img/product2.1kfwu09a04.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\product2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9677"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"P/thAvbw1LnY0F7Y+n3OqSXif1bUsT/wAjWIB0Fwk2E=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1kfwu09a04"}, {"Name": "label", "Value": "assets/img/product2.jpg"}, {"Name": "integrity", "Value": "sha256-P/thAvbw1LnY0F7Y+n3OqSXif1bUsT/wAjWIB0Fwk2E="}]}, {"Route": "assets/img/product2.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\product2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9677"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"P/thAvbw1LnY0F7Y+n3OqSXif1bUsT/wAjWIB0Fwk2E=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P/thAvbw1LnY0F7Y+n3OqSXif1bUsT/wAjWIB0Fwk2E="}]}, {"Route": "assets/img/product3.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\product3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21624"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"8Lekzy0sN2dRFotjK5ATUSuz6vSPaf6wz5zC0gBMLY4=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-8Lekzy0sN2dRFotjK5ATUSuz6vSPaf6wz5zC0gBMLY4="}]}, {"Route": "assets/img/product3.oityib5ljs.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\product3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21624"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"8Lekzy0sN2dRFotjK5ATUSuz6vSPaf6wz5zC0gBMLY4=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "oityib5ljs"}, {"Name": "label", "Value": "assets/img/product3.jpg"}, {"Name": "integrity", "Value": "sha256-8Lekzy0sN2dRFotjK5ATUSuz6vSPaf6wz5zC0gBMLY4="}]}, {"Route": "assets/img/register.c14zu2kyvj.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\register.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "545914"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MOXbuwMJvIsonWECeP8dLUyQiUsRxxArzVfILb8hgG8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c14zu2kyvj"}, {"Name": "label", "Value": "assets/img/register.jpg"}, {"Name": "integrity", "Value": "sha256-MOXbuwMJvIsonWECeP8dLUyQiUsRxxArzVfILb8hgG8="}]}, {"Route": "assets/img/register.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\register.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "545914"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"MOXbuwMJvIsonWECeP8dLUyQiUsRxxArzVfILb8hgG8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-MOXbuwMJvIsonWECeP8dLUyQiUsRxxArzVfILb8hgG8="}]}, {"Route": "assets/img/sidebar-1.9g5eqnvc3f.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189310"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GRmmooKm4U9amJHIL4unLLzMCwgb3ScuseJhgB6AAGY=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9g5eqnvc3f"}, {"Name": "label", "Value": "assets/img/sidebar-1.jpg"}, {"Name": "integrity", "Value": "sha256-GR<PERSON>ooKm4U9amJHIL4unLLzMCwgb3ScuseJhgB6AAGY="}]}, {"Route": "assets/img/sidebar-1.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-1.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "189310"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"GRmmooKm4U9amJHIL4unLLzMCwgb3ScuseJhgB6AAGY=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GR<PERSON>ooKm4U9amJHIL4unLLzMCwgb3ScuseJhgB6AAGY="}]}, {"Route": "assets/img/sidebar-2.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "393121"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"sfbfYBML03cnBuMH3tlV8dyWst8g/oupl1ZDYNSzAkg=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sfbfYBML03cnBuMH3tlV8dyWst8g/oupl1ZDYNSzAkg="}]}, {"Route": "assets/img/sidebar-2.p2xdqlcugr.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-2.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "393121"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"sfbfYBML03cnBuMH3tlV8dyWst8g/oupl1ZDYNSzAkg=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "p2xdqlcugr"}, {"Name": "label", "Value": "assets/img/sidebar-2.jpg"}, {"Name": "integrity", "Value": "sha256-sfbfYBML03cnBuMH3tlV8dyWst8g/oupl1ZDYNSzAkg="}]}, {"Route": "assets/img/sidebar-3.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "377600"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"lsHjy58jaUMJhm7dnzUWYHVKzlWW4lDLoYguFtY/7VY=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lsHjy58jaUMJhm7dnzUWYHVKzlWW4lDLoYguFtY/7VY="}]}, {"Route": "assets/img/sidebar-3.q3hkib9lo2.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-3.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "377600"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"lsHjy58jaUMJhm7dnzUWYHVKzlWW4lDLoYguFtY/7VY=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "q3hkib9lo2"}, {"Name": "label", "Value": "assets/img/sidebar-3.jpg"}, {"Name": "integrity", "Value": "sha256-lsHjy58jaUMJhm7dnzUWYHVKzlWW4lDLoYguFtY/7VY="}]}, {"Route": "assets/img/sidebar-4.2pbm105vcv.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "386464"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Smr5o2sxf0/dySEb6fNnxzy6XyBFuhzziY9WYU1NFzI=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2pbm105vcv"}, {"Name": "label", "Value": "assets/img/sidebar-4.jpg"}, {"Name": "integrity", "Value": "sha256-Smr5o2sxf0/dySEb6fNnxzy6XyBFuhzziY9WYU1NFzI="}]}, {"Route": "assets/img/sidebar-4.jpg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\img\\sidebar-4.jpg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "386464"}, {"Name": "Content-Type", "Value": "image/jpeg"}, {"Name": "ETag", "Value": "\"Smr5o2sxf0/dySEb6fNnxzy6XyBFuhzziY9WYU1NFzI=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Smr5o2sxf0/dySEb6fNnxzy6XyBFuhzziY9WYU1NFzI="}]}, {"Route": "assets/js/material-dashboard.min.7yry9pyep8.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\material-dashboard.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11942"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/w6a41SiizcbtUWuJr6tuKgPkxv+1DBlHxRq31aqBa0=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7yry9pyep8"}, {"Name": "label", "Value": "assets/js/material-dashboard.min.js"}, {"Name": "integrity", "Value": "sha256-/w6a41SiizcbtUWuJr6tuKgPkxv+1DBlHxRq31aqBa0="}]}, {"Route": "assets/js/material-dashboard.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\material-dashboard.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11942"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/w6a41SiizcbtUWuJr6tuKgPkxv+1DBlHxRq31aqBa0=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/w6a41SiizcbtUWuJr6tuKgPkxv+1DBlHxRq31aqBa0="}]}, {"Route": "assets/js/plugins/arrive.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\arrive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5091"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TKikMGzFMPdZPL/vRa0FZflEy5bP6D4sPgHQ/PPh+ss=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TKikMGzFMPdZPL/vRa0FZflEy5bP6D4sPgHQ/PPh+ss="}]}, {"Route": "assets/js/plugins/arrive.min.wv1fh4f76g.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\arrive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5091"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"TKikMGzFMPdZPL/vRa0FZflEy5bP6D4sPgHQ/PPh+ss=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wv1fh4f76g"}, {"Name": "label", "Value": "assets/js/plugins/arrive.min.js"}, {"Name": "integrity", "Value": "sha256-TKikMGzFMPdZPL/vRa0FZflEy5bP6D4sPgHQ/PPh+ss="}]}, {"Route": "assets/js/plugins/bootstrap-datetimepicker.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-datetimepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "40248"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yXozLHG4zXJz4wI/C/EKq1bg42evpBQYzC4zVZO0I7c=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-yXozLHG4zXJz4wI/C/EKq1bg42evpBQYzC4zVZO0I7c="}]}, {"Route": "assets/js/plugins/bootstrap-datetimepicker.min.lh15wxqkcf.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-datetimepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "40248"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"yXozLHG4zXJz4wI/C/EKq1bg42evpBQYzC4zVZO0I7c=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lh15wxqkcf"}, {"Name": "label", "Value": "assets/js/plugins/bootstrap-datetimepicker.min.js"}, {"Name": "integrity", "Value": "sha256-yXozLHG4zXJz4wI/C/EKq1bg42evpBQYzC4zVZO0I7c="}]}, {"Route": "assets/js/plugins/bootstrap-notify.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-notify.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IRIPV/8L/RXy6fZiaVbKuDkIsonzfALPabfYEFI2skk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IRIPV/8L/RXy6fZiaVbKuDkIsonzfALPabfYEFI2skk="}]}, {"Route": "assets/js/plugins/bootstrap-notify.nexbo68se3.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-notify.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IRIPV/8L/RXy6fZiaVbKuDkIsonzfALPabfYEFI2skk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nexbo68se3"}, {"Name": "label", "Value": "assets/js/plugins/bootstrap-notify.js"}, {"Name": "integrity", "Value": "sha256-IRIPV/8L/RXy6fZiaVbKuDkIsonzfALPabfYEFI2skk="}]}, {"Route": "assets/js/plugins/bootstrap-selectpicker.hay3z6e75s.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-selectpicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "95046"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"p7SglDuNQuTycwUFPJX9JbbLH2zlQV69e1ciW7PbvpM=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hay3z6e75s"}, {"Name": "label", "Value": "assets/js/plugins/bootstrap-selectpicker.js"}, {"Name": "integrity", "Value": "sha256-p7SglDuNQuTycwUFPJX9JbbLH2zlQV69e1ciW7PbvpM="}]}, {"Route": "assets/js/plugins/bootstrap-selectpicker.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-selectpicker.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "95046"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"p7SglDuNQuTycwUFPJX9JbbLH2zlQV69e1ciW7PbvpM=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p7SglDuNQuTycwUFPJX9JbbLH2zlQV69e1ciW7PbvpM="}]}, {"Route": "assets/js/plugins/bootstrap-tagsinput.jn76hyvc52.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-tagsinput.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P9VMZ+YS7WYtSicYoqVouHWczPdXgaxQ2LmwI10mvi8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jn76hyvc52"}, {"Name": "label", "Value": "assets/js/plugins/bootstrap-tagsinput.js"}, {"Name": "integrity", "Value": "sha256-P9VMZ+YS7WYtSicYoqVouHWczPdXgaxQ2LmwI10mvi8="}]}, {"Route": "assets/js/plugins/bootstrap-tagsinput.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\bootstrap-tagsinput.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22293"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"P9VMZ+YS7WYtSicYoqVouHWczPdXgaxQ2LmwI10mvi8=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P9VMZ+YS7WYtSicYoqVouHWczPdXgaxQ2LmwI10mvi8="}]}, {"Route": "assets/js/plugins/chartist.min.32mebuojjq.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\chartist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "40174"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"K8omIIjKNHAvHgZfw9xI9+HoypjiLDr8HhN3MUlWUXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "32mebuojjq"}, {"Name": "label", "Value": "assets/js/plugins/chartist.min.js"}, {"Name": "integrity", "Value": "sha256-K8omIIjKNHAvHgZfw9xI9+HoypjiLDr8HhN3MUlWUXo="}]}, {"Route": "assets/js/plugins/chartist.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\chartist.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "40174"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"K8omIIjKNHAvHgZfw9xI9+HoypjiLDr8HhN3MUlWUXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K8omIIjKNHAvHgZfw9xI9+HoypjiLDr8HhN3MUlWUXo="}]}, {"Route": "assets/js/plugins/fullcalendar.min.gr8nfsd9b7.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\fullcalendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "213775"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HPsVxOHjSoFxW4GXTppL59Q3yjo7AnEjo7Fq2yVYHIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gr8nfsd9b7"}, {"Name": "label", "Value": "assets/js/plugins/fullcalendar.min.js"}, {"Name": "integrity", "Value": "sha256-HPsVxOHjSoFxW4GXTppL59Q3yjo7AnEjo7Fq2yVYHIc="}]}, {"Route": "assets/js/plugins/fullcalendar.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\fullcalendar.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "213775"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"HPsVxOHjSoFxW4GXTppL59Q3yjo7AnEjo7Fq2yVYHIc=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-HPsVxOHjSoFxW4GXTppL59Q3yjo7AnEjo7Fq2yVYHIc="}]}, {"Route": "assets/js/plugins/jasny-bootstrap.min.ibzoq4ggt8.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jasny-bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16780"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zrKYjrV5tdhLTivmOO9TAI5x6i5dcMVO4YOi/zUAqrk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ibzoq4ggt8"}, {"Name": "label", "Value": "assets/js/plugins/jasny-bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-zrKYjrV5tdhLTivmOO9TAI5x6i5dcMVO4YOi/zUAqrk="}]}, {"Route": "assets/js/plugins/jasny-bootstrap.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jasny-bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16780"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"zrKYjrV5tdhLTivmOO9TAI5x6i5dcMVO4YOi/zUAqrk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-zrKYjrV5tdhLTivmOO9TAI5x6i5dcMVO4YOi/zUAqrk="}]}, {"Route": "assets/js/plugins/jquery.bootstrap-wizard.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery.bootstrap-wizard.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14594"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pea1hrFL3G3NTw+WkiM8LOiwrjtDqvsXFBU48D3N40s=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-pea1hrFL3G3NTw+WkiM8LOiwrjtDqvsXFBU48D3N40s="}]}, {"Route": "assets/js/plugins/jquery.bootstrap-wizard.n1y09h6ijo.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery.bootstrap-wizard.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14594"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"pea1hrFL3G3NTw+WkiM8LOiwrjtDqvsXFBU48D3N40s=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "n1y09h6ijo"}, {"Name": "label", "Value": "assets/js/plugins/jquery.bootstrap-wizard.js"}, {"Name": "integrity", "Value": "sha256-pea1hrFL3G3NTw+WkiM8LOiwrjtDqvsXFBU48D3N40s="}]}, {"Route": "assets/js/plugins/jquery.dataTables.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery.dataTables.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2183500"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"K/pY0C5JwJnCTDpMZYh9MS+G019rFNvawH5HrCYip50=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-K/pY0C5JwJnCTDpMZYh9MS+G019rFNvawH5HrCYip50="}]}, {"Route": "assets/js/plugins/jquery.dataTables.min.ti3sp3yx4y.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery.dataTables.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2183500"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"K/pY0C5JwJnCTDpMZYh9MS+G019rFNvawH5HrCYip50=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ti3sp3yx4y"}, {"Name": "label", "Value": "assets/js/plugins/jquery.dataTables.min.js"}, {"Name": "integrity", "Value": "sha256-K/pY0C5JwJnCTDpMZYh9MS+G019rFNvawH5HrCYip50="}]}, {"Route": "assets/js/plugins/jquery.validate.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21090"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Lj47JmDL+qxf6/elCzHQSUFZmJYmqEECssN5LP/ifRM=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Lj47JmDL+qxf6/elCzHQSUFZmJYmqEECssN5LP/ifRM="}]}, {"Route": "assets/js/plugins/jquery.validate.min.m64ca5mr57.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21090"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Lj47JmDL+qxf6/elCzHQSUFZmJYmqEECssN5LP/ifRM=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m64ca5mr57"}, {"Name": "label", "Value": "assets/js/plugins/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-Lj47JmDL+qxf6/elCzHQSUFZmJYmqEECssN5LP/ifRM="}]}, {"Route": "assets/js/plugins/jquery-jvectormap.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery-jvectormap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "269400"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FGmYM0oJCu3L/583JPu4WGlaWxXMTeFtUY88Zf1Xay0=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FGmYM0oJCu3L/583JPu4WGlaWxXMTeFtUY88Zf1Xay0="}]}, {"Route": "assets/js/plugins/jquery-jvectormap.s8frxs2lby.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\jquery-jvectormap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "269400"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FGmYM0oJCu3L/583JPu4WGlaWxXMTeFtUY88Zf1Xay0=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s8frxs2lby"}, {"Name": "label", "Value": "assets/js/plugins/jquery-jvectormap.js"}, {"Name": "integrity", "Value": "sha256-FGmYM0oJCu3L/583JPu4WGlaWxXMTeFtUY88Zf1Xay0="}]}, {"Route": "assets/js/plugins/moment.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\moment.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58687"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"t6K97JKAnxSnB126XGEiWEQsaC+JPPO6mDJ5aa3IFug=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-t6K97JKAnxSnB126XGEiWEQsaC+JPPO6mDJ5aa3IFug="}]}, {"Route": "assets/js/plugins/moment.min.xmlvivfnb8.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\moment.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58687"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"t6K97JKAnxSnB126XGEiWEQsaC+JPPO6mDJ5aa3IFug=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xmlvivfnb8"}, {"Name": "label", "Value": "assets/js/plugins/moment.min.js"}, {"Name": "integrity", "Value": "sha256-t6K97JKAnxSnB126XGEiWEQsaC+JPPO6mDJ5aa3IFug="}]}, {"Route": "assets/js/plugins/nouislider.min.itc35f2ukd.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\nouislider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21163"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WjsMa1Nc2pi7iNUPSi/IwsAM1/HvFZOxrJq8gRcf9XM=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "itc35f2ukd"}, {"Name": "label", "Value": "assets/js/plugins/nouislider.min.js"}, {"Name": "integrity", "Value": "sha256-WjsMa1Nc2pi7iNUPSi/IwsAM1/HvFZOxrJq8gRcf9XM="}]}, {"Route": "assets/js/plugins/nouislider.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\nouislider.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "21163"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WjsMa1Nc2pi7iNUPSi/IwsAM1/HvFZOxrJq8gRcf9XM=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WjsMa1Nc2pi7iNUPSi/IwsAM1/HvFZOxrJq8gRcf9XM="}]}, {"Route": "assets/js/plugins/perfect-scrollbar.jquery.min.j1rmxb6kr8.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\perfect-scrollbar.jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25332"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"maBqL1yKR1eyJOI0j6Ns5b5XvNnRtih0udc0TLUJXQQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j1rmxb6kr8"}, {"Name": "label", "Value": "assets/js/plugins/perfect-scrollbar.jquery.min.js"}, {"Name": "integrity", "Value": "sha256-maBqL1yKR1eyJOI0j6Ns5b5XvNnRtih0udc0TLUJXQQ="}]}, {"Route": "assets/js/plugins/perfect-scrollbar.jquery.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\perfect-scrollbar.jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25332"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"maBqL1yKR1eyJOI0j6Ns5b5XvNnRtih0udc0TLUJXQQ=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-maBqL1yKR1eyJOI0j6Ns5b5XvNnRtih0udc0TLUJXQQ="}]}, {"Route": "assets/js/plugins/sweetalert2.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\sweetalert2.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116600"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y3TyVu7YdsKONZZG+bpY6L/VoWpp73V4XpZybzNLyJk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y3TyVu7YdsKONZZG+bpY6L/VoWpp73V4XpZybzNLyJk="}]}, {"Route": "assets/js/plugins/sweetalert2.x521yyi2ct.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\assets\\js\\plugins\\sweetalert2.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116600"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"y3TyVu7YdsKONZZG+bpY6L/VoWpp73V4XpZybzNLyJk=\""}, {"Name": "Last-Modified", "Value": "Thu, 24 Jan 2019 14:57:01 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x521yyi2ct"}, {"Name": "label", "Value": "assets/js/plugins/sweetalert2.js"}, {"Name": "integrity", "Value": "sha256-y3TyVu7YdsKONZZG+bpY6L/VoWpp73V4XpZybzNLyJk="}]}, {"Route": "Content/image/logo.c1xu3sjlm1.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11410"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"QLpvAHYFV23XX4qzhRt03Hl73CF5JzWTlo4YhcelGXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 11 Jun 2015 12:52:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c1xu3sjlm1"}, {"Name": "label", "Value": "Content/image/logo.png"}, {"Name": "integrity", "Value": "sha256-QLpvAHYFV23XX4qzhRt03Hl73CF5JzWTlo4YhcelGXo="}]}, {"Route": "Content/image/logo.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11410"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"QLpvAHYFV23XX4qzhRt03Hl73CF5JzWTlo4YhcelGXo=\""}, {"Name": "Last-Modified", "Value": "Thu, 11 Jun 2015 12:52:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QLpvAHYFV23XX4qzhRt03Hl73CF5JzWTlo4YhcelGXo="}]}, {"Route": "Content/image/logo_icon.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo_icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4306"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52HAvvTLLvONX12i+8u7LXknSKw6bGGx+ukpA9bUZIM=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Jan 2022 05:10:11 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-52HAvvTLLvONX12i+8u7LXknSKw6bGGx+ukpA9bUZIM="}]}, {"Route": "Content/image/logo_icon.pw4d012y51.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo_icon.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4306"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"52HAvvTLLvONX12i+8u7LXknSKw6bGGx+ukpA9bUZIM=\""}, {"Name": "Last-Modified", "Value": "Wed, 26 Jan 2022 05:10:11 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pw4d012y51"}, {"Name": "label", "Value": "Content/image/logo_icon.png"}, {"Name": "integrity", "Value": "sha256-52HAvvTLLvONX12i+8u7LXknSKw6bGGx+ukpA9bUZIM="}]}, {"Route": "Content/image/Logo_red.jfiov0noex.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\Logo_red.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5563"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"sZ3hH8DzjmPhxmMHLZ0tUkp4V+iC98K/ArADvkV2QDM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Jan 2016 07:33:55 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jfiov0noex"}, {"Name": "label", "Value": "Content/image/Logo_red.png"}, {"Name": "integrity", "Value": "sha256-sZ3hH8DzjmPhxmMHLZ0tUkp4V+iC98K/ArADvkV2QDM="}]}, {"Route": "Content/image/Logo_red.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\Logo_red.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5563"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"sZ3hH8DzjmPhxmMHLZ0tUkp4V+iC98K/ArADvkV2QDM=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 19 Jan 2016 07:33:55 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sZ3hH8DzjmPhxmMHLZ0tUkp4V+iC98K/ArADvkV2QDM="}]}, {"Route": "Content/image/logo-conv.cr3isdb5hq.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo-conv.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18897"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eDyltiXOE/avROZ0SrlbeEuiZhcUMaHRsrGhLVAhpiA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 11 Aug 2015 11:32:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cr3isdb5hq"}, {"Name": "label", "Value": "Content/image/logo-conv.png"}, {"Name": "integrity", "Value": "sha256-eDyltiXOE/avROZ0SrlbeEuiZhcUMaHRsrGhLVAhpiA="}]}, {"Route": "Content/image/logo-conv.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo-conv.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "18897"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"eDyltiXOE/avROZ0SrlbeEuiZhcUMaHRsrGhLVAhpiA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 11 Aug 2015 11:32:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-eDyltiXOE/avROZ0SrlbeEuiZhcUMaHRsrGhLVAhpiA="}]}, {"Route": "Content/image/logo-islamic.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo-islamic.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3175"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Ig64Vc/zGcIw1NR/eKjAzT+2iMUK/IVULSZoBcy2Kis=\""}, {"Name": "Last-Modified", "Value": "Sun, 23 Jan 2022 14:22:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Ig64Vc/zGcIw1NR/eKjAzT+2iMUK/IVULSZoBcy2Kis="}]}, {"Route": "Content/image/logo-islamic.qe10b765u5.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\logo-islamic.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "3175"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"Ig64Vc/zGcIw1NR/eKjAzT+2iMUK/IVULSZoBcy2Kis=\""}, {"Name": "Last-Modified", "Value": "Sun, 23 Jan 2022 14:22:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qe10b765u5"}, {"Name": "label", "Value": "Content/image/logo-islamic.png"}, {"Name": "integrity", "Value": "sha256-Ig64Vc/zGcIw1NR/eKjAzT+2iMUK/IVULSZoBcy2Kis="}]}, {"Route": "Content/image/user.coz1cln6ig.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\user.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4333"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"JKUZOsuXyZyqRf8Wvovt7e/cRFuAkhRaL2vp2QV1N4Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Oct 2021 08:23:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "coz1cln6ig"}, {"Name": "label", "Value": "Content/image/user.png"}, {"Name": "integrity", "Value": "sha256-JKUZOsuXyZyqRf8Wvovt7e/cRFuAkhRaL2vp2QV1N4Q="}]}, {"Route": "Content/image/user.png", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\Content\\image\\user.png", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4333"}, {"Name": "Content-Type", "Value": "image/png"}, {"Name": "ETag", "Value": "\"JKUZOsuXyZyqRf8Wvovt7e/cRFuAkhRaL2vp2QV1N4Q=\""}, {"Name": "Last-Modified", "Value": "Tue, 05 Oct 2021 08:23:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JKUZOsuXyZyqRf8Wvovt7e/cRFuAkhRaL2vp2QV1N4Q="}]}, {"Route": "css/brand-colors.1oy9d1ws5g.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\brand-colors.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1118"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wej0CiXqDk0ZPlyR+auxgyWVXV5fsKVliJWr/O3nUOc=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 10:59:00 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1oy9d1ws5g"}, {"Name": "label", "Value": "css/brand-colors.css"}, {"Name": "integrity", "Value": "sha256-wej0CiXqDk0ZPlyR+auxgyWVXV5fsKVliJWr/O3nUOc="}]}, {"Route": "css/brand-colors.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\brand-colors.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1118"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"wej0CiXqDk0ZPlyR+auxgyWVXV5fsKVliJWr/O3nUOc=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 10:59:00 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wej0CiXqDk0ZPlyR+auxgyWVXV5fsKVliJWr/O3nUOc="}]}, {"Route": "css/custom.8m9ey7kn3p.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0PmXkEXwKULiVEZhhR9eW/hlyrZna2/ImQ1NAXFB1kw=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 18:34:03 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8m9ey7kn3p"}, {"Name": "label", "Value": "css/custom.css"}, {"Name": "integrity", "Value": "sha256-0PmXkEXwKULiVEZhhR9eW/hlyrZna2/ImQ1NAXFB1kw="}]}, {"Route": "css/custom.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\custom.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1046"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"0PmXkEXwKULiVEZhhR9eW/hlyrZna2/ImQ1NAXFB1kw=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 18:34:03 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0PmXkEXwKULiVEZhhR9eW/hlyrZna2/ImQ1NAXFB1kw="}]}, {"Route": "css/login.77io4isa0i.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\login.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11353"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3UGc3Hw4H12fKe/fTGd6Tn+4h+FaOTCkXEr4ergroFw=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 10:53:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "77io4isa0i"}, {"Name": "label", "Value": "css/login.css"}, {"Name": "integrity", "Value": "sha256-3UGc3Hw4H12fKe/fTGd6Tn+4h+FaOTCkXEr4ergroFw="}]}, {"Route": "css/login.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\login.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11353"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"3UGc3Hw4H12fKe/fTGd6Tn+4h+FaOTCkXEr4ergroFw=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 10:53:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3UGc3Hw4H12fKe/fTGd6Tn+4h+FaOTCkXEr4ergroFw="}]}, {"Route": "css/material-forms.47j1v6ljqt.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\material-forms.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6185"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cH6kiK1PczVOjrBEB2F3oEAZUilnaxuw3Gs1A+n+dgc=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 09:48:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47j1v6ljqt"}, {"Name": "label", "Value": "css/material-forms.css"}, {"Name": "integrity", "Value": "sha256-cH6kiK1PczVOjrBEB2F3oEAZUilnaxuw3Gs1A+n+dgc="}]}, {"Route": "css/material-forms.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\material-forms.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6185"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"cH6kiK1PczVOjrBEB2F3oEAZUilnaxuw3Gs1A+n+dgc=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 09:48:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-cH6kiK1PczVOjrBEB2F3oEAZUilnaxuw3Gs1A+n+dgc="}]}, {"Route": "css/site.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5254"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qxDol8xSbUXuRHH9OPDNx62IBCJqoQV5Aha/vWFVpPA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 10:59:47 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qxDol8xSbUXuRHH9OPDNx62IBCJqoQV5Aha/vWFVpPA="}]}, {"Route": "css/site.dmfl13e5hp.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\site.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5254"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"qxDol8xSbUXuRHH9OPDNx62IBCJqoQV5Aha/vWFVpPA=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 10:59:47 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dmfl13e5hp"}, {"Name": "label", "Value": "css/site.css"}, {"Name": "integrity", "Value": "sha256-qxDol8xSbUXuRHH9OPDNx62IBCJqoQV5Aha/vWFVpPA="}]}, {"Route": "css/user-profile.blv4wxc6j0.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\user-profile.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7931"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EQNMGyq9A5HKjPnl1I5HY5KhAppes42E8RlX1noujDs=\""}, {"Name": "Last-Modified", "Value": "Wed, 11 Jun 2025 18:31:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "blv4wxc6j0"}, {"Name": "label", "Value": "css/user-profile.css"}, {"Name": "integrity", "Value": "sha256-EQNMGyq9A5HKjPnl1I5HY5KhAppes42E8RlX1noujDs="}]}, {"Route": "css/user-profile.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\css\\user-profile.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7931"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"EQNMGyq9A5HKjPnl1I5HY5KhAppes42E8RlX1noujDs=\""}, {"Name": "Last-Modified", "Value": "Wed, 11 Jun 2025 18:31:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EQNMGyq9A5HKjPnl1I5HY5KhAppes42E8RlX1noujDs="}]}, {"Route": "favicon.61n19gt1b8.ico", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "61n19gt1b8"}, {"Name": "label", "Value": "favicon.ico"}, {"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "favicon.ico", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\favicon.ico", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5430"}, {"Name": "Content-Type", "Value": "image/x-icon"}, {"Name": "ETag", "Value": "\"Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Jtxf9L+5ITKRc1gIRl4VbUpGkRNfOBXjYTdhJD4facM="}]}, {"Route": "js/dashboard.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\js\\dashboard.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Nqnn8clbgv+5l0PgxcTOldg8mkMKrFn4TvPL+rYUUGg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 07:16:45 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nqnn8clbgv+5l0PgxcTOldg8mkMKrFn4TvPL+rYUUGg="}]}, {"Route": "js/dashboard.yu78tspv7y.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\js\\dashboard.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"Nqnn8clbgv+5l0PgxcTOldg8mkMKrFn4TvPL+rYUUGg=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 07:16:45 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yu78tspv7y"}, {"Name": "label", "Value": "js/dashboard.js"}, {"Name": "integrity", "Value": "sha256-Nqnn8clbgv+5l0PgxcTOldg8mkMKrFn4TvPL+rYUUGg="}]}, {"Route": "js/signalr-connection.fvm3yzzppj.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\js\\signalr-connection.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2090"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"i9SWhBUof2EkDlorK+CW1k4wgM1gBwlhRhWWHH0fLKk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 10:55:39 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvm3yzzppj"}, {"Name": "label", "Value": "js/signalr-connection.js"}, {"Name": "integrity", "Value": "sha256-i9SWhBUof2EkDlorK+CW1k4wgM1gBwlhRhWWHH0fLKk="}]}, {"Route": "js/signalr-connection.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\js\\signalr-connection.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2090"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"i9SWhBUof2EkDlorK+CW1k4wgM1gBwlhRhWWHH0fLKk=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 10:55:39 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-i9SWhBUof2EkDlorK+CW1k4wgM1gBwlhRhWWHH0fLKk="}]}, {"Route": "js/site.5c6j43bk15.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KzlEx3RWIWD5yoXxoQh01bgKfn3dGHpLTe3GNQQC8oc=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 04:42:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5c6j43bk15"}, {"Name": "label", "Value": "js/site.js"}, {"Name": "integrity", "Value": "sha256-KzlEx3RWIWD5yoXxoQh01bgKfn3dGHpLTe3GNQQC8oc="}]}, {"Route": "js/site.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\js\\site.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2930"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"KzlEx3RWIWD5yoXxoQh01bgKfn3dGHpLTe3GNQQC8oc=\""}, {"Name": "Last-Modified", "Value": "Mon, 16 Jun 2025 04:42:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KzlEx3RWIWD5yoXxoQh01bgKfn3dGHpLTe3GNQQC8oc="}]}, {"Route": "lib/bootstrap/css/bootstrap.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "161409"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T/zFmO5s/0aSwc6ics2KLxlfbewyRz6UNw1s3Ppf5gE=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:14 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-T/zFmO5s/0aSwc6ics2KLxlfbewyRz6UNw1s3Ppf5gE="}]}, {"Route": "lib/bootstrap/css/bootstrap.min.dxst0jyaol.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\css\\bootstrap.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "161409"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"T/zFmO5s/0aSwc6ics2KLxlfbewyRz6UNw1s3Ppf5gE=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:14 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxst0jyaol"}, {"Name": "label", "Value": "lib/bootstrap/css/bootstrap.min.css"}, {"Name": "integrity", "Value": "sha256-T/zFmO5s/0aSwc6ics2KLxlfbewyRz6UNw1s3Ppf5gE="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.37tfw0ft22.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "280259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "37tfw0ft22"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css"}, {"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "280259"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-j5E4XIj1p1kNnDi0x1teX9RXoh1/FNlPvCML9YmRh2Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.hrwsygsryq.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hrwsygsryq"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "679615"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3bYWUiiVYMZfv2wq5JnXIsHlQKgSKs/VcRivgjgZ1ho="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.ft3s53vfgj.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ft3s53vfgj"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "589087"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rTzXlnepcb/vgFAiB+U7ODQAfOlJLfM3gY6IU7eIANk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap.rtl.min.pk9g2wxc8p.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232911"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "pk9g2wxc8p"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-h5lE7Nm8SkeIpBHHYxN99spP3VuGFKl5NZgsocil7zk="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.bqjiyaj88i.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70329"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "bqjiyaj88i"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css"}, {"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70329"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Yy5/hBqRmmU2MJ1TKwP2aXoTO6+OjzrLmJIsC2Wy4H8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.c2jlpeoesf.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2j<PERSON><PERSON><PERSON><PERSON>"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.css.map"}, {"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203221"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-xAT+n25FE5hvOjj2fG4YdOwr1bl4IlAJBNg6PbhLT2E="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51795"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.aexeepp0ev.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aexeepp0ev"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map"}, {"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "115986"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kgL+xwVmM8IOs15lnoHt9daR2LRMiBG/cYgUPcKQOY4="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.min.erw9l3u2r3.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51795"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "erw9l3u2r3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.min.css"}, {"Name": "integrity", "Value": "sha256-5nDHMGiyfZHl3UXePuhLDQR9ncPfBR1HJeZLXyJNV24="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70403"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.ausgxo2sd3.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ausgxo2sd3"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "203225"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/siQUA8yX830j+cL4amKHY3yBtn3n8z3Eg+VZ15f90k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.d7shbmvgxk.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70403"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "d7shbmvgxk"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.css"}, {"Name": "integrity", "Value": "sha256-CZxoF8zjaLlyVkcvVCDlc8CeQR1w1RMrvgYx30cs8kM="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.cosvhxvwiu.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "cosvhxvwiu"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "116063"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-7GdOlw7U/wgyaeUtFmxPz5/MphdvVSPtVOOlTn9c33Q="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.k8d9w2qqmf.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-grid.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k8d9w2qqmf"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-grid.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-vMxTcvkC4Ly7LiAT3G8yEy9EpTr7Fge4SczWp07/p3k="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.fvhpjtyr6v.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fvhpjtyr6v"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css.map"}, {"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129371"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-RXJ/QZiBfHXoPtXR2EgC+bFo2pe3GtbZO722RtiLGzQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.b7pk76d08c.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "b7pk76d08c"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css"}, {"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10126"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-l8vt5oozv958eMd9TFsPAWgl9JJK9YKfbVSs8mchQ84="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.fsbi9cje9m.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fsbi9cje9m"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map"}, {"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.min.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51369"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-0eqVT62kqRLJh9oTqLeIH4UnQskqVjib8hl2fXxl4lg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12058"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.ee0r1s7dh0.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ee0r1s7dh0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "129386"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-OoQVwh7Arp7bVoK2ZiTx2S//KrnPrSPzPZ93CqCMhe8="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.jd9uben2k1.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jd9uben2k1"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "63943"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-910zw+rMdcg0Ls48ATp65vEn8rd5HvPxOKm2x3/CBII="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.dxx9fxp4il.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "dxx9fxp4il"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-/8jh8hcEMFKyS6goWqnNu7t3EzZPCGdQZgO6sCkI8tI="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.rzd6atqjts.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12058"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "rzd6atqjts"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.rtl.css"}, {"Name": "integrity", "Value": "sha256-V8psnHoJS/MPlCXWwc/J3tGtp9c3gGFRmqsIQgpn+Gg="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-reboot.ub07r2b239.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-reboot.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "12065"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ub07r2b239"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-reboot.css"}, {"Name": "integrity", "Value": "sha256-lo9YI82OF03vojdu+XOR3+DRrLIpMhpzZNmHbM5CDMA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.css.r4e9w2rdcm.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267535"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "r4e9w2rdcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css.map"}, {"Name": "integrity", "Value": "sha256-Nfjrc4Ur9Fv2oBEswQWIyBnNDP99q+LhL+z9553O0cY="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.khv3u5hwcm.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107823"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "khv3u5hwcm"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.css"}, {"Name": "integrity", "Value": "sha256-2BubgNUPlQSF/0wLFcRXQ/Yjzk9vsUbDAeK2QM+h+yo="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85352"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.c2oey78nd0.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "c2oey78nd0"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map"}, {"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180381"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-rHDmip4JZzuaGOcSQ1QSQrIbG0Eb3Zja9whqSF1zYIU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.min.lcd1t2u6c8.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85352"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lcd1t2u6c8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.min.css"}, {"Name": "integrity", "Value": "sha256-KyE9xbKO9CuYx0HXpIKgsWIvXkAfITtiQ172j26wmRs="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.j5mq2jizvt.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5mq2jizvt"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map"}, {"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "267476"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p0BVq5Ve/dohBIdfbrZsoQNu02JSsKh1g0wbyiQiUaU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.06098lyss8.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85281"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "06098lyss8"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "85281"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-GAUum6FjwQ8HrXGaoFRnHTqQQLpljXGavT7mBX8E9qU="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.nvvlpmu67g.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.min.css.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "180217"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "nvvlpmu67g"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.min.css.map"}, {"Name": "integrity", "Value": "sha256-o8XK32mcY/FfcOQ1D2HJvVuZ0YTXSURZDLXCK0fnQeA="}]}, {"Route": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.tdbxkamptv.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\css\\bootstrap-utilities.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "tdbxkamptv"}, {"Name": "label", "Value": "lib/bootstrap/dist/css/bootstrap-utilities.rtl.css"}, {"Name": "integrity", "Value": "sha256-H6wkBbSwjua2veJoThJo4uy161jp+DOiZTloUlcZ6qQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.6cfz1n2cew.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "207819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6cfz1n2cew"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js"}, {"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "207819"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mkoRoV24jV+rCPWcHDR5awPx8VuzzJKN0ibhxZ9/WaM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.6pdc2jztkx.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6pdc2jztkx"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.js.map"}, {"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.js.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "444579"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Wq4aWW1rQdJ+6oAgy1JQc9IBjHL9T3MKfXTBNqOv02c="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.493y06b0oq.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "493y06b0oq"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "80721"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-CDOy6cOibCWEdsRiZuaHf8dSGGJRYuBGC+mjoJimHGw="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.iovd86k7lj.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iovd86k7lj"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map"}, {"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.bundle.min.js.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.bundle.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "332090"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Xj4HYxZBQ7qqHKBwa2EAugRS+RHWzpcTtI49vgezUSU="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "135829"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.kbrnm935zg.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "kbrnm935zg"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js.map"}, {"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.js.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "305438"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-EPRLgpqWkahLxEn6CUjdM76RIYIw1xdHwTbeHssuj/4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.jj8uyg4cgr.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "73935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jj8uyg4cgr"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js"}, {"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "73935"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-QZdFT1ZNdly4rmgUBtXmXFS9BU1FTa+sPe6h794sFRQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.min.js.y7v9cxd14o.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "222455"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y7v9cxd14o"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.min.js.map"}, {"Name": "integrity", "Value": "sha256-Tsbv8z6VlNgVET8xvz/yLo/v5iJHTAj2J4hkhjP1rHM="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.esm.vr1egmr9el.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.esm.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "135829"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vr1egmr9el"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.esm.js"}, {"Name": "integrity", "Value": "sha256-exiXZNJDwucXfuje3CbXPbuS6+Ery3z9sP+pgmvh8nA="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145401"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.h1s4sie4z3.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "h1s4sie4z3"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js.map"}, {"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.js.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "306606"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9Wr7Hxe8gCJDoIHh5xP29ldXvC3kN2GkifQj9c8vYx4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.63fj8s7r0e.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "63fj8s7r0e"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js"}, {"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "60635"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3gQJhtmj7YnV1fmtbVcnAV6eI4ws0Tr48bVZCThtCGQ="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.0j3bgjxly4.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0j3bgjxly4"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.min.js.map"}, {"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.min.js.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.min.js.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "220561"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZI01e/ns473GKvACG4McggJdxvFfFIw4xspwQiG8Ye4="}]}, {"Route": "lib/bootstrap/dist/js/bootstrap.notf2xhcfb.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\dist\\js\\bootstrap.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "145401"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "notf2xhcfb"}, {"Name": "label", "Value": "lib/bootstrap/dist/js/bootstrap.js"}, {"Name": "integrity", "Value": "sha256-+UW802wgVfnjaSbdwyHLlU7AVplb0WToOlvN1CnzIac="}]}, {"Route": "lib/bootstrap/js/bootstrap.bundle.min.8u741w00kw.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84378"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sCElQ8xaSgoxwbWp0eiXMmGZIRa0z94+ffzzO06BqXs=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8u741w00kw"}, {"Name": "label", "Value": "lib/bootstrap/js/bootstrap.bundle.min.js"}, {"Name": "integrity", "Value": "sha256-sCElQ8xaSgoxwbWp0eiXMmGZIRa0z94+ffzzO06BqXs="}]}, {"Route": "lib/bootstrap/js/bootstrap.bundle.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\js\\bootstrap.bundle.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "84378"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"sCElQ8xaSgoxwbWp0eiXMmGZIRa0z94+ffzzO06BqXs=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-sCElQ8xaSgoxwbWp0eiXMmGZIRa0z94+ffzzO06BqXs="}]}, {"Route": "lib/bootstrap/LICENSE", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/bootstrap/LICENSE.81b7ukuj9c", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap\\LICENSE", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1153"}, {"Name": "Content-Type", "Value": "application/octet-stream"}, {"Name": "ETag", "Value": "\"ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "81b7ukuj9c"}, {"Name": "label", "Value": "lib/bootstrap/LICENSE"}, {"Name": "integrity", "Value": "sha256-ZH6pA6BSx6fuHZvdaKph1DwUJ+VSYilIiEQu8ilnvqk="}]}, {"Route": "lib/bootstrap-datepicker/css/bootstrap-datepicker.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap-datepicker\\css\\bootstrap-datepicker.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15727"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bj/NnRFZeLLz+xTKmDMuoXaAzgtz+D8xYKFKX/YpbgE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 06:29:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bj/NnRFZeLLz+xTKmDMuoXaAzgtz+D8xYKFKX/YpbgE="}]}, {"Route": "lib/bootstrap-datepicker/css/bootstrap-datepicker.min.ywzretzxeo.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap-datepicker\\css\\bootstrap-datepicker.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15727"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bj/NnRFZeLLz+xTKmDMuoXaAzgtz+D8xYKFKX/YpbgE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 06:29:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "yw<PERSON>retzxeo"}, {"Name": "label", "Value": "lib/bootstrap-datepicker/css/bootstrap-datepicker.min.css"}, {"Name": "integrity", "Value": "sha256-bj/NnRFZeLLz+xTKmDMuoXaAzgtz+D8xYKFKX/YpbgE="}]}, {"Route": "lib/bootstrap-datepicker/js/bootstrap-datepicker.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap-datepicker\\js\\bootstrap-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10830"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WF1Td1ZzGSctvURD28bCDv8CYBJsuqs8ZC+698TCRdE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 06:28:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-WF1Td1ZzGSctvURD28bCDv8CYBJsuqs8ZC+698TCRdE="}]}, {"Route": "lib/bootstrap-datepicker/js/bootstrap-datepicker.min.ooq100mrm4.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\bootstrap-datepicker\\js\\bootstrap-datepicker.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10830"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"WF1Td1ZzGSctvURD28bCDv8CYBJsuqs8ZC+698TCRdE=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 06:28:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ooq100mrm4"}, {"Name": "label", "Value": "lib/bootstrap-datepicker/js/bootstrap-datepicker.min.js"}, {"Name": "integrity", "Value": "sha256-WF1Td1ZzGSctvURD28bCDv8CYBJsuqs8ZC+698TCRdE="}]}, {"Route": "lib/datatables/bs4/css/dataTables.bootstrap4.min.44c5h2nspz.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\datatables\\bs4\\css\\dataTables.bootstrap4.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7496"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lDWLG10paq84N0F/7818mEj3YW5d6LCSBmIj2LirkYo=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "44c5h2nspz"}, {"Name": "label", "Value": "lib/datatables/bs4/css/dataTables.bootstrap4.min.css"}, {"Name": "integrity", "Value": "sha256-lDWLG10paq84N0F/7818mEj3YW5d6LCSBmIj2LirkYo="}]}, {"Route": "lib/datatables/bs4/css/dataTables.bootstrap4.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\datatables\\bs4\\css\\dataTables.bootstrap4.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "7496"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"lDWLG10paq84N0F/7818mEj3YW5d6LCSBmIj2LirkYo=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lDWLG10paq84N0F/7818mEj3YW5d6LCSBmIj2LirkYo="}]}, {"Route": "lib/datatables/bs4/js/dataTables.bootstrap4.min.gueg8vqske.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\datatables\\bs4\\js\\dataTables.bootstrap4.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2MzaecCGkwO775PvRJkqMTd4sR6cuRiQlkT2iUeCsSU=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "gueg8vqske"}, {"Name": "label", "Value": "lib/datatables/bs4/js/dataTables.bootstrap4.min.js"}, {"Name": "integrity", "Value": "sha256-2MzaecCGkwO775PvRJkqMTd4sR6cuRiQlkT2iUeCsSU="}]}, {"Route": "lib/datatables/bs4/js/dataTables.bootstrap4.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\datatables\\bs4\\js\\dataTables.bootstrap4.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4520"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"2MzaecCGkwO775PvRJkqMTd4sR6cuRiQlkT2iUeCsSU=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2MzaecCGkwO775PvRJkqMTd4sR6cuRiQlkT2iUeCsSU="}]}, {"Route": "lib/datatables/js/jquery.dataTables.min.aow88z2agc.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\datatables\\js\\jquery.dataTables.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "88048"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lpQbyCSrPqrv7IZbdk1u4zJ3Ft/DUAIfZElc0Zi25Kw=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "aow88z2agc"}, {"Name": "label", "Value": "lib/datatables/js/jquery.dataTables.min.js"}, {"Name": "integrity", "Value": "sha256-lpQbyCSrPqrv7IZbdk1u4zJ3Ft/DUAIfZElc0Zi25Kw="}]}, {"Route": "lib/datatables/js/jquery.dataTables.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\datatables\\js\\jquery.dataTables.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "88048"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lpQbyCSrPqrv7IZbdk1u4zJ3Ft/DUAIfZElc0Zi25Kw=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lpQbyCSrPqrv7IZbdk1u4zJ3Ft/DUAIfZElc0Zi25Kw="}]}, {"Route": "lib/font-awesome/css/all.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\css\\all.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "59305"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mUZM63G8m73Mcidfrv5E+Y61y7a12O5mW4ezU3bxqW4=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mUZM63G8m73Mcidfrv5E+Y61y7a12O5mW4ezU3bxqW4="}]}, {"Route": "lib/font-awesome/css/all.min.vm77v644vc.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\css\\all.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "59305"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"mUZM63G8m73Mcidfrv5E+Y61y7a12O5mW4ezU3bxqW4=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vm77v644vc"}, {"Name": "label", "Value": "lib/font-awesome/css/all.min.css"}, {"Name": "integrity", "Value": "sha256-mUZM63G8m73Mcidfrv5E+Y61y7a12O5mW4ezU3bxqW4="}]}, {"Route": "lib/font-awesome/webfonts/fa-brands-400.6fdyj7j1sx.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-brands-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76736"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"jqh5F1SRWomKMQDmPjKXim0XY75t+Oc6OdOpDWkc3u8=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:48 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "6fdyj7j1sx"}, {"Name": "label", "Value": "lib/font-awesome/webfonts/fa-brands-400.woff2"}, {"Name": "integrity", "Value": "sha256-jqh5F1SRWomKMQDmPjKXim0XY75t+Oc6OdOpDWkc3u8="}]}, {"Route": "lib/font-awesome/webfonts/fa-brands-400.j5wh9ughv7.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-brands-400.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "89988"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"+SF/ZodLDAHNjBC2opXbxPYJrLb1rcQcN9pGZBtX6wI=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j5wh9ughv7"}, {"Name": "label", "Value": "lib/font-awesome/webfonts/fa-brands-400.woff"}, {"Name": "integrity", "Value": "sha256-+SF/ZodLDAHNjBC2opXbxPYJrLb1rcQcN9pGZBtX6wI="}]}, {"Route": "lib/font-awesome/webfonts/fa-brands-400.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-brands-400.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "89988"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"+SF/ZodLDAHNjBC2opXbxPYJrLb1rcQcN9pGZBtX6wI=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-+SF/ZodLDAHNjBC2opXbxPYJrLb1rcQcN9pGZBtX6wI="}]}, {"Route": "lib/font-awesome/webfonts/fa-brands-400.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-brands-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "76736"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"jqh5F1SRWomKMQDmPjKXim0XY75t+Oc6OdOpDWkc3u8=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:48 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jqh5F1SRWomKMQDmPjKXim0XY75t+Oc6OdOpDWkc3u8="}]}, {"Route": "lib/font-awesome/webfonts/fa-regular-400.40m2n68m7w.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-regular-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13224"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"5CqIRERIrD1gVJzHwf8sipyschA0wHPYChSkTnlzDMo=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:35 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "40m2n68m7w"}, {"Name": "label", "Value": "lib/font-awesome/webfonts/fa-regular-400.woff2"}, {"Name": "integrity", "Value": "sha256-5CqIRERIrD1gVJzHwf8sipyschA0wHPYChSkTnlzDMo="}]}, {"Route": "lib/font-awesome/webfonts/fa-regular-400.hp61lh7kro.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-regular-400.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16276"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"y56eaTGSQTzeKx8hwdwdRLb+eyfMK0WOizWdGPn/j04=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:41 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "hp61lh7kro"}, {"Name": "label", "Value": "lib/font-awesome/webfonts/fa-regular-400.woff"}, {"Name": "integrity", "Value": "sha256-y56eaTGSQTzeKx8hwdwdRLb+eyfMK0WOizWdGPn/j04="}]}, {"Route": "lib/font-awesome/webfonts/fa-regular-400.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-regular-400.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16276"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"y56eaTGSQTzeKx8hwdwdRLb+eyfMK0WOizWdGPn/j04=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:41 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-y56eaTGSQTzeKx8hwdwdRLb+eyfMK0WOizWdGPn/j04="}]}, {"Route": "lib/font-awesome/webfonts/fa-regular-400.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-regular-400.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "13224"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"5CqIRERIrD1gVJzHwf8sipyschA0wHPYChSkTnlzDMo=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:35 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5CqIRERIrD1gVJzHwf8sipyschA0wHPYChSkTnlzDMo="}]}, {"Route": "lib/font-awesome/webfonts/fa-solid-900.jba8vm33uh.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-solid-900.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "101648"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"P200iM9lN09vZ2wxU0CwrCvoMr1VJAyAlEjjbvm5YyY=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jba8vm33uh"}, {"Name": "label", "Value": "lib/font-awesome/webfonts/fa-solid-900.woff"}, {"Name": "integrity", "Value": "sha256-P200iM9lN09vZ2wxU0CwrCvoMr1VJAyAlEjjbvm5YyY="}]}, {"Route": "lib/font-awesome/webfonts/fa-solid-900.k51ry5602j.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-solid-900.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78268"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"mDS4KtJuKjdYPSJnahLdLrD+fIA1aiEU0NsaqLOJlTc=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "k51ry5602j"}, {"Name": "label", "Value": "lib/font-awesome/webfonts/fa-solid-900.woff2"}, {"Name": "integrity", "Value": "sha256-mDS4KtJuKjdYPSJnahLdLrD+fIA1aiEU0NsaqLOJlTc="}]}, {"Route": "lib/font-awesome/webfonts/fa-solid-900.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-solid-900.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "101648"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"P200iM9lN09vZ2wxU0CwrCvoMr1VJAyAlEjjbvm5YyY=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-P200iM9lN09vZ2wxU0CwrCvoMr1VJAyAlEjjbvm5YyY="}]}, {"Route": "lib/font-awesome/webfonts/fa-solid-900.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\font-awesome\\webfonts\\fa-solid-900.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "78268"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"mDS4KtJuKjdYPSJnahLdLrD+fIA1aiEU0NsaqLOJlTc=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-mDS4KtJuKjdYPSJnahLdLrD+fIA1aiEU0NsaqLOJlTc="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1140"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UUtYe9HXy0ub5OcGvMgAoki4hrsFDhx9ZOvBhohTgVw=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:00:28 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UUtYe9HXy0ub5OcGvMgAoki4hrsFDhx9ZOvBhohTgVw="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro.jnm0qub8ui.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1140"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"UUtYe9HXy0ub5OcGvMgAoki4hrsFDhx9ZOvBhohTgVw=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:00:28 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "jnm0qub8ui"}, {"Name": "label", "Value": "lib/fonts/source-sans-pro/source-sans-pro.css"}, {"Name": "integrity", "Value": "sha256-UUtYe9HXy0ub5OcGvMgAoki4hrsFDhx9ZOvBhohTgVw="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-300.0chuq1odcr.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-300.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20096"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"wI76kXgYZdGi6fywMPisVcLY6tv4giwuolFVYzP5nZw=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:17 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0chuq1odcr"}, {"Name": "label", "Value": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-300.woff"}, {"Name": "integrity", "Value": "sha256-wI76kXgYZdGi6fywMPisVcLY6tv4giwuolFVYzP5nZw="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-300.7agu212shz.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-300.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15948"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"77PNxeRYL9Z9/6tvxuUGIHTOP4xRdHNGr5ROl3Sdwwk=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:15 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "7agu212shz"}, {"Name": "label", "Value": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-300.woff2"}, {"Name": "integrity", "Value": "sha256-77PNxeRYL9Z9/6tvxuUGIHTOP4xRdHNGr5ROl3Sdwwk="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-300.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-300.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20096"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"wI76kXgYZdGi6fywMPisVcLY6tv4giwuolFVYzP5nZw=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:17 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wI76kXgYZdGi6fywMPisVcLY6tv4giwuolFVYzP5nZw="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-300.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-300.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15948"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"77PNxeRYL9Z9/6tvxuUGIHTOP4xRdHNGr5ROl3Sdwwk=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:15 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-77PNxeRYL9Z9/6tvxuUGIHTOP4xRdHNGr5ROl3Sdwwk="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-700.4l6isozb0a.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-700.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19896"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"2N0N5jgpPrYtuhWm5BD7Cvmls2w13yJiN7G2CdVzxj4=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:12 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4l6isozb0a"}, {"Name": "label", "Value": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-700.woff"}, {"Name": "integrity", "Value": "sha256-2N0N5jgpPrYtuhWm5BD7Cvmls2w13yJiN7G2CdVzxj4="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-700.on17fdnzzh.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-700.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15764"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"JPfjl/rseeYsN/8vALFw9twVV/tGrBafnxiXqdZB3QM=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:08 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "on17fdnzzh"}, {"Name": "label", "Value": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-700.woff2"}, {"Name": "integrity", "Value": "sha256-JPfjl/rseeYsN/8vALFw9twVV/tGrBafnxiXqdZB3QM="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-700.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-700.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19896"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"2N0N5jgpPrYtuhWm5BD7Cvmls2w13yJiN7G2CdVzxj4=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:12 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-2N0N5jgpPrYtuhWm5BD7Cvmls2w13yJiN7G2CdVzxj4="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-700.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-700.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15764"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"JPfjl/rseeYsN/8vALFw9twVV/tGrBafnxiXqdZB3QM=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:08 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-JPfjl/rseeYsN/8vALFw9twVV/tGrBafnxiXqdZB3QM="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-italic.j82urxxfh7.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-italic.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15316"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"01WcgWr2QOg4KynQLU+9jHIl/PAwLPJE2LLXz12y/dE=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:19 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "j82urxxfh7"}, {"Name": "label", "Value": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-italic.woff2"}, {"Name": "integrity", "Value": "sha256-01WcgWr2QOg4KynQLU+9jHIl/PAwLPJE2LLXz12y/dE="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-italic.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-italic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19408"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"NF/QvWIlxTxNKKolZ5jW2KoNI+3ifkKTO2JZn95wLnw=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:25 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-NF/QvWIlxTxNKKolZ5jW2KoNI+3ifkKTO2JZn95wLnw="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-italic.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-italic.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "15316"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"01WcgWr2QOg4KynQLU+9jHIl/PAwLPJE2LLXz12y/dE=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:19 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-01WcgWr2QOg4KynQLU+9jHIl/PAwLPJE2LLXz12y/dE="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-italic.wxz1f1ij2p.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-italic.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19408"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"NF/QvWIlxTxNKKolZ5jW2KoNI+3ifkKTO2JZn95wLnw=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:25 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wxz1f1ij2p"}, {"Name": "label", "Value": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-italic.woff"}, {"Name": "integrity", "Value": "sha256-NF/QvWIlxTxNKKolZ5jW2KoNI+3ifkKTO2JZn95wLnw="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-regular.klzwlqew5m.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-regular.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20180"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"ODme/nB6j/wSNZoAhuc0AxW0IZShD9Lh0SiL4S2p45w=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:10 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "klzwlqew5m"}, {"Name": "label", "Value": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-regular.woff"}, {"Name": "integrity", "Value": "sha256-ODme/nB6j/wSNZoAhuc0AxW0IZShD9Lh0SiL4S2p45w="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-regular.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-regular.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "20180"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"ODme/nB6j/wSNZoAhuc0AxW0IZShD9Lh0SiL4S2p45w=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:10 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ODme/nB6j/wSNZoAhuc0AxW0IZShD9Lh0SiL4S2p45w="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-regular.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-regular.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16112"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"qZUPpcqc9HBydwkA0lm89neKoRGWUtLnBtXrkt8lQZk=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:06 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-qZUPpcqc9HBydwkA0lm89neKoRGWUtLnBtXrkt8lQZk="}]}, {"Route": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-regular.xe5pesne3m.woff2", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\fonts\\source-sans-pro\\source-sans-pro-v14-latin-regular.woff2", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "16112"}, {"Name": "Content-Type", "Value": "font/woff2"}, {"Name": "ETag", "Value": "\"qZUPpcqc9HBydwkA0lm89neKoRGWUtLnBtXrkt8lQZk=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:49:06 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xe5pesne3m"}, {"Name": "label", "Value": "lib/fonts/source-sans-pro/source-sans-pro-v14-latin-regular.woff2"}, {"Name": "integrity", "Value": "sha256-qZUPpcqc9HBydwkA0lm89neKoRGWUtLnBtXrkt8lQZk="}]}, {"Route": "lib/ionicons/css/ionicons.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\css\\ionicons.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51284"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kqxQgiD1u2DslOB2UFKOtmYl+CpHQK2gaM3gU2V4EoY=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kqxQgiD1u2DslOB2UFKOtmYl+CpHQK2gaM3gU2V4EoY="}]}, {"Route": "lib/ionicons/css/ionicons.min.iz7wz4ptva.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\css\\ionicons.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "51284"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"kqxQgiD1u2DslOB2UFKOtmYl+CpHQK2gaM3gU2V4EoY=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "iz7wz4ptva"}, {"Name": "label", "Value": "lib/ionicons/css/ionicons.min.css"}, {"Name": "integrity", "Value": "sha256-kqxQgiD1u2DslOB2UFKOtmYl+CpHQK2gaM3gU2V4EoY="}]}, {"Route": "lib/ionicons/fonts/ionicons.eot", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "120724"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"fjMNxTOru4beuavPT1OkJjkV8oh/oOwCbF3jbH2xo20=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:50:40 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-fjMNxTOru4beuavPT1OkJjkV8oh/oOwCbF3jbH2xo20="}]}, {"Route": "lib/ionicons/fonts/ionicons.f726s6newv.svg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "333834"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"VUvwsesnAE/NKYGhjIfWgbcQfNpR2Srq+WXQrOKxLX8=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:50:53 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f726s6newv"}, {"Name": "label", "Value": "lib/ionicons/fonts/ionicons.svg"}, {"Name": "integrity", "Value": "sha256-VUvwsesnAE/NKYGhjIfWgbcQfNpR2Srq+WXQrOKxLX8="}]}, {"Route": "lib/ionicons/fonts/ionicons.qdbrmunew0.ttf", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "188508"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"XnAINewFKTo9D541Tn0DgxnTRSHNJ554IZjf9tHdWPI=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:50:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "qdbrmunew0"}, {"Name": "label", "Value": "lib/ionicons/fonts/ionicons.ttf"}, {"Name": "integrity", "Value": "sha256-XnAINewFKTo9D541Tn0DgxnTRSHNJ554IZjf9tHdWPI="}]}, {"Route": "lib/ionicons/fonts/ionicons.svg", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.svg", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "333834"}, {"Name": "Content-Type", "Value": "image/svg+xml"}, {"Name": "ETag", "Value": "\"VUvwsesnAE/NKYGhjIfWgbcQfNpR2Srq+WXQrOKxLX8=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:50:53 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-VUvwsesnAE/NKYGhjIfWgbcQfNpR2Srq+WXQrOKxLX8="}]}, {"Route": "lib/ionicons/fonts/ionicons.ttf", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.ttf", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "188508"}, {"Name": "Content-Type", "Value": "application/x-font-ttf"}, {"Name": "ETag", "Value": "\"XnAINewFKTo9D541Tn0DgxnTRSHNJ554IZjf9tHdWPI=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:50:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XnAINewFKTo9D541Tn0DgxnTRSHNJ554IZjf9tHdWPI="}]}, {"Route": "lib/ionicons/fonts/ionicons.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "67904"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"p144ECbs7UT06NbqTcQOKOamTdlT6MC2wjnRrIRMSi0=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:50:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-p144ECbs7UT06NbqTcQOKOamTdlT6MC2wjnRrIRMSi0="}]}, {"Route": "lib/ionicons/fonts/ionicons.xatgdrjce6.woff", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.woff", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "67904"}, {"Name": "Content-Type", "Value": "application/font-woff"}, {"Name": "ETag", "Value": "\"p144ECbs7UT06NbqTcQOKOamTdlT6MC2wjnRrIRMSi0=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:50:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xatgdrjce6"}, {"Name": "label", "Value": "lib/ionicons/fonts/ionicons.woff"}, {"Name": "integrity", "Value": "sha256-p144ECbs7UT06NbqTcQOKOamTdlT6MC2wjnRrIRMSi0="}]}, {"Route": "lib/ionicons/fonts/ionicons.ygayrii7su.eot", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\ionicons\\fonts\\ionicons.eot", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "120724"}, {"Name": "Content-Type", "Value": "application/vnd.ms-fontobject"}, {"Name": "ETag", "Value": "\"fjMNxTOru4beuavPT1OkJjkV8oh/oOwCbF3jbH2xo20=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 04:50:40 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ygayrii7su"}, {"Name": "label", "Value": "lib/ionicons/fonts/ionicons.eot"}, {"Name": "integrity", "Value": "sha256-fjMNxTOru4beuavPT1OkJjkV8oh/oOwCbF3jbH2xo20="}]}, {"Route": "lib/jquery/dist/jquery.0i3buxo5is.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "285314"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0i3buxo5is"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.js"}, {"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "285314"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"eKhayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-e<PERSON>hayi8LEQwp4NKxN+CfCh+3qOVUtJn3QNZ0TciWLP4="}]}, {"Route": "lib/jquery/dist/jquery.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "134755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.min.o1o13a6vjx.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.js"}, {"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/dist/jquery.min.ttgo8qnofa.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "134755"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ttgo8qnofa"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.min.map"}, {"Name": "integrity", "Value": "sha256-z3TVHGLSmRiZiRMOu0I7MEU1Mv3ImI2OK3GxuRZagLg="}]}, {"Route": "lib/jquery/dist/jquery.slim.2z0ns9nrw6.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232015"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "2z0ns9nrw6"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.js"}, {"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.slim.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "232015"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-UgvvN8vBkgO0luPSUl2s8TIlOSYRoGFAX4jlCIm9Adc="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.87fc7y1x7t.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107143"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "87fc7y1x7t"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.map"}, {"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.map", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.map", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "107143"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-9FYmcgtx8qZo1OPPiPt/BJ/sz0EI8SRNUYsFLZDEEt4="}]}, {"Route": "lib/jquery/dist/jquery.slim.min.muycvpuwrr.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\dist\\jquery.slim.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "70264"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "muycvpuwrr"}, {"Name": "label", "Value": "lib/jquery/dist/jquery.slim.min.js"}, {"Name": "integrity", "Value": "sha256-kmHvs0B+OpCW5GVHUNjv9rOmY0IvSIRcf7zGUDTDQM8="}]}, {"Route": "lib/jquery/jquery-3.7.1.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\jquery-3.7.1.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 18:23:04 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/jquery-3.7.1.min.o1o13a6vjx.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\jquery-3.7.1.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "87533"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 18:23:04 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "o1o13a6vjx"}, {"Name": "label", "Value": "lib/jquery/jquery-3.7.1.min.js"}, {"Name": "integrity", "Value": "sha256-/JqT3SQfawRcv/BIHPThkBvs0OEvtFFmqPF/lYI/Cxo="}]}, {"Route": "lib/jquery/LICENSE.mlv21k5csn.txt", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mlv21k5csn"}, {"Name": "label", "Value": "lib/jquery/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery/LICENSE.txt", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-hjIBkvmgxQXbNXK3B9YQ3t06RwLuQSQzC/dpvuB/lMk="}]}, {"Route": "lib/jquery-ui/jquery-ui.min.5ovtr2a0tm.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-ui\\jquery-ui.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "255084"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:31 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "5ovtr2a0tm"}, {"Name": "label", "Value": "lib/jquery-ui/jquery-ui.min.js"}, {"Name": "integrity", "Value": "sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0="}]}, {"Route": "lib/jquery-ui/jquery-ui.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-ui\\jquery-ui.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "255084"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:31 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-lSjKY0/srUM9BE3dPm+c4fBo1dky2v27Gdjm2uoZaL0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.83jwlth58m.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "83jwlth58m"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.js"}, {"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "53033"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XL6yOf4sfG2g15W8aB744T4ClbiDG4IMGl2mi0tbzu0="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/additional-methods.min.mrlpezrjn3.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\additional-methods.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "22125"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mrlpezrjn3"}, {"Name": "label", "Value": "lib/jquery-validation/dist/additional-methods.min.js"}, {"Name": "integrity", "Value": "sha256-jhvKRxZo6eW/PyCe+4rjBLzqesJlE8rnyQGEjk8l2k8="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52536"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.lzl9nlhx6b.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "52536"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lzl9nlhx6b"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.js"}, {"Name": "integrity", "Value": "sha256-kRL82372ur5YrVTjFWp9muao9yeU8AoLiJxSb5ekmHg="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.ag7o75518u.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ag7o75518u"}, {"Name": "label", "Value": "lib/jquery-validation/dist/jquery.validate.min.js"}, {"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/dist/jquery.validate.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\dist\\jquery.validate.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "25308"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-umbTaFxP31Fv6O1itpLS/3+v5fOAWDLOUzlmvOGaKV4="}]}, {"Route": "lib/jquery-validation/LICENSE.md", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation/LICENSE.x0q3zqp4vz.md", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation\\LICENSE.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1117"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "x0q3zqp4vz"}, {"Name": "label", "Value": "lib/jquery-validation/LICENSE.md"}, {"Name": "integrity", "Value": "sha256-geHEkw/WGPdaHQMRq5HuNY9snliNzU/y2OW8ycnhGXw="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.47otxtyo56.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "47otxtyo56"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js"}, {"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "19385"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-wJQaJ0XynBE2fq6CexXXhxKu7fstVmQc7V2MHNTo+WQ="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.4v8eqarkd7.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4v8eqarkd7"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js"}, {"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/dist/jquery.validate.unobtrusive.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation-unobtrusive\\dist\\jquery.validate.unobtrusive.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5824"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-YJa7W8EiQdQpkk93iGEjjnLSUWRpRJbSfzfURh1kxz4="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.356vix0kms.txt", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "356vix0kms"}, {"Name": "label", "Value": "lib/jquery-validation-unobtrusive/LICENSE.txt"}, {"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jquery-validation-unobtrusive/LICENSE.txt", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jquery-validation-unobtrusive\\LICENSE.txt", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1139"}, {"Name": "Content-Type", "Value": "text/plain"}, {"Name": "ETag", "Value": "\"16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s=\""}, {"Name": "Last-Modified", "Value": "Mon, 19 May 2025 08:03:46 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-16aFlqtpsG9RyieKZUUUjkJpqTgcJtWXwT312I4Iz1s="}]}, {"Route": "lib/jstree/jstree.min.0zstsvc49u.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jstree\\jstree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9512"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FJAUH4nh8wC0HFIQs2PRp+uGz5rjkuOU0IRDa/qUMh8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 06:23:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "0zstsvc49u"}, {"Name": "label", "Value": "lib/jstree/jstree.min.js"}, {"Name": "integrity", "Value": "sha256-FJAUH4nh8wC0HFIQs2PRp+uGz5rjkuOU0IRDa/qUMh8="}]}, {"Route": "lib/jstree/jstree.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jstree\\jstree.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9512"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FJAUH4nh8wC0HFIQs2PRp+uGz5rjkuOU0IRDa/qUMh8=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 06:23:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FJAUH4nh8wC0HFIQs2PRp+uGz5rjkuOU0IRDa/qUMh8="}]}, {"Route": "lib/jstree/themes/default/style.min.btr2h80lz8.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jstree\\themes\\default\\style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TwoJXEiJfHQg0aDknoywqfQVLWhUPG6Xt2cyaVtS7lk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 06:26:02 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "btr2h80lz8"}, {"Name": "label", "Value": "lib/jstree/themes/default/style.min.css"}, {"Name": "integrity", "Value": "sha256-TwoJXEiJfHQg0aDknoywqfQVLWhUPG6Xt2cyaVtS7lk="}]}, {"Route": "lib/jstree/themes/default/style.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\jstree\\themes\\default\\style.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "11870"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"TwoJXEiJfHQg0aDknoywqfQVLWhUPG6Xt2cyaVtS7lk=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 06:26:02 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-TwoJXEiJfHQg0aDknoywqfQVLWhUPG6Xt2cyaVtS7lk="}]}, {"Route": "lib/microsoft/signalr/dist/browser/signalr.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\microsoft\\signalr\\dist\\browser\\signalr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47647"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4opyCjWbN8sBV1jVQ/kIcw7Vu+R42wlQa7aIfxgxNTg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 10:53:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-4opyCjWbN8sBV1jVQ/kIcw7Vu+R42wlQa7aIfxgxNTg="}]}, {"Route": "lib/microsoft/signalr/dist/browser/signalr.min.m3i9f6419i.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\microsoft\\signalr\\dist\\browser\\signalr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "47647"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"4opyCjWbN8sBV1jVQ/kIcw7Vu+R42wlQa7aIfxgxNTg=\""}, {"Name": "Last-Modified", "Value": "Thu, 29 May 2025 10:53:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "m3i9f6419i"}, {"Name": "label", "Value": "lib/microsoft/signalr/dist/browser/signalr.min.js"}, {"Name": "integrity", "Value": "sha256-4opyCjWbN8sBV1jVQ/kIcw7Vu+R42wlQa7aIfxgxNTg="}]}, {"Route": "lib/modern-toast/toast.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\modern-toast\\toast.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IRch838g6Dbq3+oGARMd1NH82wyEYX4C7X4YvJuUFUA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 18:17:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IRch838g6Dbq3+oGARMd1NH82wyEYX4C7X4YvJuUFUA="}]}, {"Route": "lib/modern-toast/toast.vwcq5cpvuf.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\modern-toast\\toast.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "10198"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"IRch838g6Dbq3+oGARMd1NH82wyEYX4C7X4YvJuUFUA=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 18:17:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "vwcq5cpvuf"}, {"Name": "label", "Value": "lib/modern-toast/toast.js"}, {"Name": "integrity", "Value": "sha256-IRch838g6Dbq3+oGARMd1NH82wyEYX4C7X4YvJuUFUA="}]}, {"Route": "lib/moment/moment.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\moment\\moment.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58862"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c95CVJWVMOTR2b7FhjeRhPlrSVPaz5zV5eK917/s7vc=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-c95CVJWVMOTR2b7FhjeRhPlrSVPaz5zV5eK917/s7vc="}]}, {"Route": "lib/moment/moment.min.xnrazl4vv0.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\moment\\moment.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "58862"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"c95CVJWVMOTR2b7FhjeRhPlrSVPaz5zV5eK917/s7vc=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "xnrazl4vv0"}, {"Name": "label", "Value": "lib/moment/moment.min.js"}, {"Name": "integrity", "Value": "sha256-c95CVJWVMOTR2b7FhjeRhPlrSVPaz5zV5eK917/s7vc="}]}, {"Route": "lib/README.lrtqx9pgo3.md", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2403"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"z9GuL/khvJXCh93DEjyv3rl4hvfH2ukClQKVH/2BrRc=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 10:21:16 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "lrtqx9pgo3"}, {"Name": "label", "Value": "lib/README.md"}, {"Name": "integrity", "Value": "sha256-z9GuL/khvJXCh93DEjyv3rl4hvfH2ukClQKVH/2BrRc="}]}, {"Route": "lib/README.md", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\README.md", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "2403"}, {"Name": "Content-Type", "Value": "text/markdown"}, {"Name": "ETag", "Value": "\"z9GuL/khvJXCh93DEjyv3rl4hvfH2ukClQKVH/2BrRc=\""}, {"Name": "Last-Modified", "Value": "Thu, 22 May 2025 10:21:16 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-z9GuL/khvJXCh93DEjyv3rl4hvfH2ukClQKVH/2BrRc="}]}, {"Route": "lib/select2/css/select2.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14153"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5Rc+BOfd3BmHW7CAHZHc3Yqi83NNVIzDVwu15+HyEyE=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 18:41:07 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-5Rc+BOfd3BmHW7CAHZHc3Yqi83NNVIzDVwu15+HyEyE="}]}, {"Route": "lib/select2/css/select2.min.fzeltrmjmf.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "14153"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"5Rc+BOfd3BmHW7CAHZHc3Yqi83NNVIzDVwu15+HyEyE=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 18:41:07 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "fzeltrmjmf"}, {"Name": "label", "Value": "lib/select2/css/select2.min.css"}, {"Name": "integrity", "Value": "sha256-5Rc+BOfd3BmHW7CAHZHc3Yqi83NNVIzDVwu15+HyEyE="}]}, {"Route": "lib/select2/css/select2-bootstrap4.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap4.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ChnouFUvWwco4SA0QlMptNRDv5qCcUdEgcwVG+RiqK8=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 18:41:36 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ChnouFUvWwco4SA0QlMptNRDv5qCcUdEgcwVG+RiqK8="}]}, {"Route": "lib/select2/css/select2-bootstrap4.min.y26l3lv3fk.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap4.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "4277"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ChnouFUvWwco4SA0QlMptNRDv5qCcUdEgcwVG+RiqK8=\""}, {"Name": "Last-Modified", "Value": "Fri, 23 May 2025 18:41:36 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "y26l3lv3fk"}, {"Name": "label", "Value": "lib/select2/css/select2-bootstrap4.min.css"}, {"Name": "integrity", "Value": "sha256-ChnouFUvWwco4SA0QlMptNRDv5qCcUdEgcwVG+RiqK8="}]}, {"Route": "lib/select2/css/select2-bootstrap-5-theme.9t6rxls2rs.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33700"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PdA95MFDvPdYlnfpIczR/wkxW/Us9YI/lQR3OkU80OY=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 May 2022 19:11:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9t6rxls2rs"}, {"Name": "label", "Value": "lib/select2/css/select2-bootstrap-5-theme.css"}, {"Name": "integrity", "Value": "sha256-PdA95MFDvPdYlnfpIczR/wkxW/Us9YI/lQR3OkU80OY="}]}, {"Route": "lib/select2/css/select2-bootstrap-5-theme.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33700"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"PdA95MFDvPdYlnfpIczR/wkxW/Us9YI/lQR3OkU80OY=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 May 2022 19:11:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-PdA95MFDvPdYlnfpIczR/wkxW/Us9YI/lQR3OkU80OY="}]}, {"Route": "lib/select2/css/select2-bootstrap-5-theme.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31223"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XLNUEfzPGHBeStES2DbLUURZ3e793BablwzJlYj6W2Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 May 2022 19:11:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XLNUEfzPGHBeStES2DbLUURZ3e793BablwzJlYj6W2Q="}]}, {"Route": "lib/select2/css/select2-bootstrap-5-theme.min.wfe5v7adcw.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31223"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XLNUEfzPGHBeStES2DbLUURZ3e793BablwzJlYj6W2Q=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 May 2022 19:11:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "wfe5v7adcw"}, {"Name": "label", "Value": "lib/select2/css/select2-bootstrap-5-theme.min.css"}, {"Name": "integrity", "Value": "sha256-XLNUEfzPGHBeStES2DbLUURZ3e793BablwzJlYj6W2Q="}]}, {"Route": "lib/select2/css/select2-bootstrap-5-theme.rtl.4a7ercfsxc.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XLffXXWZpoYoOoO7GfXAoBD7n/KAY+IzKgxoJkQ+7MM=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 May 2022 19:11:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "4a7ercfsxc"}, {"Name": "label", "Value": "lib/select2/css/select2-bootstrap-5-theme.rtl.css"}, {"Name": "integrity", "Value": "sha256-XLffXXWZpoYoOoO7GfXAoBD7n/KAY+IzKgxoJkQ+7MM="}]}, {"Route": "lib/select2/css/select2-bootstrap-5-theme.rtl.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.rtl.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "33691"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"XLffXXWZpoYoOoO7GfXAoBD7n/KAY+IzKgxoJkQ+7MM=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 May 2022 19:11:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-XLffXXWZpoYoOoO7GfXAoBD7n/KAY+IzKgxoJkQ+7MM="}]}, {"Route": "lib/select2/css/select2-bootstrap-5-theme.rtl.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31216"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bXXMs+0o746W3FX9DzwopnPEHG3bX9Ar53Wo7rv908c=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 May 2022 19:11:18 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-bXXMs+0o746W3FX9DzwopnPEHG3bX9Ar53Wo7rv908c="}]}, {"Route": "lib/select2/css/select2-bootstrap-5-theme.rtl.min.f9k4fvkj2a.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\css\\select2-bootstrap-5-theme.rtl.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "31216"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"bXXMs+0o746W3FX9DzwopnPEHG3bX9Ar53Wo7rv908c=\""}, {"Name": "Last-Modified", "Value": "Sat, 07 May 2022 19:11:18 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "f9k4fvkj2a"}, {"Name": "label", "Value": "lib/select2/css/select2-bootstrap-5-theme.rtl.min.css"}, {"Name": "integrity", "Value": "sha256-bXXMs+0o746W3FX9DzwopnPEHG3bX9Ar53Wo7rv908c="}]}, {"Route": "lib/select2/js/select2.min.9nt46wh0zh.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\js\\select2.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74922"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FcVIknBiVRk5KLQeIBb9VQdtFRMqwffXyZ+D8q0gQro=\""}, {"Name": "Last-Modified", "Value": "Thu, 28 Jul 2022 05:40:49 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "9nt46wh0zh"}, {"Name": "label", "Value": "lib/select2/js/select2.min.js"}, {"Name": "integrity", "Value": "sha256-FcVIknBiVRk5KLQeIBb9VQdtFRMqwffXyZ+D8q0gQro="}]}, {"Route": "lib/select2/js/select2.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\select2\\js\\select2.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "74922"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"FcVIknBiVRk5KLQeIBb9VQdtFRMqwffXyZ+D8q0gQro=\""}, {"Name": "Last-Modified", "Value": "Thu, 28 Jul 2022 05:40:49 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-FcVIknBiVRk5KLQeIBb9VQdtFRMqwffXyZ+D8q0gQro="}]}, {"Route": "lib/toastr/css/toastr.min.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\toastr\\css\\toastr.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ENFZrbVzylNbgnXx0n3I1g//2WeO47XxoPe0vkp3NC8=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:34 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-ENFZrbVzylNbgnXx0n3I1g//2WeO47XxoPe0vkp3NC8="}]}, {"Route": "lib/toastr/css/toastr.min.s50x20xwuu.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\toastr\\css\\toastr.min.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "6741"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"ENFZrbVzylNbgnXx0n3I1g//2WeO47XxoPe0vkp3NC8=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:34 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "s50x20xwuu"}, {"Name": "label", "Value": "lib/toastr/css/toastr.min.css"}, {"Name": "integrity", "Value": "sha256-ENFZrbVzylNbgnXx0n3I1g//2WeO47XxoPe0vkp3NC8="}]}, {"Route": "lib/toastr/js/toastr.min.30edegnhg3.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\toastr\\js\\toastr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5537"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3blsJd4Hli/7wCQ+bmgXfOdK7p/ZUMtPXY08jmxSSgk=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:33 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "30edegnhg3"}, {"Name": "label", "Value": "lib/toastr/js/toastr.min.js"}, {"Name": "integrity", "Value": "sha256-3blsJd4Hli/7wCQ+bmgXfOdK7p/ZUMtPXY08jmxSSgk="}]}, {"Route": "lib/toastr/js/toastr.min.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\toastr\\js\\toastr.min.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5537"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"3blsJd4Hli/7wCQ+bmgXfOdK7p/ZUMtPXY08jmxSSgk=\""}, {"Name": "Last-Modified", "Value": "Wed, 21 May 2025 18:05:33 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-3blsJd4Hli/7wCQ+bmgXfOdK7p/ZUMtPXY08jmxSSgk="}]}, {"Route": "lib/tree-js/tree.1fi7ptg5iw.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\tree-js\\tree.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8093"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/eVkZGlXmiRW9Mk4Dfhp2v23oQLuxtiCxHm0zlOSTRc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 18:08:29 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "1fi7ptg5iw"}, {"Name": "label", "Value": "lib/tree-js/tree.js"}, {"Name": "integrity", "Value": "sha256-/eVkZGlXmiRW9Mk4Dfhp2v23oQLuxtiCxHm0zlOSTRc="}]}, {"Route": "lib/tree-js/tree.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\tree-js\\tree.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5487"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IgLYyHXd4toYJUsbYGNNMke1b+NW9dHhfVZWsv6D/bo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 18:05:32 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-IgLYyHXd4toYJUsbYGNNMke1b+NW9dHhfVZWsv6D/bo="}]}, {"Route": "lib/tree-js/tree.js", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\tree-js\\tree.js", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "8093"}, {"Name": "Content-Type", "Value": "text/javascript"}, {"Name": "ETag", "Value": "\"/eVkZGlXmiRW9Mk4Dfhp2v23oQLuxtiCxHm0zlOSTRc=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 18:08:29 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-/eVkZGlXmiRW9Mk4Dfhp2v23oQLuxtiCxHm0zlOSTRc="}]}, {"Route": "lib/tree-js/tree.mv3unr996a.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\lib\\tree-js\\tree.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "5487"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"IgLYyHXd4toYJUsbYGNNMke1b+NW9dHhfVZWsv6D/bo=\""}, {"Name": "Last-Modified", "Value": "Sat, 24 May 2025 18:05:32 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "mv3unr996a"}, {"Name": "label", "Value": "lib/tree-js/tree.css"}, {"Name": "integrity", "Value": "sha256-IgLYyHXd4toYJUsbYGNNMke1b+NW9dHhfVZWsv6D/bo="}]}, {"Route": "SmartBI.Core.8cpw0666k3.bundle.scp.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\SmartBI.Core.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1111"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 17 Jun 2025 05:17:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8cpw0666k3"}, {"Name": "label", "Value": "SmartBI.Core.bundle.scp.css"}, {"Name": "integrity", "Value": "sha256-SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA="}]}, {"Route": "SmartBI.Core.8cpw0666k3.styles.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\bundle\\SmartBI.Core.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1111"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 17 Jun 2025 05:17:50 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "8cpw0666k3"}, {"Name": "label", "Value": "SmartBI.Core.styles.css"}, {"Name": "integrity", "Value": "sha256-SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA="}]}, {"Route": "SmartBI.Core.bundle.scp.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\projectbundle\\SmartBI.Core.bundle.scp.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1111"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 17 Jun 2025 05:17:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA="}]}, {"Route": "SmartBI.Core.styles.css", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\obj\\Debug\\net8.0\\scopedcss\\bundle\\SmartBI.Core.styles.css", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "1111"}, {"Name": "Content-Type", "Value": "text/css"}, {"Name": "ETag", "Value": "\"SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA=\""}, {"Name": "Last-Modified", "Value": "<PERSON><PERSON>, 17 Jun 2025 05:17:50 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-SABny9HwT0wx4wjPCvsr8Ej21LBUeJzkgNMkUjbL+iA="}]}, {"Route": "UploadedFiles/20250612_194827_RMTarget.ln9cugo920.xlsx", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\UploadedFiles\\20250612_194827_RMTarget.xlsx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9434"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}, {"Name": "ETag", "Value": "\"H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 13:48:27 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ln9cugo920"}, {"Name": "label", "Value": "UploadedFiles/20250612_194827_RMTarget.xlsx"}, {"Name": "integrity", "Value": "sha256-H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI="}]}, {"Route": "UploadedFiles/20250612_194827_RMTarget.xlsx", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\UploadedFiles\\20250612_194827_RMTarget.xlsx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9434"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}, {"Name": "ETag", "Value": "\"H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 13:48:27 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI="}]}, {"Route": "UploadedFiles/20250612_195042_RMTarget.ln9cugo920.xlsx", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\UploadedFiles\\20250612_195042_RMTarget.xlsx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9434"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}, {"Name": "ETag", "Value": "\"H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 13:50:42 GMT"}, {"Name": "Cache-Control", "Value": "max-age=31536000, immutable"}], "EndpointProperties": [{"Name": "fingerprint", "Value": "ln9cugo920"}, {"Name": "label", "Value": "UploadedFiles/20250612_195042_RMTarget.xlsx"}, {"Name": "integrity", "Value": "sha256-H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI="}]}, {"Route": "UploadedFiles/20250612_195042_RMTarget.xlsx", "AssetFile": "E:\\Files From IP 45\\Development\\RoboAdvisory\\Migration\\Working\\SmartBI.Core\\wwwroot\\UploadedFiles\\20250612_195042_RMTarget.xlsx", "Selectors": [], "ResponseHeaders": [{"Name": "Accept-Ranges", "Value": "bytes"}, {"Name": "Content-Length", "Value": "9434"}, {"Name": "Content-Type", "Value": "application/vnd.openxmlformats-officedocument.spreadsheetml.sheet"}, {"Name": "ETag", "Value": "\"H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI=\""}, {"Name": "Last-Modified", "Value": "Thu, 12 Jun 2025 13:50:42 GMT"}, {"Name": "Cache-Control", "Value": "no-cache"}], "EndpointProperties": [{"Name": "integrity", "Value": "sha256-H2YAl6bzY4GC5Dcs702pkyyTBRMG7BpMm05gMDAd1JI="}]}]}