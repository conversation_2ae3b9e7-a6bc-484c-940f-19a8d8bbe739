@model SmartBI.Data.ViewModels.Dashboard.ComplianceDashboardViewModel

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-info">
                <h4 class="card-title">
                    <i class="material-icons">gavel</i>
                    FATCA Compliance
                </h4>
                <p class="card-category">Foreign Account Tax Compliance Act</p>
            </div>
            <div class="card-body">
                @if (Model?.FATCA != null && Model.FATCA.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered compliance-table" id="fatcaTable">
                            <thead>
                                <tr>
                                    <th style="text-align:center;font-weight:600">Branch</th>
                                    <th colspan="2" style="text-align:center;font-weight:600">Individual</th>
                                    <th colspan="2" style="text-align:center;font-weight:600">Entity</th>
                                    <th colspan="2" style="text-align:center;font-weight:600">Total</th>
                                </tr>
                                <tr>
                                    <th></th>
                                    <th style="text-align:center;font-weight:600">Customers</th>
                                    <th style="text-align:center;font-weight:600">Accounts</th>
                                    <th style="text-align:center;font-weight:600">Customers</th>
                                    <th style="text-align:center;font-weight:600">Accounts</th>
                                    <th style="text-align:center;font-weight:600">Customers</th>
                                    <th style="text-align:center;font-weight:600">Accounts</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.FATCA.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.FATCA.Columns.Count && j < 7; j++)
                                        {
                                            <td style="text-align:center;">@Model.FATCA.Rows[i][j]</td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No FATCA Data Available</h4>
                        <p class="text-muted">No FATCA compliance data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#fatcaTable').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "pageLength": 10,
            "scrollX": true
        });
    });
</script>
