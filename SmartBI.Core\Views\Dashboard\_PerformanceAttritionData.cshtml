@model List<SmartBI.Data.ViewModels.RM_ATTRITION_GROWTH_SUMMARY>

<div class="row">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header card-header-rose">
                <h4 class="card-title">RM Attrition Analysis</h4>
                <p class="card-category">Performance & Attrition Growth Summary</p>
            </div>
            <div class="card-body">
                @if (Model != null && Model.Any())
                {
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th>Product Type</th>
                                    <th>Attrition</th>
                                    <th>Growth</th>
                                    <th>Net Growth</th>
                                    <th>Priority</th>
                                </tr>
                            </thead>
                            <tbody>
                                @foreach (var item in Model)
                                {
                                    var attritionClass = item.ATTRITION < 0 ? "text-danger" : "text-success";
                                    var growthClass = item.GROWTH < 0 ? "text-danger" : "text-success";
                                    var netGrowthClass = item.NET_GROWTH < 0 ? "text-danger" : "text-success";
                                    
                                    <tr>
                                        <td>@(item.PRODUCT_TYPE ?? "N/A")</td>
                                        <td class="@attritionClass">@item.ATTRITION.ToString("N2")</td>
                                        <td class="@growthClass">@item.GROWTH.ToString("N2")</td>
                                        <td class="@netGrowthClass">@item.NET_GROWTH.ToString("N2")</td>
                                        <td>
                                            @if (item.PRIORITY == 1)
                                            {
                                                <span class="badge badge-danger">High</span>
                                            }
                                            else if (item.PRIORITY == 2)
                                            {
                                                <span class="badge badge-warning">Medium</span>
                                            }
                                            else
                                            {
                                                <span class="badge badge-success">Low</span>
                                            }
                                        </td>
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No Attrition Data Available</h4>
                        <p class="text-muted">No attrition analysis data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@if (Model != null && Model.Any())
{
    <!-- Summary Cards Row -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-info card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">trending_up</i>
                    </div>
                    <p class="card-category">Total Growth</p>
                    <h3 class="card-title">@Model.Sum(x => x.GROWTH).ToString("N2")</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-info">info</i>
                        Combined growth across products
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-warning card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">trending_down</i>
                    </div>
                    <p class="card-category">Total Attrition</p>
                    <h3 class="card-title">@Model.Sum(x => x.ATTRITION).ToString("N2")</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-warning">info</i>
                        Combined attrition across products
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-success card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">assessment</i>
                    </div>
                    <p class="card-category">Net Growth</p>
                    <h3 class="card-title">@Model.Sum(x => x.NET_GROWTH).ToString("N2")</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-success">info</i>
                        Overall net growth performance
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-rose card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">priority_high</i>
                    </div>
                    <p class="card-category">High Priority Items</p>
                    <h3 class="card-title">@Model.Count(x => x.PRIORITY == 1)</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-rose">info</i>
                        Items requiring immediate attention
                    </div>
                </div>
            </div>
        </div>
    </div>
}
