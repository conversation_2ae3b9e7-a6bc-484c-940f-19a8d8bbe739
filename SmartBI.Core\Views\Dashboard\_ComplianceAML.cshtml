@model SmartBI.Data.ViewModels.Dashboard.ComplianceDashboardViewModel

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-success">
                <h4 class="card-title">
                    <i class="material-icons">shield</i>
                    AML Compliance
                </h4>
                <p class="card-category">Anti-Money Laundering Monitoring</p>
            </div>
            <div class="card-body">
                @if (Model?.AML != null && Model.AML.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered compliance-table" id="amlTable">
                            <thead>
                                <tr>
                                    @for (int i = 0; i < Model.AML.Columns.Count; i++)
                                    {
                                        <th>@Model.AML.Columns[i].ColumnName</th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.AML.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.AML.Columns.Count; j++)
                                        {
                                            <td>@Model.AML.Rows[i][j]</td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>

                    <!-- AML Summary Cards -->
                    @if (Model.AML.Rows.Count > 0 && Model.AML.Columns.Count >= 3)
                    {
                        <div class="row mt-4">
                            <div class="col-lg-4 col-md-6">
                                <div class="card card-stats">
                                    <div class="card-header card-header-info card-header-icon">
                                        <div class="card-icon">
                                            <i class="material-icons">people</i>
                                        </div>
                                        <p class="card-category">Total Customers</p>
                                        <h3 class="card-title">@Model.AML.Rows[0][1]</h3>
                                    </div>
                                    <div class="card-footer">
                                        <div class="stats">
                                            <i class="material-icons text-info">info</i>
                                            AML Monitored
                                        </div>
                                    </div>
                                </div>
                            </div>
                            <div class="col-lg-4 col-md-6">
                                <div class="card card-stats">
                                    <div class="card-header card-header-success card-header-icon">
                                        <div class="card-icon">
                                            <i class="material-icons">business</i>
                                        </div>
                                        <p class="card-category">Total Entity Customers</p>
                                        <h3 class="card-title">@Model.AML.Rows[0][2]</h3>
                                    </div>
                                    <div class="card-footer">
                                        <div class="stats">
                                            <i class="material-icons text-success">info</i>
                                            Entity Accounts
                                        </div>
                                    </div>
                                </div>
                            </div>
                            @if (Model.AML.Columns.Count >= 4)
                            {
                                <div class="col-lg-4 col-md-6">
                                    <div class="card card-stats">
                                        <div class="card-header card-header-warning card-header-icon">
                                            <div class="card-icon">
                                                <i class="material-icons">trending_up</i>
                                            </div>
                                            <p class="card-category">Additional Metric</p>
                                            <h3 class="card-title">@Model.AML.Rows[0][3]</h3>
                                        </div>
                                        <div class="card-footer">
                                            <div class="stats">
                                                <i class="material-icons text-warning">info</i>
                                                AML Compliance
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            }
                        </div>
                    }
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No AML Data Available</h4>
                        <p class="text-muted">No AML compliance data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#amlTable').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "pageLength": 10
        });
    });
</script>
