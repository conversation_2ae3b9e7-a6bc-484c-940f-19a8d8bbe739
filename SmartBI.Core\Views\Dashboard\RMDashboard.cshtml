@{
    ViewData["Title"] = "RM Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-rose">
                        <h4 class="card-title">RM Dashboard</h4>
                        <p class="card-category">Relationship Manager Performance Analytics</p>
                    </div>
                    <div class="card-body">
                        <!-- Alert Messages -->
                        <div id="errorAlert" class="alert alert-danger" style="display: none;">
                            <span id="errorMessage"></span>
                        </div>
                        <div id="successAlert" class="alert alert-success" style="display: none;">
                            <span id="successMessage"></span>
                        </div>

                        <!-- Navigation Pills -->
                        <ul class="nav nav-pills nav-pills-rose" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="pill" href="#performance" role="tab">
                                    <i class="material-icons">dashboard</i> Performance Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="pill" href="#attrition" role="tab">
                                    <i class="material-icons">trending_up</i> Growth & Attrition
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="pill" href="#targets" role="tab">
                                    <i class="material-icons">track_changes</i> Target vs Achievement
                                </a>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content">
                            <!-- Performance Dashboard Tab -->
                            <div class="tab-pane fade show active" id="performance" role="tabpanel">
                                <div class="text-center" id="performanceLoader" style="display: none;">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p>Loading performance data...</p>
                                </div>
                                <div id="performanceContent">
                                    <!-- Performance cards will be loaded here -->
                                </div>
                            </div>

                            <!-- Growth & Attrition Tab -->
                            <div class="tab-pane fade" id="attrition" role="tabpanel">
                                <div class="text-center" id="attritionLoader" style="display: none;">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p>Loading attrition data...</p>
                                </div>
                                <div id="attritionContent">
                                    <!-- Attrition tables will be loaded here -->
                                </div>
                            </div>

                            <!-- Target vs Achievement Tab -->
                            <div class="tab-pane fade" id="targets" role="tabpanel">
                                <div class="text-center" id="targetsLoader" style="display: none;">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p>Loading target data...</p>
                                </div>
                                <div id="targetsContent">
                                    <!-- Target table will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Refresh Button -->
                        <div class="row mt-3">
                            <div class="col-12 text-center">
                                <button type="button" class="btn btn-rose" onclick="refreshDashboard()">
                                    <i class="material-icons">refresh</i> Refresh Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Simple RM Dashboard Implementation
        $(document).ready(function() {
            console.log('RM Dashboard initialized');
            
            // Load initial data
            loadPerformanceData();
            
            // Tab click handlers
            $('a[data-toggle="pill"]').on('shown.bs.tab', function (e) {
                var target = $(e.target).attr("href");
                console.log('Tab switched to:', target);
                
                if (target === '#performance') {
                    loadPerformanceData();
                } else if (target === '#attrition') {
                    loadAttritionData();
                } else if (target === '#targets') {
                    loadTargetsData();
                }
            });
        });

        // Refresh all data
        function refreshDashboard() {
            console.log('Refreshing dashboard data');
            var activeTab = $('.nav-link.active').attr('href');
            
            if (activeTab === '#performance') {
                loadPerformanceData();
            } else if (activeTab === '#attrition') {
                loadAttritionData();
            } else if (activeTab === '#targets') {
                loadTargetsData();
            }
            
            showMessage('Dashboard data refreshed successfully', 'success');
        }

        // Load Performance Data
        function loadPerformanceData() {
            console.log('Loading performance data...');
            showLoader('#performanceLoader');
            
            $.ajax({
                url: '@Url.Action("GetRMDashboardData", "Dashboard")',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    hideLoader('#performanceLoader');
                    if (response && response.success) {
                        renderPerformanceCards(response.data);
                    } else {
                        renderPerformanceCards(getSamplePerformanceData());
                    }
                },
                error: function(xhr, status, error) {
                    hideLoader('#performanceLoader');
                    console.error('Performance data error:', error);
                    renderPerformanceCards(getSamplePerformanceData());
                }
            });
        }

        // Load Attrition Data
        function loadAttritionData() {
            console.log('Loading attrition data...');
            showLoader('#attritionLoader');
            
            // Load both growth and attrition data
            Promise.all([
                $.ajax({
                    url: '@Url.Action("GetRMGrowthData", "Dashboard")',
                    type: 'GET',
                    dataType: 'json'
                }),
                $.ajax({
                    url: '@Url.Action("GetPerformanceAttritionData", "Dashboard")',
                    type: 'GET',
                    dataType: 'json'
                })
            ]).then(function(results) {
                hideLoader('#attritionLoader');
                var growthData = (results[0] && results[0].success) ? results[0].data : getSampleGrowthData();
                var attritionData = (results[1] && results[1].success) ? results[1].data : getSampleAttritionData();
                renderAttritionTables(growthData, attritionData);
            }).catch(function(error) {
                hideLoader('#attritionLoader');
                console.error('Attrition data error:', error);
                renderAttritionTables(getSampleGrowthData(), getSampleAttritionData());
            });
        }

        // Load Targets Data
        function loadTargetsData() {
            console.log('Loading targets data...');
            showLoader('#targetsLoader');
            
            $.ajax({
                url: '@Url.Action("GetTargetAchievementData", "Dashboard")',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    hideLoader('#targetsLoader');
                    if (response && response.success) {
                        renderTargetsTable(response.data);
                    } else {
                        renderTargetsTable(getSampleTargetsData());
                    }
                },
                error: function(xhr, status, error) {
                    hideLoader('#targetsLoader');
                    console.error('Targets data error:', error);
                    renderTargetsTable(getSampleTargetsData());
                }
            });
        }

        // Render Performance Cards
        function renderPerformanceCards(data) {
            var html = `
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-danger card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">account_balance_wallet</i>
                                </div>
                                <p class="card-category">Total Accounts</p>
                                <h3 class="card-title">${formatNumber(data.TOTAL_AC || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-danger">info</i>
                                    Total account count
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-danger card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">account_balance</i>
                                </div>
                                <p class="card-category">Total Balances</p>
                                <h3 class="card-title">${formatCurrency(data.TOTAL_BAL || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-danger">info</i>
                                    Total balance amount
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-danger card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">trending_up</i>
                                </div>
                                <p class="card-category">Avg Balances</p>
                                <h3 class="card-title">${formatCurrency(data.AVG_BAL || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-danger">info</i>
                                    Average balance per account
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-danger card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">monetization_on</i>
                                </div>
                                <p class="card-category">Cost of Deposit</p>
                                <h3 class="card-title">${formatPercentage(data.COD || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-danger">info</i>
                                    Cost of deposit rate
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#performanceContent').html(html);
        }

        // Render Attrition Tables
        function renderAttritionTables(growthData, attritionData) {
            var html = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-rose">
                                <h4 class="card-title">RM Performance Growth</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Growth Type</th>
                                                <th>Product</th>
                                                <th>Total Accounts</th>
                                                <th>Total Balances</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${buildGrowthTableRows(growthData)}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-rose">
                                <h4 class="card-title">RM Attrition Analysis</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Attrition</th>
                                                <th>Growth</th>
                                                <th>Net Growth</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${buildAttritionTableRows(attritionData)}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#attritionContent').html(html);
        }

        // Render Targets Table
        function renderTargetsTable(data) {
            var html = `
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-rose">
                                <h4 class="card-title">Target VS Achievement</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th colspan="4" style="text-align:center;">Target</th>
                                                <th colspan="4" style="text-align:center;">Achievement</th>
                                                <th colspan="4" style="text-align:center;">Variance</th>
                                                <th colspan="2" style="text-align:center;">Overall Performance</th>
                                            </tr>
                                            <tr>
                                                <th colspan="2" style="text-align:center;">Account</th>
                                                <th colspan="2" style="text-align:center;">Volume</th>
                                                <th colspan="2" style="text-align:center;">Account</th>
                                                <th colspan="2" style="text-align:center;">Volume</th>
                                                <th colspan="2" style="text-align:center;">Account</th>
                                                <th colspan="2" style="text-align:center;">Volume</th>
                                                <th colspan="2" style="text-align:center;"></th>
                                            </tr>
                                            <tr>
                                                <th>Monthly</th>
                                                <th>Yearly</th>
                                                <th>Monthly</th>
                                                <th>Yearly</th>
                                                <th>Monthly</th>
                                                <th>Yearly</th>
                                                <th>Monthly</th>
                                                <th>Yearly</th>
                                                <th>MTD</th>
                                                <th>YTD</th>
                                                <th>MTD</th>
                                                <th>YTD</th>
                                                <th>Account Growth</th>
                                                <th>Volume Growth</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>${formatNumber(data.MONTHLY_TARGET_ACCOUNTS || 0)}</td>
                                                <td>${formatNumber(data.YEARLY_TARGET_ACCOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.MONTHLY_TARGET_AMOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.YEARLY_TARGET_AMOUNTS || 0)}</td>
                                                <td>${formatNumber(data.MTD_ACHIEVED_ACCOUNTS || 0)}</td>
                                                <td>${formatNumber(data.YTD_ACHIEVED_ACCOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.MTD_ACHIEVED_AMOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.YTD_ACHIEVED_AMOUNTS || 0)}</td>
                                                <td>${formatNumber(data.MTD_VARIANCE_ACCOUNTS || 0)}</td>
                                                <td>${formatNumber(data.YTD_VARIANCE_ACCOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.MTD_VARIANCE_AMOUNS || 0)}</td>
                                                <td>${formatCurrency(data.YTD_VARIANCE_AMOUNTS || 0)}</td>
                                                <td>${formatPercentage(data.ACC_PERFORMANCE || 0)}</td>
                                                <td>${formatPercentage(data.VOL_PERFORMANCE || 0)}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#targetsContent').html(html);
        }

        // Helper Functions
        function buildGrowthTableRows(growthData) {
            if (!growthData || !Array.isArray(growthData)) {
                return '<tr><td colspan="4" class="text-center">No growth data available</td></tr>';
            }

            return growthData.map(function(item) {
                return `
                    <tr>
                        <td>${item.GROWTH_TYPE || ''}</td>
                        <td>${item.PRODUCT_TYPE || ''}</td>
                        <td>${formatNumber(item.NO_OF_ACCOUNS || 0)}</td>
                        <td>${formatCurrency(item.TOTAL_BALANCES || 0)}</td>
                    </tr>
                `;
            }).join('');
        }

        function buildAttritionTableRows(attritionData) {
            if (!attritionData || !Array.isArray(attritionData)) {
                return '<tr><td colspan="4" class="text-center">No attrition data available</td></tr>';
            }

            return attritionData.map(function(item) {
                var attritionClass = (item.ATTRITION || 0) < 0 ? 'text-danger' : 'text-success';
                var growthClass = (item.GROWTH || 0) < 0 ? 'text-danger' : 'text-success';
                var netGrowthClass = (item.NET_GROWTH || 0) < 0 ? 'text-danger' : 'text-success';

                return `
                    <tr>
                        <td>${item.PRODUCT_TYPE || ''}</td>
                        <td class="${attritionClass}">${formatNumber(item.ATTRITION || 0)}</td>
                        <td class="${growthClass}">${formatNumber(item.GROWTH || 0)}</td>
                        <td class="${netGrowthClass}">${formatNumber(item.NET_GROWTH || 0)}</td>
                    </tr>
                `;
            }).join('');
        }

        // Formatting Functions
        function formatNumber(value) {
            return new Intl.NumberFormat('en-US').format(value || 0);
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'BDT',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value || 0);
        }

        function formatPercentage(value) {
            return (value || 0).toFixed(2) + '%';
        }

        // Utility Functions
        function showLoader(selector) {
            $(selector).show();
        }

        function hideLoader(selector) {
            $(selector).hide();
        }

        function showMessage(message, type) {
            if (type === 'success') {
                $('#successMessage').text(message);
                $('#successAlert').show().delay(3000).fadeOut();
            } else {
                $('#errorMessage').text(message);
                $('#errorAlert').show().delay(5000).fadeOut();
            }
        }

        // Sample Data Functions
        function getSamplePerformanceData() {
            return {
                TOTAL_AC: 191,
                TOTAL_BAL: *********.58,
                AVG_BAL: 632789.55,
                COD: 6.61,
                CA_AC: 25,
                CA_BAL: *********.09,
                CA_AVG_BAL: 8369949.56,
                NO_OF_DORMANT_ACCOUNTS: 4,
                SA_AC: 54,
                SA_BAL: *********.29,
                SA_AVG_BAL: 2582391.86,
                BALANCE_OF_DORMANT_ACCOUNTS: 113346.00,
                FDR_AC: 12,
                FDR_BAL: ********.00,
                FDR_AVG_BAL: 4166666.67,
                DPS_AC: 8,
                DPS_BAL: ********.00,
                DPS_AVG_BAL: 3125000.00,
                NO_OF_ZERO_ACCOUNTS: 3,
                BALANCE_OF_ZERO_ACCOUNTS: 0.00
            };
        }

        function getSampleGrowthData() {
            return [
                { GROWTH_TYPE: 'New Customer', PRODUCT_TYPE: 'Current Account', NO_OF_ACCOUNS: 15, TOTAL_BALANCES: 5000000.00 },
                { GROWTH_TYPE: 'New Customer', PRODUCT_TYPE: 'Savings Account', NO_OF_ACCOUNS: 25, TOTAL_BALANCES: 8000000.00 },
                { GROWTH_TYPE: 'New Customer', PRODUCT_TYPE: 'FDR', NO_OF_ACCOUNS: 8, TOTAL_BALANCES: ********.00 },
                { GROWTH_TYPE: 'Existing Customer', PRODUCT_TYPE: 'Current Account', NO_OF_ACCOUNS: 12, TOTAL_BALANCES: 3500000.00 },
                { GROWTH_TYPE: 'Existing Customer', PRODUCT_TYPE: 'Savings Account', NO_OF_ACCOUNS: 18, TOTAL_BALANCES: 6200000.00 },
                { GROWTH_TYPE: 'Existing Customer', PRODUCT_TYPE: 'DPS', NO_OF_ACCOUNS: 5, TOTAL_BALANCES: 2800000.00 }
            ];
        }

        function getSampleAttritionData() {
            return [
                { PRODUCT_TYPE: 'Current Account', ATTRITION: -2.5, GROWTH: 5.2, NET_GROWTH: 2.7 },
                { PRODUCT_TYPE: 'Savings Account', ATTRITION: -1.8, GROWTH: 4.1, NET_GROWTH: 2.3 },
                { PRODUCT_TYPE: 'FDR', ATTRITION: -0.5, GROWTH: 3.8, NET_GROWTH: 3.3 },
                { PRODUCT_TYPE: 'DPS', ATTRITION: -1.2, GROWTH: 2.9, NET_GROWTH: 1.7 }
            ];
        }

        function getSampleTargetsData() {
            return {
                MONTHLY_TARGET_ACCOUNTS: 50,
                YEARLY_TARGET_ACCOUNTS: 600,
                MONTHLY_TARGET_AMOUNTS: ********.00,
                YEARLY_TARGET_AMOUNTS: ********0.00,
                MTD_ACHIEVED_ACCOUNTS: 42,
                YTD_ACHIEVED_ACCOUNTS: 485,
                MTD_ACHIEVED_AMOUNTS: 8750000.00,
                YTD_ACHIEVED_AMOUNTS: ********.00,
                MTD_VARIANCE_ACCOUNTS: -8,
                YTD_VARIANCE_ACCOUNTS: -115,
                MTD_VARIANCE_AMOUNS: -1250000.00,
                YTD_VARIANCE_AMOUNTS: -********.00,
                ACC_PERFORMANCE: 84.00,
                VOL_PERFORMANCE: 87.50
            };
        }
    </script>
}

@section Styles {
    <style>
        /* Material Dashboard Pro Navigation Pills */
        .nav-pills .nav-link {
            border-radius: 30px;
            margin-right: 10px;
            padding: 10px 20px;
            color: #999;
            background: transparent;
            border: 1px solid transparent;
        }

        .nav-pills .nav-link.active {
            background: linear-gradient(60deg, #e91e63, #ad1457);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(233, 30, 99, 0.4);
            color: #fff;
        }

        .nav-pills .nav-link:hover {
            background: rgba(233, 30, 99, 0.1);
            color: #e91e63;
        }

        /* Card Headers */
        .card-header-rose {
            background: linear-gradient(60deg, #e91e63, #ad1457);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(233, 30, 99, 0.4);
        }

        .card-header-danger {
            background: linear-gradient(60deg, #ef5350, #e53935);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(244, 67, 54, 0.4);
        }

        /* Dashboard Cards */
        .card-stats {
            transition: transform 0.2s ease-in-out;
            margin-bottom: 30px;
            min-height: 180px;
        }

        .card-stats:hover {
            transform: translateY(-2px);
        }

        .card-stats .card-header {
            border-radius: 3px;
            padding: 15px;
            margin: -20px 15px 0;
            position: relative;
            z-index: 2;
        }

        .card-stats .card-header.card-header-icon {
            text-align: right;
            padding: 15px;
        }

        .card-stats .card-title {
            margin: 0;
            color: #fff;
            font-weight: 300;
            font-size: 1.8em;
            line-height: 1.2em;
            margin-top: 10px;
            word-break: break-word;
        }

        .card-stats .card-category {
            color: #fff;
            margin: 0;
            font-size: 14px;
            margin-top: 0;
            padding-top: 10px;
            margin-bottom: 0;
            font-weight: 400;
        }

        .card-stats .card-icon {
            float: left;
            padding: 15px;
            margin-top: -20px;
            margin-right: 15px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            width: 70px;
            height: 70px;
            text-align: center;
        }

        .card-stats .card-icon i {
            font-size: 36px;
            line-height: 40px;
            color: #fff;
            width: 40px;
            height: 40px;
            text-align: center;
        }

        .card-stats .card-footer {
            padding: 0;
            line-height: 30px;
            border-top: none;
            background: transparent;
        }

        .card-stats .card-footer .stats {
            color: #999999;
            font-size: 12px;
            line-height: 22px;
            padding: 15px;
        }

        .card-stats .card-footer .stats i {
            font-size: 16px;
            margin-right: 5px;
            position: relative;
            top: 3px;
        }

        /* Table Styling */
        .table th {
            border-top: none;
            font-weight: 500;
            font-size: 14px;
        }

        .table td {
            font-size: 14px;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* Color Classes */
        .text-danger {
            color: #f44336 !important;
        }

        .text-success {
            color: #4caf50 !important;
        }

        /* Responsive adjustments */
        @media (max-width: 768px) {
            .card-stats .card-title {
                font-size: 1.4em;
                line-height: 1.1em;
            }

            .nav-pills .nav-link {
                margin-bottom: 5px;
                margin-right: 5px;
                padding: 8px 15px;
            }

            .card-stats {
                margin-bottom: 20px;
                min-height: 160px;
            }
        }

        @media (max-width: 576px) {
            .card-stats .card-title {
                font-size: 1.2em;
                line-height: 1.1em;
            }

            .card-stats {
                min-height: 140px;
            }
        }
    </style>
}
