@{
    ViewData["Title"] = "RM Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-rose">
                        <h4 class="card-title">RM Dashboard</h4>
                        <p class="card-category">Relationship Manager Performance Analytics</p>
                    </div>
                    <div class="card-body">
                        <!-- Alert Messages -->
                        <div id="errorAlert" class="alert alert-danger" style="display: none;">
                            <span id="errorMessage"></span>
                        </div>
                        <div id="successAlert" class="alert alert-success" style="display: none;">
                            <span id="successMessage"></span>
                        </div>

                        <!-- Navigation Pills -->
                        <ul class="nav nav-pills nav-pills-rose" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="pill" href="#performance" role="tab">
                                    <i class="material-icons">dashboard</i> Performance Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="pill" href="#attrition" role="tab">
                                    <i class="material-icons">trending_up</i> Growth & Attrition
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="pill" href="#targets" role="tab">
                                    <i class="material-icons">track_changes</i> Target vs Achievement
                                </a>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content">
                            <!-- Performance Dashboard Tab -->
                            <div class="tab-pane fade show active" id="performance" role="tabpanel">
                                <div class="text-center" id="performanceLoader" style="display: none;">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p>Loading performance data...</p>
                                </div>
                                <div id="performanceContent">
                                    <!-- Performance cards will be loaded here -->
                                </div>
                            </div>

                            <!-- Growth & Attrition Tab -->
                            <div class="tab-pane fade" id="attrition" role="tabpanel">
                                <div class="text-center" id="attritionLoader" style="display: none;">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p>Loading attrition data...</p>
                                </div>
                                <div id="attritionContent">
                                    <!-- Attrition tables will be loaded here -->
                                </div>
                            </div>

                            <!-- Target vs Achievement Tab -->
                            <div class="tab-pane fade" id="targets" role="tabpanel">
                                <div class="text-center" id="targetsLoader" style="display: none;">
                                    <div class="spinner-border text-primary" role="status"></div>
                                    <p>Loading target data...</p>
                                </div>
                                <div id="targetsContent">
                                    <!-- Target table will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Refresh Button -->
                        <div class="row mt-3">
                            <div class="col-12 text-center">
                                <button type="button" class="btn btn-rose" onclick="refreshDashboard()">
                                    <i class="material-icons">refresh</i> Refresh Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Simple RM Dashboard Implementation
        $(document).ready(function() {
            console.log('RM Dashboard initialized');

            // Load initial data
            loadPerformanceData();

            // Tab click handlers - Multiple approaches for compatibility
            $('a[data-toggle="pill"]').on('shown.bs.tab', function (e) {
                var target = $(e.target).attr("href");
                console.log('Tab switched to:', target);
                handleTabSwitch(target);
            });

            // Fallback click handler for direct clicks
            $('a[data-toggle="pill"]').on('click', function (e) {
                e.preventDefault();
                var target = $(this).attr("href");
                console.log('Tab clicked:', target);

                // Remove active class from all tabs and content
                $('a[data-toggle="pill"]').removeClass('active');
                $('.tab-pane').removeClass('show active');

                // Add active class to clicked tab and corresponding content
                $(this).addClass('active');
                $(target).addClass('show active');

                // Load data for the tab
                handleTabSwitch(target);
            });
        });

        // Handle tab switching logic
        function handleTabSwitch(target) {
            if (target === '#performance') {
                loadPerformanceData();
            } else if (target === '#attrition') {
                loadAttritionData();
            } else if (target === '#targets') {
                loadTargetsData();
            }
        }

        // Refresh all data
        function refreshDashboard() {
            console.log('Refreshing dashboard data');
            var activeTab = $('.nav-link.active').attr('href');
            console.log('Active tab for refresh:', activeTab);

            handleTabSwitch(activeTab);
            showMessage('Dashboard data refreshed successfully', 'success');
        }

        // Load Performance Data
        function loadPerformanceData() {
            console.log('Loading performance data...');
            showLoader('#performanceLoader');

            $.ajax({
                url: '@Url.Action("GetPerformanceData", "Dashboard")',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    hideLoader('#performanceLoader');
                    if (response && response.success && response.data) {
                        var performanceData = Array.isArray(response.data) ? response.data[0] : response.data;
                        renderPerformanceCards(performanceData);
                    } else {
                        showErrorState('#performanceContent', 'No performance data available from API');
                    }
                },
                error: function(xhr, status, error) {
                    hideLoader('#performanceLoader');
                    console.error('Performance data error:', error);
                    showErrorState('#performanceContent', 'Failed to load performance data. API connection error: ' + (error || status));
                }
            });
        }

        // Load Attrition Data
        function loadAttritionData() {
            console.log('Loading attrition data...');
            showLoader('#attritionLoader');

            // Load both growth and attrition data
            Promise.all([
                $.ajax({
                    url: '@Url.Action("GetRMGrowthData", "Dashboard")',
                    type: 'GET',
                    dataType: 'json'
                }),
                $.ajax({
                    url: '@Url.Action("GetPerformanceAttritionData", "Dashboard")',
                    type: 'GET',
                    dataType: 'json'
                })
            ]).then(function(results) {
                hideLoader('#attritionLoader');
                var growthResponse = results[0];
                var attritionResponse = results[1];

                if (growthResponse && growthResponse.success && growthResponse.data &&
                    attritionResponse && attritionResponse.success && attritionResponse.data) {
                    renderAttritionTables(growthResponse.data, attritionResponse.data);
                } else {
                    var errorMsg = 'API data unavailable: ';
                    if (!growthResponse || !growthResponse.success) errorMsg += 'Growth data failed. ';
                    if (!attritionResponse || !attritionResponse.success) errorMsg += 'Attrition data failed.';
                    showErrorState('#attritionContent', errorMsg);
                }
            }).catch(function(error) {
                hideLoader('#attritionLoader');
                console.error('Attrition data error:', error);
                showErrorState('#attritionContent', 'Failed to load attrition data. API connection error: ' + (error.statusText || error.message || error));
            });
        }

        // Load Targets Data
        function loadTargetsData() {
            console.log('Loading targets data...');
            showLoader('#targetsLoader');

            $.ajax({
                url: '@Url.Action("GetTargetAchievementData", "Dashboard")',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    hideLoader('#targetsLoader');
                    if (response && response.success && response.data) {
                        renderTargetsTable(response.data);
                    } else {
                        showErrorState('#targetsContent', 'No target achievement data available from API');
                    }
                },
                error: function(xhr, status, error) {
                    hideLoader('#targetsLoader');
                    console.error('Targets data error:', error);
                    showErrorState('#targetsContent', 'Failed to load target data. API connection error: ' + (error || status));
                }
            });
        }

        // Render Performance Cards
        function renderPerformanceCards(data) {
            if (!data) {
                showErrorState('#performanceContent', 'No performance data received from API');
                return;
            }

            var html = `
                <!-- Summary Cards Row -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-rose card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">account_balance_wallet</i>
                                </div>
                                <p class="card-category">Total Accounts</p>
                                <h3 class="card-title">${formatNumber(data.TOTAL_AC || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-rose">info</i>
                                    All account types combined
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-success card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">account_balance</i>
                                </div>
                                <p class="card-category">Total Balances</p>
                                <h3 class="card-title">${formatCurrency(data.TOTAL_BAL || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-success">info</i>
                                    Combined balance amount
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-info card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">trending_up</i>
                                </div>
                                <p class="card-category">Avg Balances</p>
                                <h3 class="card-title">${formatCurrency(data.AVG_BAL || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-info">info</i>
                                    Average balance per account
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-warning card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">monetization_on</i>
                                </div>
                                <p class="card-category">Cost of Deposit</p>
                                <h3 class="card-title">${formatPercentage(data.COD || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-warning">info</i>
                                    Cost of deposit rate
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Product-wise Account Cards Row -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-primary card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">account_box</i>
                                </div>
                                <p class="card-category">Current Accounts</p>
                                <h3 class="card-title">${formatNumber(data.CA_AC || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-primary">account_balance_wallet</i>
                                    ${formatCurrency(data.CA_BAL || 0)}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-primary card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">savings</i>
                                </div>
                                <p class="card-category">Savings Accounts</p>
                                <h3 class="card-title">${formatNumber(data.SA_AC || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-primary">account_balance_wallet</i>
                                    ${formatCurrency(data.SA_BAL || 0)}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-primary card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">schedule</i>
                                </div>
                                <p class="card-category">FDR Accounts</p>
                                <h3 class="card-title">${formatNumber(data.FDR_AC || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-primary">account_balance_wallet</i>
                                    ${formatCurrency(data.FDR_BAL || 0)}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-primary card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">payment</i>
                                </div>
                                <p class="card-category">DPS Accounts</p>
                                <h3 class="card-title">${formatNumber(data.DPS_AC || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-primary">account_balance_wallet</i>
                                    ${formatCurrency(data.DPS_BAL || 0)}
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Average Balance Cards Row -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-secondary card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">trending_flat</i>
                                </div>
                                <p class="card-category">CA Avg Balance</p>
                                <h3 class="card-title">${formatCurrency(data.CA_AVG_BAL || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-secondary">info</i>
                                    Per current account
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-secondary card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">trending_flat</i>
                                </div>
                                <p class="card-category">SA Avg Balance</p>
                                <h3 class="card-title">${formatCurrency(data.SA_AVG_BAL || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-secondary">info</i>
                                    Per savings account
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-secondary card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">trending_flat</i>
                                </div>
                                <p class="card-category">FDR Avg Balance</p>
                                <h3 class="card-title">${formatCurrency(data.FDR_AVG_BAL || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-secondary">info</i>
                                    Per FDR account
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-secondary card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">trending_flat</i>
                                </div>
                                <p class="card-category">DPS Avg Balance</p>
                                <h3 class="card-title">${formatCurrency(data.DPS_AVG_BAL || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-secondary">info</i>
                                    Per DPS account
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Special Accounts Row -->
                <div class="row">
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-danger card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">warning</i>
                                </div>
                                <p class="card-category">Dormant Accounts</p>
                                <h3 class="card-title">${formatNumber(data.NO_OF_DORMANT_ACCOUNTS || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-danger">account_balance_wallet</i>
                                    ${formatCurrency(data.BALANCE_OF_DORMANT_ACCOUNTS || 0)}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-danger card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">block</i>
                                </div>
                                <p class="card-category">Zero Balance Accounts</p>
                                <h3 class="card-title">${formatNumber(data.NO_OF_ZERO_ACCOUNTS || 0)}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-danger">account_balance_wallet</i>
                                    ${formatCurrency(data.BALANCE_OF_ZERO_ACCOUNTS || 0)}
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-rose card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">person</i>
                                </div>
                                <p class="card-category">RM Code</p>
                                <h3 class="card-title">${data.RM_CODE || 'N/A'}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-rose">info</i>
                                    Relationship Manager
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-3 col-md-6">
                        <div class="card card-stats">
                            <div class="card-header card-header-rose card-header-icon">
                                <div class="card-icon">
                                    <i class="material-icons">badge</i>
                                </div>
                                <p class="card-category">RM Name</p>
                                <h3 class="card-title" style="font-size: 1.2em;">${data.RM_NAME || 'N/A'}</h3>
                            </div>
                            <div class="card-footer">
                                <div class="stats">
                                    <i class="material-icons text-rose">info</i>
                                    Manager Name
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#performanceContent').html(html);
        }

        // Render Attrition Tables
        function renderAttritionTables(growthData, attritionData) {
            if (!growthData && !attritionData) {
                showErrorState('#attritionContent', 'No growth or attrition data received from API');
                return;
            }

            var html = `
                <div class="row">
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-rose">
                                <h4 class="card-title">RM Performance Growth</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Growth Type</th>
                                                <th>Product</th>
                                                <th>Total Accounts</th>
                                                <th>Total Balances</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${buildGrowthTableRows(growthData)}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="card">
                            <div class="card-header card-header-rose">
                                <h4 class="card-title">RM Attrition Analysis</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th>Product</th>
                                                <th>Attrition</th>
                                                <th>Growth</th>
                                                <th>Net Growth</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            ${buildAttritionTableRows(attritionData)}
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#attritionContent').html(html);
        }

        // Render Targets Table
        function renderTargetsTable(data) {
            if (!data) {
                showErrorState('#targetsContent', 'No target achievement data received from API');
                return;
            }

            var html = `
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-header card-header-rose">
                                <h4 class="card-title">Target VS Achievement</h4>
                            </div>
                            <div class="card-body">
                                <div class="table-responsive">
                                    <table class="table table-striped">
                                        <thead>
                                            <tr>
                                                <th colspan="4" style="text-align:center;">Target</th>
                                                <th colspan="4" style="text-align:center;">Achievement</th>
                                                <th colspan="4" style="text-align:center;">Variance</th>
                                                <th colspan="2" style="text-align:center;">Overall Performance</th>
                                            </tr>
                                            <tr>
                                                <th colspan="2" style="text-align:center;">Account</th>
                                                <th colspan="2" style="text-align:center;">Volume</th>
                                                <th colspan="2" style="text-align:center;">Account</th>
                                                <th colspan="2" style="text-align:center;">Volume</th>
                                                <th colspan="2" style="text-align:center;">Account</th>
                                                <th colspan="2" style="text-align:center;">Volume</th>
                                                <th colspan="2" style="text-align:center;"></th>
                                            </tr>
                                            <tr>
                                                <th>Monthly</th>
                                                <th>Yearly</th>
                                                <th>Monthly</th>
                                                <th>Yearly</th>
                                                <th>Monthly</th>
                                                <th>Yearly</th>
                                                <th>Monthly</th>
                                                <th>Yearly</th>
                                                <th>MTD</th>
                                                <th>YTD</th>
                                                <th>MTD</th>
                                                <th>YTD</th>
                                                <th>Account Growth</th>
                                                <th>Volume Growth</th>
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td>${formatNumber(data.MONTHLY_TARGET_ACCOUNTS || 0)}</td>
                                                <td>${formatNumber(data.YEARLY_TARGET_ACCOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.MONTHLY_TARGET_AMOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.YEARLY_TARGET_AMOUNTS || 0)}</td>
                                                <td>${formatNumber(data.MTD_ACHIEVED_ACCOUNTS || 0)}</td>
                                                <td>${formatNumber(data.YTD_ACHIEVED_ACCOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.MTD_ACHIEVED_AMOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.YTD_ACHIEVED_AMOUNTS || 0)}</td>
                                                <td>${formatNumber(data.MTD_VARIANCE_ACCOUNTS || 0)}</td>
                                                <td>${formatNumber(data.YTD_VARIANCE_ACCOUNTS || 0)}</td>
                                                <td>${formatCurrency(data.MTD_VARIANCE_AMOUNS || 0)}</td>
                                                <td>${formatCurrency(data.YTD_VARIANCE_AMOUNTS || 0)}</td>
                                                <td>${formatPercentage(data.ACC_PERFORMANCE || 0)}</td>
                                                <td>${formatPercentage(data.VOL_PERFORMANCE || 0)}</td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $('#targetsContent').html(html);
        }

        // Helper Functions
        function buildGrowthTableRows(growthData) {
            if (!growthData || !Array.isArray(growthData) || growthData.length === 0) {
                return '<tr><td colspan="4" class="text-center text-muted">No growth data available from API</td></tr>';
            }

            return growthData.map(function(item) {
                return `
                    <tr>
                        <td>${item.GROWTH_TYPE || 'N/A'}</td>
                        <td>${item.PRODUCT_TYPE || 'N/A'}</td>
                        <td>${formatNumber(item.NO_OF_ACCOUNS || 0)}</td>
                        <td>${formatCurrency(item.TOTAL_BALANCES || 0)}</td>
                    </tr>
                `;
            }).join('');
        }

        function buildAttritionTableRows(attritionData) {
            if (!attritionData || !Array.isArray(attritionData) || attritionData.length === 0) {
                return '<tr><td colspan="4" class="text-center text-muted">No attrition data available from API</td></tr>';
            }

            return attritionData.map(function(item) {
                var attritionClass = (item.ATTRITION || 0) < 0 ? 'text-danger' : 'text-success';
                var growthClass = (item.GROWTH || 0) < 0 ? 'text-danger' : 'text-success';
                var netGrowthClass = (item.NET_GROWTH || 0) < 0 ? 'text-danger' : 'text-success';

                return `
                    <tr>
                        <td>${item.PRODUCT_TYPE || 'N/A'}</td>
                        <td class="${attritionClass}">${formatNumber(item.ATTRITION || 0)}</td>
                        <td class="${growthClass}">${formatNumber(item.GROWTH || 0)}</td>
                        <td class="${netGrowthClass}">${formatNumber(item.NET_GROWTH || 0)}</td>
                    </tr>
                `;
            }).join('');
        }

        // Formatting Functions
        function formatNumber(value) {
            return new Intl.NumberFormat('en-US').format(value || 0);
        }

        function formatCurrency(value) {
            return new Intl.NumberFormat('en-US', {
                style: 'currency',
                currency: 'BDT',
                minimumFractionDigits: 2,
                maximumFractionDigits: 2
            }).format(value || 0);
        }

        function formatPercentage(value) {
            return (value || 0).toFixed(2) + '%';
        }

        // Utility Functions
        function showLoader(selector) {
            $(selector).show();
        }

        function hideLoader(selector) {
            $(selector).hide();
        }

        function showMessage(message, type) {
            if (type === 'success') {
                $('#successMessage').text(message);
                $('#successAlert').show().delay(3000).fadeOut();
            } else {
                $('#errorMessage').text(message);
                $('#errorAlert').show().delay(5000).fadeOut();
            }
        }

        function showErrorState(selector, message) {
            var errorHtml = `
                <div class="row">
                    <div class="col-12">
                        <div class="card">
                            <div class="card-body text-center py-5">
                                <i class="material-icons" style="font-size: 48px; color: #f44336;">error_outline</i>
                                <h4 class="mt-3 text-muted">Data Unavailable</h4>
                                <p class="text-muted">${message}</p>
                                <button type="button" class="btn btn-rose btn-sm" onclick="refreshDashboard()">
                                    <i class="material-icons">refresh</i> Retry
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            `;
            $(selector).html(errorHtml);
        }


    </script>
}

@section Styles {
    <style>
        /* Material Dashboard Pro Navigation Pills */
        .nav-pills .nav-link {
            border-radius: 30px;
            margin-right: 10px;
            padding: 10px 20px;
            color: #999;
            background: transparent;
            border: 1px solid transparent;
        }

        .nav-pills .nav-link.active {
            background: linear-gradient(60deg, #e91e63, #ad1457);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(233, 30, 99, 0.4);
            color: #fff;
        }

        .nav-pills .nav-link:hover {
            background: rgba(233, 30, 99, 0.1);
            color: #e91e63;
        }

        /* Performance Dashboard Cards */
        .card-stats {
            min-height: 140px;
        }

        .card-stats .card-title {
            font-size: 1.8em;
            font-weight: 300;
            margin: 0;
            line-height: 1.2;
        }

        .card-stats .card-category {
            font-size: 0.9em;
            margin-bottom: 5px;
            color: #999;
            font-weight: 400;
        }

        .card-stats .card-footer .stats {
            font-size: 0.8em;
            color: #999;
        }

        /* Ensure consistent card heights */
        .row .card-stats {
            height: 100%;
        }

        .row .col-lg-3,
        .row .col-md-6 {
            margin-bottom: 20px;
        }

        /* Card Headers */
        .card-header-rose {
            background: linear-gradient(60deg, #e91e63, #ad1457);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(233, 30, 99, 0.4);
        }

        .card-header-danger {
            background: linear-gradient(60deg, #ef5350, #e53935);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(244, 67, 54, 0.4);
        }

        /* Dashboard Cards */
        .card-stats {
            transition: transform 0.2s ease-in-out;
            margin-bottom: 30px;
            min-height: 180px;
        }

        .card-stats:hover {
            transform: translateY(-2px);
        }

        .card-stats .card-header {
            border-radius: 3px;
            padding: 15px;
            margin: -20px 15px 0;
            position: relative;
            z-index: 2;
        }

        .card-stats .card-header.card-header-icon {
            text-align: right;
            padding: 15px;
        }

        .card-stats .card-title {
            margin: 0;
            color: #fff;
            font-weight: 300;
            font-size: 1.8em;
            line-height: 1.2em;
            margin-top: 10px;
            word-break: break-word;
        }

        .card-stats .card-category {
            color: #fff;
            margin: 0;
            font-size: 14px;
            margin-top: 0;
            padding-top: 10px;
            margin-bottom: 0;
            font-weight: 400;
        }

        .card-stats .card-icon {
            float: left;
            padding: 15px;
            margin-top: -20px;
            margin-right: 15px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            width: 70px;
            height: 70px;
            text-align: center;
        }

        .card-stats .card-icon i {
            font-size: 36px;
            line-height: 40px;
            color: #fff;
            width: 40px;
            height: 40px;
            text-align: center;
        }

        .card-stats .card-footer {
            padding: 0;
            line-height: 30px;
            border-top: none;
            background: transparent;
        }

        .card-stats .card-footer .stats {
            color: #999999;
            font-size: 12px;
            line-height: 22px;
            padding: 15px;
        }

        .card-stats .card-footer .stats i {
            font-size: 16px;
            margin-right: 5px;
            position: relative;
            top: 3px;
        }

        /* Table Styling */
        .table th {
            border-top: none;
            font-weight: 500;
            font-size: 14px;
        }

        .table td {
            font-size: 14px;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* Color Classes */
        .text-danger {
            color: #f44336 !important;
        }

        .text-success {
            color: #4caf50 !important;
        }

        /* Responsive adjustments */
        @@media (max-width: 768px) {
            .card-stats .card-title {
                font-size: 1.4em;
                line-height: 1.1em;
            }

            .nav-pills .nav-link {
                margin-bottom: 5px;
                margin-right: 5px;
                padding: 8px 15px;
            }

            .card-stats {
                margin-bottom: 20px;
                min-height: 160px;
            }
        }

        @@media (max-width: 576px) {
            .card-stats .card-title {
                font-size: 1.2em;
                line-height: 1.1em;
            }

            .card-stats {
                min-height: 140px;
            }
        }
    </style>
}
