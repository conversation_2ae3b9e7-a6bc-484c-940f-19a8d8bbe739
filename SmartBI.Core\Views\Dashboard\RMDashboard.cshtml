@model SmartBI.Core.Models.DashboardModels.PerformanceData
@{
    ViewData["Title"] = "RM Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Styles {
    <link rel="stylesheet" href="~/css/material-dashboard.min.css">
    <link rel="stylesheet" href="~/css/site.css">
    <style>
        /* Card Styles */
        .card {
            margin-bottom: 30px;
            border-radius: 6px;
            color: rgba(0,0,0,.87);
            background: #fff;
            box-shadow: 0 2px 2px 0 rgba(0,0,0,.14), 0 3px 1px -2px rgba(0,0,0,.2), 0 1px 5px 0 rgba(0,0,0,.12);
        }

        .card-header {
            padding: 15px;
            margin: -20px 15px 0;
            border-radius: 3px;
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(233,30,99,.4);
        }

        .card-header.card-header-rose {
            background: linear-gradient(60deg,#ec407a,#d81b60);
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(233,30,99,.4);
        }

        .card-header.card-header-warning {
            background: linear-gradient(60deg,#ffa726,#fb8c00);
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(255,152,0,.4);
        }

        .card-header.card-header-info {
            background: linear-gradient(60deg,#26c6da,#00acc1);
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(0,188,212,.4);
        }

        .card-header.card-header-success {
            background: linear-gradient(60deg,#66bb6a,#43a047);
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(76,175,80,.4);
        }

        .card-title {
            color: #fff;
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 1.825em;
            font-weight: 300;
        }

        .card-category {
            color: rgba(255,255,255,.8);
            margin: 0;
            font-size: 14px;
        }

        /* Stats Cards */
        .card-stats .card-header.card-header-icon {
            text-align: right;
            padding: 15px;
        }

        .card-stats .card-header.card-header-icon i {
            font-size: 36px;
            line-height: 56px;
            width: 56px;
            height: 56px;
            text-align: center;
        }

        .card-stats .card-header .card-category {
            margin-top: 10px;
            margin-bottom: 0;
            color: #999;
        }

        .card-stats .card-header .card-title {
            margin: 0;
            color: #3c4858;
            font-size: 1.825em;
            font-weight: 300;
            line-height: 1.4em;
        }

        .card-stats .card-footer {
            border-top: 1px solid #eee;
            margin-top: 20px;
            padding: 10px 0;
        }

        .card-stats .card-footer .stats {
            color: #999;
            font-size: 12px;
            line-height: 22px;
        }

        .card-stats .card-footer .stats i {
            position: relative;
            top: 4px;
            font-size: 16px;
        }

        /* Utility Classes */
        .text-warning { color: #ff9800 !important; }
        .text-info { color: #00bcd4 !important; }
        .text-success { color: #4caf50 !important; }
        .text-rose { color: #e91e63 !important; }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
        }
        .nav-pills .nav-link.active {
            background: linear-gradient(60deg,#ec407a,#d81b60);
        }
        .progress {
            height: 4px;
            border-radius: 0;
            margin: 10px 0;
        }
    </style>
}

<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-rose">
                        <h4 class="card-title">RM Dashboard</h4>
                        <p class="card-category">Comprehensive Performance Analytics</p>
                    </div>
                    <div class="card-body">
                        <!-- Alert Messages -->
                        <div id="errorAlert" class="alert alert-danger" style="display: none;">
                            <span id="errorMessage"></span>
                        </div>
                        <div id="successAlert" class="alert alert-success" style="display: none;">
                            <span id="successMessage"></span>
                        </div>

                        <!-- Navigation Pills -->
                        <ul class="nav nav-pills nav-pills-rose" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="pill" href="#performance" role="tab" onclick="loadTab('performance')">
                                    <i class="material-icons">dashboard</i> Performance Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="pill" href="#attrition" role="tab" onclick="loadTab('attrition')">
                                    <i class="material-icons">trending_up</i> Performance & Attrition
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="pill" href="#targets" role="tab" onclick="loadTab('targets')">
                                    <i class="material-icons">track_changes</i> Target vs Achievement
                                </a>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content">
                            <!-- Performance Dashboard Tab -->
                            <div class="tab-pane fade show active" id="performance" role="tabpanel">
                                <div class="loading-spinner" id="performanceLoader">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                                <div id="performanceContent">
                                    <div class="row">
                                        <!-- Total Stats Row -->
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-rose card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">account_balance_wallet</i>
                                                    </div>
                                                    <p class="card-category">Total Accounts</p>
                                                    <h3 class="card-title" id="totalAccounts">@Model.TOTAL_AC.ToString("N0")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-rose">info</i>
                                                        All account types combined
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-success card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">account_balance</i>
                                                    </div>
                                                    <p class="card-category">Total Balances</p>
                                                    <h3 class="card-title" id="totalBalances">BDT @Model.TOTAL_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-success">info</i>
                                                        Combined balance amount
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-info card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">trending_up</i>
                                                    </div>
                                                    <p class="card-category">Average Balances</p>
                                                    <h3 class="card-title" id="avgBalances">BDT @Model.AVG_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-info">info</i>
                                                        Per account average
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-warning card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">attach_money</i>
                                                    </div>
                                                    <p class="card-category">Cost of Deposit</p>
                                                    <h3 class="card-title" id="cod">@Model.COD.ToString("N2")%</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-warning">info</i>
                                                        Current COD rate
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Current Account Stats Row -->
                                    <div class="row">
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-warning card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">account_circle</i>
                                                    </div>
                                                    <p class="card-category">Total Current Accounts</p>
                                                    <h3 class="card-title" id="currentAccounts">@Model.CA_AC.ToString("N0")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-warning">info</i>
                                                        Current accounts count
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-warning card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">account_balance</i>
                                                    </div>
                                                    <p class="card-category">Total Current Balances</p>
                                                    <h3 class="card-title" id="currentBalances">BDT @Model.CA_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-warning">info</i>
                                                        Current accounts balance
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-warning card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">trending_up</i>
                                                    </div>
                                                    <p class="card-category">Current Avg Balances</p>
                                                    <h3 class="card-title" id="currentAvgBalances">BDT @Model.CA_AVG_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-warning">info</i>
                                                        Average current balance
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-warning card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">hourglass_empty</i>
                                                    </div>
                                                    <p class="card-category">Total Dormant Accounts</p>
                                                    <h3 class="card-title" id="dormantAccounts">@Model.NO_OF_DORMANT_ACCOUNTS.ToString("N0")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-warning">info</i>
                                                        Dormant accounts count
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- Savings Account Stats Row -->
                                    <div class="row">
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-info card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">savings</i>
                                                    </div>
                                                    <p class="card-category">Total Savings Accounts</p>
                                                    <h3 class="card-title" id="savingsAccounts">@Model.SA_AC.ToString("N0")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-info">info</i>
                                                        Savings accounts count
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-info card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">account_balance</i>
                                                    </div>
                                                    <p class="card-category">Total Savings Balances</p>
                                                    <h3 class="card-title" id="savingsBalances">BDT @Model.SA_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-info">info</i>
                                                        Savings accounts balance
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-info card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">trending_up</i>
                                                    </div>
                                                    <p class="card-category">Savings Avg Balances</p>
                                                    <h3 class="card-title" id="savingsAvgBalances">BDT @Model.SA_AVG_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-info">info</i>
                                                        Average savings balance
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-info card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">hourglass_empty</i>
                                                    </div>
                                                    <p class="card-category">Total Dormant Balances</p>
                                                    <h3 class="card-title" id="dormantBalances">BDT @Model.BALANCE_OF_DORMANT_ACCOUNTS.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-info">info</i>
                                                        Dormant accounts balance
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- FDR Account Stats Row -->
                                    <div class="row">
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-success card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">account_balance</i>
                                                    </div>
                                                    <p class="card-category">Total FDR Accounts</p>
                                                    <h3 class="card-title" id="fdrAccounts">@Model.FDR_AC.ToString("N0")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-success">info</i>
                                                        FDR accounts count
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-success card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">account_balance</i>
                                                    </div>
                                                    <p class="card-category">Total FDR Balances</p>
                                                    <h3 class="card-title" id="fdrBalances">BDT @Model.FDR_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-success">info</i>
                                                        FDR accounts balance
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-success card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">trending_up</i>
                                                    </div>
                                                    <p class="card-category">FDR Avg Balances</p>
                                                    <h3 class="card-title" id="fdrAvgBalances">BDT @Model.FDR_AVG_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-success">info</i>
                                                        Average FDR balance
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-success card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">hourglass_empty</i>
                                                    </div>
                                                    <p class="card-category">Total Zero Balance Accounts</p>
                                                    <h3 class="card-title" id="zeroBalanceAccounts">@Model.NO_OF_ZERO_ACCOUNTS.ToString("N0")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-success">info</i>
                                                        Zero balance accounts
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>

                                    <!-- DPS Account Stats Row -->
                                    <div class="row">
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-rose card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">savings</i>
                                                    </div>
                                                    <p class="card-category">Total DPS Accounts</p>
                                                    <h3 class="card-title" id="dpsAccounts">@Model.DPS_AC.ToString("N0")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-rose">info</i>
                                                        DPS accounts count
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-rose card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">account_balance</i>
                                                    </div>
                                                    <p class="card-category">Total DPS Balances</p>
                                                    <h3 class="card-title" id="dpsBalances">BDT @Model.DPS_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-rose">info</i>
                                                        DPS accounts balance
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-rose card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">trending_up</i>
                                                    </div>
                                                    <p class="card-category">DPS Avg Balances</p>
                                                    <h3 class="card-title" id="dpsAvgBalances">BDT @Model.DPS_AVG_BAL.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-rose">info</i>
                                                        Average DPS balance
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        <div class="col-lg-3 col-md-6 col-sm-6">
                                            <div class="card card-stats">
                                                <div class="card-header card-header-rose card-header-icon">
                                                    <div class="card-icon">
                                                        <i class="material-icons">money_off</i>
                                                    </div>
                                                    <p class="card-category">Total balances of Zero Accounts</p>
                                                    <h3 class="card-title" id="zeroBalances">BDT @Model.BALANCE_OF_ZERO_ACCOUNTS.ToString("N2")</h3>
                                                </div>
                                                <div class="card-footer">
                                                    <div class="stats">
                                                        <i class="material-icons text-rose">info</i>
                                                        Zero balance total
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <!-- Attrition Analysis Tab -->
                            <div class="tab-pane fade" id="attrition" role="tabpanel">
                                <div class="loading-spinner" id="attritionLoader">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                                <div id="attritionContent">
                                    <!-- Attrition tables will be loaded here -->
                                </div>
                            </div>

                            <!-- Target Achievement Tab -->
                            <div class="tab-pane fade" id="targets" role="tabpanel">
                                <div class="loading-spinner" id="targetsLoader">
                                    <div class="spinner-border text-primary" role="status">
                                        <span class="sr-only">Loading...</span>
                                    </div>
                                </div>
                                <div id="targetsContent">
                                    <!-- Target table will be loaded here -->
                                </div>
                            </div>
                        </div>

                        <!-- Refresh Button -->
                        <div class="row mt-3">
                            <div class="col-12 text-center">
                                <button onclick="refreshDashboard()" class="btn btn-rose">
                                    <i class="material-icons">refresh</i> Refresh Data
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/material-dashboard.min.js"></script>
    <script>
        // Tab loading state
        const loadedTabs = {
            performance: false,
            attrition: false,
            targets: false
        };

        // Load tab content
        function loadTab(tabName) {
            if (loadedTabs[tabName]) return;

            const loader = document.getElementById(`${tabName}Loader`);
            const content = document.getElementById(`${tabName}Content`);

            if (loader) loader.style.display = 'block';
            if (content) content.style.display = 'none';

            let url;
            switch (tabName) {
                case 'performance':
                    url = '@Url.Action("PerformanceData", "Dashboard")';
                    break;
                case 'attrition':
                    url = '@Url.Action("PerformanceAttritionData", "Dashboard")';
                    break;
                case 'targets':
                    url = '@Url.Action("TargetAchievementData", "Dashboard")';
                    break;
            }

            fetch(url)
                .then(response => {
                    if (!response.ok) {
                        throw new Error(`HTTP error! status: ${response.status}`);
                    }
                    return response.text();
                })
                .then(html => {
                    if (content) {
                        content.innerHTML = html;
                        content.style.display = 'block';
                    }
                    loadedTabs[tabName] = true;
                })
                .catch(error => {
                    console.error('Error loading tab:', error);
                    const errorDiv = document.getElementById('errorAlert');
                    if (errorDiv) {
                        errorDiv.style.display = 'block';
                        errorDiv.querySelector('#errorMessage').textContent = 
                            `Failed to load ${tabName} data: ${error.message}`;
                    }
                })
                .finally(() => {
                    if (loader) loader.style.display = 'none';
                });
        }

        // Initialize first tab
        document.addEventListener('DOMContentLoaded', function() {
            loadTab('performance');
        });

        // Refresh functionality
        function refreshDashboard() {
            // Reset loaded state
            Object.keys(loadedTabs).forEach(key => loadedTabs[key] = false);
            
            // Reload current tab
            const activeTab = document.querySelector('.nav-pills .nav-link.active');
            if (activeTab) {
                const tabId = activeTab.getAttribute('href').substring(1);
                loadTab(tabId);
            }
        }
    </script>
}