@{
    ViewData["Title"] = "RM Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<!-- Dashboard Content -->
<div class="content">
    <div class="container-fluid">

        <!-- Loading Indicator -->
        <div id="dashboardLoader" class="text-center py-5" style="display: none;">
            <div class="spinner-border text-primary" role="status">
                <span class="visually-hidden">Loading...</span>
            </div>
            <p class="mt-2">Loading dashboard data...</p>
        </div>

        <!-- Error Alert -->
        <div id="errorAlert" class="alert alert-danger alert-dismissible fade" role="alert" style="display: none;">
            <i class="material-icons">error</i>
            <span id="errorMessage">An error occurred while loading data.</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <!-- Success Alert -->
        <div id="successAlert" class="alert alert-success alert-dismissible fade" role="alert" style="display: none;">
            <i class="material-icons">check_circle</i>
            <span id="successMessage">Data loaded successfully.</span>
            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
        </div>

        <!-- Dashboard Card -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header card-header-rose">
                        <h4 class="card-title">RM Dashboard || <span class="fas fa-user" style="color:white;font-weight:800;font-size:large" id="rmNameDisplay"></span></h4>
                        <div class="text-end">
                            <button type="button" class="btn btn-white btn-sm" id="refreshDashboard">
                                <i class="material-icons">refresh</i> Refresh Data
                            </button>
                            <button type="button" class="btn btn-white btn-sm" id="clearCache">
                                <i class="material-icons">clear_all</i> Clear Cache
                            </button>
                        </div>
                    </div>
                    <div class="card-body">

                        <!-- Tab Navigation - Material Dashboard Pro Pills -->
                        <ul class="nav nav-pills nav-pills-rose" id="rmDashboardTabs" role="tablist">
                            <li class="nav-item" role="presentation">
                                <a class="nav-link active" id="performance-tab" data-toggle="pill"
                                   href="#performance" role="tab" aria-controls="performance" aria-selected="true">
                                    RM Dashboard
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="attrition-tab" data-toggle="pill"
                                   href="#attrition" role="tab" aria-controls="attrition" aria-selected="false">
                                    Performance & Attrition
                                </a>
                            </li>
                            <li class="nav-item" role="presentation">
                                <a class="nav-link" id="targets-tab" data-toggle="pill"
                                   href="#targets" role="tab" aria-controls="targets" aria-selected="false">
                                   Target VS Achievement
                                </a>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content mt-4" id="rmDashboardTabContent">

                            <!-- Performance Tab -->
                            <div class="tab-pane fade show active" id="performance" role="tabpanel" aria-labelledby="performance-tab">
                                <div id="performanceLoader" class="text-center py-4" style="display: none;">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                    <span class="ms-2">Loading performance data...</span>
                                </div>
                                <div id="performanceContent">
                                    <!-- Performance content will be loaded here -->
                                </div>
                            </div>

                            <!-- Performance & Attrition Tab -->
                            <div class="tab-pane fade" id="attrition" role="tabpanel" aria-labelledby="attrition-tab">
                                <div id="attritionLoader" class="text-center py-4" style="display: none;">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                    <span class="ms-2">Loading attrition data...</span>
                                </div>
                                <div id="attritionContent">
                                    <!-- Attrition content will be loaded here -->
                                </div>
                            </div>

                            <!-- Target VS Achievement Tab -->
                            <div class="tab-pane fade" id="targets" role="tabpanel" aria-labelledby="targets-tab">
                                <div id="targetsLoader" class="text-center py-4" style="display: none;">
                                    <div class="spinner-border spinner-border-sm text-primary" role="status"></div>
                                    <span class="ms-2">Loading target data...</span>
                                </div>
                                <div id="targetsContent">
                                    <!-- Targets content will be loaded here -->
                                </div>
                            </div>

                        </div>
                    </div>
                </div>
            </div>
        </div>

    </div>
</div>

@section Scripts {
    <script>
        // RM Dashboard JavaScript Implementation
        var RMDashboard = {
            config: {
                userId: '',
                baseUrl: '',
                enableCaching: true,
                autoRefresh: false,
                refreshInterval: 300000
            },

            cache: {
                performance: null,
                growth: null,
                attrition: null,
                targets: null
            },

            init: function(options) {
                this.config = Object.assign(this.config, options);
                this.bindEvents();
                this.loadActiveTab();

                if (this.config.autoRefresh) {
                    this.startAutoRefresh();
                }

                console.log('RM Dashboard initialized for user:', this.config.userId);
            },

            bindEvents: function() {
                var self = this;

                // Tab click events - Bootstrap 4 compatibility
                $('#rmDashboardTabs a[data-toggle="pill"]').on('shown.bs.tab', function(e) {
                    var target = $(e.target).attr('href');
                    console.log('Tab switched to:', target);
                    self.loadTabData(target);
                });

                // Also bind click events as fallback
                $('#rmDashboardTabs a[data-toggle="pill"]').on('click', function(e) {
                    e.preventDefault();
                    var target = $(this).attr('href');
                    console.log('Tab clicked:', target);

                    // Update active states
                    $('#rmDashboardTabs .nav-link').removeClass('active');
                    $(this).addClass('active');

                    // Update tab content
                    $('.tab-pane').removeClass('show active');
                    $(target).addClass('show active');

                    // Load data
                    self.loadTabData(target);
                });

                // Refresh button
                $('#refreshDashboard').on('click', function() {
                    self.refreshAllData();
                });

                // Clear cache button
                $('#clearCache').on('click', function() {
                    self.clearCache();
                });
            },

            loadActiveTab: function() {
                var activeTab = $('#rmDashboardTabs .nav-link.active').attr('href');
                this.loadTabData(activeTab);
            },

            loadTabData: function(tabId) {
                console.log('Loading data for tab:', tabId);

                switch(tabId) {
                    case '#performance':
                        this.loadPerformanceData();
                        break;
                    case '#attrition':
                        this.loadAttritionData();
                        break;
                    case '#targets':
                        this.loadTargetsData();
                        break;
                    default:
                        console.warn('Unknown tab ID:', tabId);
                        this.loadPerformanceData(); // Default to performance
                        break;
                }
            },

            loadPerformanceData: function() {
                var self = this;

                if (this.cache.performance && this.config.enableCaching) {
                    this.renderPerformanceData(this.cache.performance);
                    return;
                }

                this.showLoader('#performanceLoader');

                // Call SmartBI.Core Dashboard controller endpoint
                console.log('Making AJAX call to:', this.config.baseUrl + 'GetPerformanceData');
                $.ajax({
                    url: this.config.baseUrl + 'GetPerformanceData',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 30000, // 30 seconds for testing
                    success: function(response) {
                        self.hideLoader('#performanceLoader');

                        console.log('Performance API Response:', response);
                        console.log('Response success:', response ? response.success : 'response is null');
                        console.log('Response data:', response ? response.data : 'response is null');

                        if (response && response.success) {
                            console.log('✅ Performance data received successfully:', response.data);
                            self.cache.performance = response.data;
                            self.renderPerformanceData(response.data);

                            // Update RM name in header
                            if (response.data && response.data.RM_NAME) {
                                $('#rmNameDisplay').text(response.data.RM_NAME + '||');
                            }

                            // Show cache indicator if data was cached
                            if (response.cached) {
                                self.showCacheIndicator(true);
                            }
                        } else {
                            console.error('API Error - Performance Response:', response);
                            console.error('Response success property:', response ? response.success : 'response is null/undefined');
                            console.error('Response data property:', response ? response.data : 'response is null/undefined');
                            self.showError('Failed to load performance data: ' + (response.error || 'Unknown error'));
                            self.loadSamplePerformanceData();
                        }
                    },
                    error: function(xhr, status, error) {
                        self.hideLoader('#performanceLoader');
                        console.error('Performance data error:', xhr, status, error);
                        console.error('XHR object:', xhr);
                        console.error('Response text:', xhr.responseText);

                        self.showError('API connection failed: ' + error);
                        self.loadSamplePerformanceData();
                    }
                });
            },

            loadSamplePerformanceData: function() {
                // Show sample data for demonstration when API fails
                var sampleData = {
                    RM_NAME: 'Sample RM',
                    TOTAL_AC: 191,
                    TOTAL_BAL: *********.58,
                    AVG_BAL: 632789.55,
                    COD: 6.61,
                    CA_AC: 25,
                    CA_BAL: *********.09,
                    CA_AVG_BAL: 8369949.56,
                    NO_OF_DORMANT_ACCOUNTS: 4,
                    SA_AC: 54,
                    SA_BAL: *********.29,
                    SA_AVG_BAL: 2582391.86,
                    BALANCE_OF_DORMANT_ACCOUNTS: 113346.00,
                    FDR_AC: 12,
                    FDR_BAL: ********.00,
                    FDR_AVG_BAL: 4166666.67,
                    DPS_AC: 8,
                    DPS_BAL: ********.00,
                    DPS_AVG_BAL: 3125000.00,
                    NO_OF_ZERO_ACCOUNTS: 3,
                    BALANCE_OF_ZERO_ACCOUNTS: 0.00
                };

                this.cache.performance = sampleData;
                this.renderPerformanceData(sampleData);
            },

            loadAttritionData: function() {
                var self = this;

                if (this.cache.growth && this.cache.attrition && this.config.enableCaching) {
                    this.renderAttritionData({ growth: this.cache.growth, attrition: this.cache.attrition });
                    return;
                }

                this.showLoader('#attritionLoader');

                console.log('Loading attrition data from APIs...');
                console.log('Base URL:', this.config.baseUrl);
                console.log('Growth URL:', this.config.baseUrl + 'GetRMGrowthData');
                console.log('Attrition URL:', this.config.baseUrl + 'GetPerformanceAttritionData');

                // Load both RM Growth and RM Attrition data from SmartBI.Core controller endpoints
                console.log('Making AJAX calls to:');
                console.log('- Growth:', this.config.baseUrl + 'GetRMGrowthData');
                console.log('- Attrition:', this.config.baseUrl + 'GetPerformanceAttritionData');
                Promise.all([
                    $.ajax({
                        url: this.config.baseUrl + 'GetRMGrowthData',
                        type: 'GET',
                        dataType: 'json',
                        timeout: 30000 // 30 seconds for testing
                    }),
                    $.ajax({
                        url: this.config.baseUrl + 'GetPerformanceAttritionData',
                        type: 'GET',
                        dataType: 'json',
                        timeout: 30000 // 30 seconds for testing
                    })
                ]).then(function(results) {
                    self.hideLoader('#attritionLoader');

                    var growthResponse = results[0];
                    var attritionResponse = results[1];

                    console.log('Growth Response:', growthResponse);
                    console.log('Attrition Response:', attritionResponse);

                    if (growthResponse && growthResponse.success && attritionResponse && attritionResponse.success) {
                        console.log('Both API calls successful!');
                        console.log('Growth data received:', growthResponse.data);
                        console.log('Attrition data received:', attritionResponse.data);

                        self.cache.growth = growthResponse.data;
                        self.cache.attrition = attritionResponse.data;

                        self.renderAttritionData({ growth: growthResponse.data, attrition: attritionResponse.data });

                        // Show cache indicator if any data was cached
                        if (growthResponse.cached || attritionResponse.cached) {
                            self.showCacheIndicator(true);
                        }
                    } else {
                        var errorMsg = (growthResponse && growthResponse.error) || (attritionResponse && attritionResponse.error) || 'Unknown error';
                        console.error('API Error - Growth Response:', growthResponse);
                        console.error('Growth Response success property:', growthResponse ? growthResponse.success : 'growthResponse is null/undefined');
                        console.error('Growth Response data property:', growthResponse ? growthResponse.data : 'growthResponse is null/undefined');
                        console.error('API Error - Attrition Response:', attritionResponse);
                        console.error('Attrition Response success property:', attritionResponse ? attritionResponse.success : 'attritionResponse is null/undefined');
                        console.error('Attrition Response data property:', attritionResponse ? attritionResponse.data : 'attritionResponse is null/undefined');
                        console.log('API Error, loading sample data:', errorMsg);
                        self.showError('Failed to load attrition data: ' + errorMsg);
                        self.loadSampleAttritionData();
                    }
                }).catch(function(error) {
                    self.hideLoader('#attritionLoader');
                    console.error('Attrition data error:', error);
                    console.error('Error details:', error.statusText, error.message, error.responseText);
                    console.log('API Connection failed, loading sample data');

                    self.showError('API connection failed: ' + (error.statusText || error.message || error));
                    self.loadSampleAttritionData();
                });
            },

            loadSampleAttritionData: function() {
                // Show sample data for demonstration when API fails
                console.log('Loading sample attrition data...');

                var sampleGrowthData = [
                    { GROWTH_TYPE: 'New Customer', PRODUCT_TYPE: 'Current Account', NO_OF_ACCOUNS: 15, TOTAL_BALANCES: 5000000.00 },
                    { GROWTH_TYPE: 'New Customer', PRODUCT_TYPE: 'Savings Account', NO_OF_ACCOUNS: 25, TOTAL_BALANCES: 8000000.00 },
                    { GROWTH_TYPE: 'New Customer', PRODUCT_TYPE: 'FDR', NO_OF_ACCOUNS: 8, TOTAL_BALANCES: ********.00 },
                    { GROWTH_TYPE: 'Existing Customer', PRODUCT_TYPE: 'Current Account', NO_OF_ACCOUNS: 12, TOTAL_BALANCES: 3500000.00 },
                    { GROWTH_TYPE: 'Existing Customer', PRODUCT_TYPE: 'Savings Account', NO_OF_ACCOUNS: 18, TOTAL_BALANCES: 6200000.00 },
                    { GROWTH_TYPE: 'Existing Customer', PRODUCT_TYPE: 'DPS', NO_OF_ACCOUNS: 5, TOTAL_BALANCES: 2800000.00 }
                ];

                var sampleAttritionData = [
                    { PRODUCT_TYPE: 'Current Account', ATTRITION: -2.5, GROWTH: 5.2, NET_GROWTH: 2.7 },
                    { PRODUCT_TYPE: 'Savings Account', ATTRITION: -1.8, GROWTH: 4.1, NET_GROWTH: 2.3 },
                    { PRODUCT_TYPE: 'FDR', ATTRITION: -0.5, GROWTH: 3.8, NET_GROWTH: 3.3 },
                    { PRODUCT_TYPE: 'DPS', ATTRITION: -1.2, GROWTH: 2.9, NET_GROWTH: 1.7 }
                ];

                console.log('Sample Growth Data:', sampleGrowthData);
                console.log('Sample Attrition Data:', sampleAttritionData);

                this.cache.growth = sampleGrowthData;
                this.cache.attrition = sampleAttritionData;

                this.renderAttritionData({ growth: sampleGrowthData, attrition: sampleAttritionData });
            },

            loadTargetsData: function() {
                var self = this;

                if (this.cache.targets && this.config.enableCaching) {
                    this.renderTargetsData(this.cache.targets);
                    return;
                }

                this.showLoader('#targetsLoader');

                console.log('Loading targets data from API...');
                console.log('Targets URL:', this.config.baseUrl + 'GetTargetAchievementData');

                // Call SmartBI.Core Dashboard controller endpoint
                console.log('Making AJAX call to:', this.config.baseUrl + 'GetTargetAchievementData');
                $.ajax({
                    url: this.config.baseUrl + 'GetTargetAchievementData',
                    type: 'GET',
                    dataType: 'json',
                    timeout: 30000, // 30 seconds for testing
                    success: function(response) {
                        self.hideLoader('#targetsLoader');
                        console.log('Targets Response:', response);

                        if (response && response.success) {
                            console.log('Targets API call successful!');
                            console.log('Targets data received:', response.data);

                            self.cache.targets = response.data;
                            self.renderTargetsData(response.data);

                            // Show cache indicator if data was cached
                            if (response.cached) {
                                self.showCacheIndicator(true);
                            }
                        } else {
                            var errorMsg = (response && response.error) || 'Unknown error';
                            console.error('API Error - Targets Response:', response);
                            console.error('Targets Response success property:', response ? response.success : 'response is null/undefined');
                            console.error('Targets Response data property:', response ? response.data : 'response is null/undefined');
                            console.log('API Error, loading sample targets data:', errorMsg);
                            self.showError('Failed to load target data: ' + errorMsg);
                            self.loadSampleTargetsData();
                        }
                    },
                    error: function(xhr, status, error) {
                        self.hideLoader('#targetsLoader');
                        console.error('Target data error:', xhr, status, error);
                        console.error('XHR object:', xhr);
                        console.error('Response text:', xhr.responseText);
                        console.log('API Connection failed, loading sample targets data');

                        self.showError('API connection failed: ' + (error || status));
                        self.loadSampleTargetsData();
                    }
                });
            },

            loadSampleTargetsData: function() {
                // Show sample data for demonstration when API fails
                var sampleTargetData = {
                    MONTHLY_TARGET_ACCOUNTS: 50,
                    YEARLY_TARGET_ACCOUNTS: 600,
                    MONTHLY_TARGET_AMOUNTS: ********.00,
                    YEARLY_TARGET_AMOUNTS: *********.00,
                    MTD_ACHIEVED_ACCOUNTS: 42,
                    YTD_ACHIEVED_ACCOUNTS: 485,
                    MTD_ACHIEVED_AMOUNTS: 8750000.00,
                    YTD_ACHIEVED_AMOUNTS: ********.00,
                    MTD_VARIANCE_ACCOUNTS: -8,
                    YTD_VARIANCE_ACCOUNTS: -115,
                    MTD_VARIANCE_AMOUNS: -1250000.00,
                    YTD_VARIANCE_AMOUNTS: -********.00,
                    ACC_PERFORMANCE: 84.00,
                    VOL_PERFORMANCE: 87.50
                };

                this.cache.targets = sampleTargetData;
                this.renderTargetsData(sampleTargetData);
            },

            renderPerformanceData: function(data) {
                var html = this.buildPerformanceHTML(data);
                $('#performanceContent').html(html);
            },

            renderAttritionData: function(data) {
                console.log('renderAttritionData called with:', data);
                var html = this.buildAttritionHTML(data);
                console.log('Generated HTML:', html);
                $('#attritionContent').html(html);

                // Initialize DataTables after rendering
                setTimeout(function() {
                    console.log('Initializing DataTables...');

                    if ($.fn.DataTable.isDataTable('#tblRMPerformance')) {
                        $('#tblRMPerformance').DataTable().destroy();
                    }
                    if ($.fn.DataTable.isDataTable('#tblRMAttrition')) {
                        $('#tblRMAttrition').DataTable().destroy();
                    }

                    $('#tblRMPerformance').DataTable({
                        "paging": true,
                        "lengthChange": false,
                        "searching": true,
                        "ordering": true,
                        "aaSorting": [],
                        "info": true,
                        "autoWidth": false,
                        "responsive": true,
                    });

                    $('#tblRMAttrition').DataTable({
                        "paging": true,
                        "lengthChange": false,
                        "searching": true,
                        "ordering": true,
                        "aaSorting": [],
                        "info": true,
                        "autoWidth": false,
                        "responsive": true,
                    });

                    console.log('DataTables initialized successfully');
                }, 100);
            },

            renderTargetsData: function(data) {
                var html = this.buildTargetsHTML(data);
                $('#targetsContent').html(html);

                // Initialize DataTable after rendering
                setTimeout(function() {
                    if ($.fn.DataTable.isDataTable('#RMTargetAchievement')) {
                        $('#RMTargetAchievement').DataTable().destroy();
                    }

                    $('#RMTargetAchievement').DataTable({
                        "paging": true,
                        "lengthChange": true,
                        lengthMenu: [
                            [05, 10, 15, -1],
                            [05, 10, 15, 'All'],
                        ],
                        "searching": true,
                        "ordering": false,
                        "aaSorting": [],
                        "info": true,
                        "autoWidth": false,
                        "responsive": true,
                    });
                }, 100);
            },

            buildPerformanceHTML: function(data) {
                if (!data) {
                    return '<div class="alert alert-info">No performance data available.</div>';
                }

                console.log('🔍 buildPerformanceHTML called with data:', data);

                // Test formatting functions first
                console.log('🧪 Testing formatNumber(191):', this.formatNumber(191));
                console.log('🧪 Testing formatCurrency(*********.58):', this.formatCurrency(*********.58));

                // Log raw data values
                console.log('📈 Raw data values:');
                console.log('  TOTAL_AC:', data.TOTAL_AC, 'type:', typeof data.TOTAL_AC);
                console.log('  TOTAL_BAL:', data.TOTAL_BAL, 'type:', typeof data.TOTAL_BAL);
                console.log('  AVG_BAL:', data.AVG_BAL, 'type:', typeof data.AVG_BAL);
                console.log('  COD:', data.COD, 'type:', typeof data.COD);

                // TEMPORARY: Use hardcoded test values to isolate the issue
                var testData = {
                    TOTAL_AC: 191,
                    TOTAL_BAL: *********.58,
                    AVG_BAL: 632789.55,
                    COD: 6.61,
                    CA_AC: 25,
                    CA_BAL: *********.09,
                    CA_AVG_BAL: 8369949.56,
                    NO_OF_DORMANT_ACCOUNTS: 4,
                    SA_AC: 54,
                    SA_BAL: *********.29,
                    SA_AVG_BAL: 2582391.86,
                    BALANCE_OF_DORMANT_ACCOUNTS: 113346.00,
                    FDR_AC: 12,
                    FDR_BAL: ********.00,
                    FDR_AVG_BAL: 4166666.67,
                    DPS_AC: 8,
                    DPS_BAL: ********.00,
                    DPS_AVG_BAL: 3125000.00,
                    NO_OF_ZERO_ACCOUNTS: 3,
                    BALANCE_OF_ZERO_ACCOUNTS: 0.00
                };

                console.log('🧪 Using test data instead of API data for debugging');

                // Pre-format all values to avoid context issues in template literals
                var formattedData = {
                    totalAc: this.formatNumber(testData.TOTAL_AC || 0),
                    totalBal: this.formatCurrency(testData.TOTAL_BAL || 0),
                    avgBal: this.formatCurrency(testData.AVG_BAL || 0),
                    cod: this.formatCurrency(testData.COD || 0),
                    caAc: this.formatNumber(testData.CA_AC || 0),
                    caBal: this.formatCurrency(testData.CA_BAL || 0),
                    caAvgBal: this.formatCurrency(testData.CA_AVG_BAL || 0),
                    dormantAccounts: this.formatNumber(testData.NO_OF_DORMANT_ACCOUNTS || 0),
                    saAc: this.formatNumber(testData.SA_AC || 0),
                    saBal: this.formatCurrency(testData.SA_BAL || 0),
                    saAvgBal: this.formatCurrency(testData.SA_AVG_BAL || 0),
                    dormantBal: this.formatCurrency(testData.BALANCE_OF_DORMANT_ACCOUNTS || 0),
                    fdrAc: this.formatNumber(testData.FDR_AC || 0),
                    fdrBal: this.formatCurrency(testData.FDR_BAL || 0),
                    fdrAvgBal: this.formatCurrency(testData.FDR_AVG_BAL || 0),
                    dpsAc: this.formatNumber(testData.DPS_AC || 0),
                    dpsBal: this.formatCurrency(testData.DPS_BAL || 0),
                    dpsAvgBal: this.formatCurrency(testData.DPS_AVG_BAL || 0),
                    zeroAccounts: this.formatNumber(testData.NO_OF_ZERO_ACCOUNTS || 0),
                    zeroBal: this.formatCurrency(testData.BALANCE_OF_ZERO_ACCOUNTS || 0)
                };

                console.log('📊 Formatted data object:', formattedData);

                return `
                    <!-- Legacy RM Dashboard Performance Cards Layout -->
                    <div class="row">
                        <!-- Row 1: Total Accounts, Total Balances, Avg Balances, Cost of Deposit -->
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-danger card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_balance_wallet</i>
                                    </div>
                                    <p class="card-category">Total Accounts</p>
                                    <h3 class="card-title">${formattedData.totalAc}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-danger">info</i>
                                        Total account count
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-danger card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_balance</i>
                                    </div>
                                    <p class="card-category">Total Balances</p>
                                    <h3 class="card-title">${formattedData.totalBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-danger">info</i>
                                        Total balance amount
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-danger card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">trending_up</i>
                                    </div>
                                    <p class="card-category">Avg Balances</p>
                                    <h3 class="card-title">${formattedData.avgBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-danger">info</i>
                                        Average balance per account
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-danger card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">monetization_on</i>
                                    </div>
                                    <p class="card-category">Cost of Deposit</p>
                                    <h3 class="card-title">${formattedData.cod}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-danger">info</i>
                                        Cost of deposit rate
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 2: Current Account Details -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-warning card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_circle</i>
                                    </div>
                                    <p class="card-category">Total Current Accounts</p>
                                    <h3 class="card-title">${formattedData.caAc}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-warning">info</i>
                                        Current account count
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-warning card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_balance</i>
                                    </div>
                                    <p class="card-category">Total Current Balances</p>
                                    <h3 class="card-title">${formattedData.caBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-warning">info</i>
                                        Current account balances
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-warning card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">trending_up</i>
                                    </div>
                                    <p class="card-category">Avg Current Balances</p>
                                    <h3 class="card-title">${formattedData.caAvgBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-warning">info</i>
                                        Average current balance
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-warning card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">people</i>
                                    </div>
                                    <p class="card-category">Total Dormant Accounts</p>
                                    <h3 class="card-title">${formattedData.dormantAccounts}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-warning">info</i>
                                        Dormant account count
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 3: Savings Account Details -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-info card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">savings</i>
                                    </div>
                                    <p class="card-category">Total Savings Accounts</p>
                                    <h3 class="card-title">${formattedData.saAc}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-info">info</i>
                                        Savings account count
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-info card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_balance</i>
                                    </div>
                                    <p class="card-category">Total Savings Balances</p>
                                    <h3 class="card-title">${formattedData.saBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-info">info</i>
                                        Savings account balances
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-info card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">trending_up</i>
                                    </div>
                                    <p class="card-category">Savings Avg Balances</p>
                                    <h3 class="card-title">${formattedData.saAvgBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-info">info</i>
                                        Average savings balance
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-info card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_balance_wallet</i>
                                    </div>
                                    <p class="card-category">Total Dormant Balances</p>
                                    <h3 class="card-title">${formattedData.dormantBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-info">info</i>
                                        Dormant account balances
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 4: FDR and DPS Details -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-success card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">receipt</i>
                                    </div>
                                    <p class="card-category">Total FDR Accounts</p>
                                    <h3 class="card-title">${formattedData.fdrAc}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-success">info</i>
                                        Fixed deposit accounts
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-success card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_balance</i>
                                    </div>
                                    <p class="card-category">Total FDR Balances</p>
                                    <h3 class="card-title">${formattedData.fdrBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-success">info</i>
                                        Fixed deposit balances
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-success card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">trending_up</i>
                                    </div>
                                    <p class="card-category">FDR Avg Balances</p>
                                    <h3 class="card-title">${formattedData.fdrAvgBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-success">info</i>
                                        Average FDR balance
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-success card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_circle</i>
                                    </div>
                                    <p class="card-category">Total DPS Accounts</p>
                                    <h3 class="card-title">${formattedData.dpsAc}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-success">info</i>
                                        DPS account count
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- Row 5: DPS and Zero Account Details -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-primary card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_balance</i>
                                    </div>
                                    <p class="card-category">Total DPS Balances</p>
                                    <h3 class="card-title">${formattedData.dpsBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-primary">info</i>
                                        DPS account balances
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-primary card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">trending_up</i>
                                    </div>
                                    <p class="card-category">DPS Avg Balances</p>
                                    <h3 class="card-title">${formattedData.dpsAvgBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-primary">info</i>
                                        Average DPS balance
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-primary card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">remove_circle</i>
                                    </div>
                                    <p class="card-category">Total Zero Accounts</p>
                                    <h3 class="card-title">${formattedData.zeroAccounts}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-primary">info</i>
                                        Zero balance accounts
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6 col-sm-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-primary card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">account_balance_wallet</i>
                                    </div>
                                    <p class="card-category">Zero Account Balances</p>
                                    <h3 class="card-title">${formattedData.zeroBal}</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-primary">info</i>
                                        Total zero balances
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            },

            buildAttritionHTML: function(data) {
                if (!data || !data.growth || !data.attrition) {
                    return '<div class="alert alert-info">No attrition data available.</div>';
                }

                var growthData = data.growth;
                var attritionData = data.attrition;

                return `
                    <div class="row">
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header card-header-rose">
                                    <h4 class="card-title">RM Performance Growth</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table id="tblRMPerformance" class="table table-striped table-bordered v_center dataTable no-footer" role="grid">
                                            <thead>
                                                <tr>
                                                    <th style="font-weight:800; font-size:large; text-align:center; border: 1px solid #dee2e6;">Growth Type</th>
                                                    <th style="font-weight: 800; font-size: large; text-align: center; border: 1px solid #dee2e6;">Product</th>
                                                    <th style="font-weight: 800; font-size: large; text-align: center; border: 1px solid #dee2e6;">Total Accounts</th>
                                                    <th style="font-weight: 800; font-size: large; text-align: center; border: 1px solid #dee2e6;">Total Balances</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${this.buildGrowthTableRows(growthData)}
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-md-6">
                            <div class="card">
                                <div class="card-header card-header-rose">
                                    <h4 class="card-title">RM Attrition Analysis</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table id="tblRMAttrition" class="table table-striped table-bordered v_center dataTable no-footer" role="grid">
                                            <thead>
                                                <tr>
                                                    <th style="font-weight: 800; font-size: large; text-align: center; border: 1px solid #dee2e6;">Product</th>
                                                    <th style="font-weight: 800; font-size: large; text-align: center; border: 1px solid #dee2e6;">Attrition</th>
                                                    <th style="font-weight: 800; font-size: large; text-align: center; border: 1px solid #dee2e6;">Growth</th>
                                                    <th style="font-weight: 800; font-size: large; text-align: center; border: 1px solid #dee2e6;">Net Growth</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                ${this.buildAttritionTableRows(attritionData)}
                                            </tbody>
                                            <tfoot>
                                                <tr>
                                                    <td style="text-align:center;font-weight:400">Grand Total</td>
                                                    <td style="text-align:right;font-weight:400">${this.calculateAttritionTotal(attritionData, 'ATTRITION')}</td>
                                                    <td style="text-align:right;font-weight:400">${this.calculateAttritionTotal(attritionData, 'GROWTH')}</td>
                                                    <td style="text-align:right;font-weight:400">${this.calculateAttritionTotal(attritionData, 'NET_GROWTH')}</td>
                                                </tr>
                                            </tfoot>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            },

            buildGrowthTableRows: function(growthData) {
                console.log('buildGrowthTableRows called with:', growthData);

                if (!growthData || !Array.isArray(growthData)) {
                    console.log('No growth data or not an array');
                    return '<tr><td colspan="4" class="text-center" style="border: 1px solid #dee2e6;">No growth data available</td></tr>';
                }

                var rows = growthData.map(function(item) {
                    console.log('Processing growth item:', item);

                    // Map API codes to display-friendly names
                    var growthTypeDisplay = this.mapGrowthType(item.GROWTH_TYPE);
                    var productTypeDisplay = this.mapProductType(item.PRODUCT_TYPE);

                    return `
                        <tr style="font-weight:400; font-size:large">
                            <td style="border: 1px solid #dee2e6; padding: 8px;">${growthTypeDisplay}</td>
                            <td style="border: 1px solid #dee2e6; padding: 8px;">${productTypeDisplay}</td>
                            <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; padding-right: 12px;">${(item.NO_OF_ACCOUNS || 0).toLocaleString()}</td>
                            <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; padding-right: 12px;">${(item.TOTAL_BALANCES || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                        </tr>
                    `;
                }.bind(this)).join('');

                console.log('Generated growth table rows:', rows);
                return rows;
            },

            mapGrowthType: function(code) {
                var mapping = {
                    'VTD': 'New Customer',
                    'STD': 'Existing Customer',
                    'New Customer': 'New Customer',
                    'Existing Customer': 'Existing Customer'
                };
                return mapping[code] || code || '';
            },

            mapProductType: function(code) {
                var mapping = {
                    'CA': 'Current Account',
                    'SA': 'Savings Account',
                    'FDR': 'Fixed Deposit Receipt',
                    'DPS': 'Deposit Pension Scheme',
                    'Current Account': 'Current Account',
                    'Savings Account': 'Savings Account',
                    'Fixed Deposit Receipt': 'Fixed Deposit Receipt',
                    'Deposit Pension Scheme': 'Deposit Pension Scheme'
                };
                return mapping[code] || code || '';
            },

            buildAttritionTableRows: function(attritionData) {
                if (!attritionData || !Array.isArray(attritionData)) {
                    return '<tr><td colspan="4" class="text-center" style="border: 1px solid #dee2e6;">No attrition data available</td></tr>';
                }

                return attritionData.map(function(item) {
                    var attritionClass = (item.ATTRITION || 0) < 0 ? 'text-danger' : 'text-success';
                    var growthClass = (item.GROWTH || 0) < 0 ? 'text-danger' : 'text-success';
                    var netGrowthClass = (item.NET_GROWTH || 0) < 0 ? 'text-danger' : 'text-success';

                    // Map product type codes to display names
                    var productTypeDisplay = this.mapProductType(item.PRODUCT_TYPE);

                    return `
                        <tr style="font-weight:400; font-size:large">
                            <td style="border: 1px solid #dee2e6; padding: 8px;">${productTypeDisplay}</td>
                            <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; padding-right: 12px;" class="${attritionClass}">${(item.ATTRITION || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                            <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; padding-right: 12px;" class="${growthClass}">${(item.GROWTH || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                            <td style="border: 1px solid #dee2e6; padding: 8px; text-align: right; padding-right: 12px;" class="${netGrowthClass}">${(item.NET_GROWTH || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                        </tr>
                    `;
                }.bind(this)).join('');
            },

            calculateAttritionTotal: function(attritionData, field) {
                if (!attritionData || !Array.isArray(attritionData)) {
                    return '0.00';
                }

                var total = attritionData.reduce(function(sum, item) {
                    return sum + (item[field] || 0);
                }, 0);

                return total.toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2});
            },

            buildTargetsHTML: function(data) {
                if (!data) {
                    return '<div class="alert alert-info">No target data available.</div>';
                }

                return `
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header card-header-rose">
                                    <h4 class="card-title">Target VS Achievement</h4>
                                </div>
                                <div class="card-body">
                                    <div class="table-responsive">
                                        <table id="RMTargetAchievement" class="table table-striped table-bordered v_center dataTable no-footer" role="grid">
                                            <thead>
                                                <tr>
                                                    <th colspan="4" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Target</th>
                                                    <th colspan="4" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Achievement</th>
                                                    <th colspan="4" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Variance</th>
                                                    <th colspan="2" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Overall Performance</th>
                                                </tr>
                                                <tr>
                                                    <th colspan="2" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Account</th>
                                                    <th colspan="2" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Volume</th>
                                                    <th colspan="2" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Account</th>
                                                    <th colspan="2" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Volume</th>
                                                    <th colspan="2" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Account</th>
                                                    <th colspan="2" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;">Volume</th>
                                                    <th colspan="2" style="text-align:center;font-weight:300; border: 1px solid #dee2e6;"></th>
                                                </tr>
                                                <tr>
                                                    <th style="border: 1px solid #dee2e6;">Monthly</th>
                                                    <th style="border: 1px solid #dee2e6;">Yearly</th>
                                                    <th style="border: 1px solid #dee2e6;">Monthly</th>
                                                    <th style="border: 1px solid #dee2e6;">Yearly</th>
                                                    <th style="border: 1px solid #dee2e6;">Monthly</th>
                                                    <th style="border: 1px solid #dee2e6;">Yearly</th>
                                                    <th style="border: 1px solid #dee2e6;">Monthly</th>
                                                    <th style="border: 1px solid #dee2e6;">Yearly</th>
                                                    <th style="border: 1px solid #dee2e6;">MTD</th>
                                                    <th style="border: 1px solid #dee2e6;">YTD</th>
                                                    <th style="border: 1px solid #dee2e6;">MTD</th>
                                                    <th style="border: 1px solid #dee2e6;">YTD</th>
                                                    <th style="border: 1px solid #dee2e6;">Account Growth</th>
                                                    <th style="border: 1px solid #dee2e6;">Volume Growth</th>
                                                </tr>
                                            </thead>
                                            <tbody>
                                                <tr>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.MONTHLY_TARGET_ACCOUNTS || 0).toLocaleString()}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.YEARLY_TARGET_ACCOUNTS || 0).toLocaleString()}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.MONTHLY_TARGET_AMOUNTS || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.YEARLY_TARGET_AMOUNTS || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.MTD_ACHIEVED_ACCOUNTS || 0).toLocaleString()}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.YTD_ACHIEVED_ACCOUNTS || 0).toLocaleString()}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.MTD_ACHIEVED_AMOUNTS || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.YTD_ACHIEVED_AMOUNTS || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.MTD_VARIANCE_ACCOUNTS || 0).toLocaleString()}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.YTD_VARIANCE_ACCOUNTS || 0).toLocaleString()}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.MTD_VARIANCE_AMOUNS || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.YTD_VARIANCE_AMOUNTS || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})}</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.ACC_PERFORMANCE || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})} %</td>
                                                    <td style="border: 1px solid #dee2e6; padding: 8px;">${(data.VOL_PERFORMANCE || 0).toLocaleString('en-US', {minimumFractionDigits: 2, maximumFractionDigits: 2})} %</td>
                                                </tr>
                                            </tbody>
                                        </table>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                `;
            },

            refreshAllData: function() {
                this.clearCache();
                this.loadActiveTab();
                this.showSuccess('Dashboard data refreshed successfully');
            },

            clearCache: function() {
                var self = this;

                // Clear local cache
                this.cache = { performance: null, growth: null, attrition: null, targets: null };

                // Clear server-side cache through controller endpoint
                $.ajax({
                    url: this.config.baseUrl + 'ClearRMCache',
                    type: 'POST',
                    dataType: 'json',
                    success: function(response) {
                        if (response.success) {
                            self.showSuccess('Cache cleared successfully');
                        } else {
                            self.showSuccess('Local cache cleared (server cache clear failed)');
                        }
                    },
                    error: function(xhr, status, error) {
                        console.warn('Failed to clear server cache:', error);
                        self.showSuccess('Local cache cleared (server cache clear failed)');
                    }
                });
            },

            showLoader: function(selector) {
                $(selector).show();
            },

            hideLoader: function(selector) {
                $(selector).hide();
            },

            showError: function(message) {
                $('#errorMessage').text(message);
                $('#errorAlert').addClass('show').show();
                setTimeout(function() {
                    $('#errorAlert').removeClass('show').hide();
                }, 5000);
            },

            showSuccess: function(message) {
                $('#successMessage').text(message);
                $('#successAlert').addClass('show').show();
                setTimeout(function() {
                    $('#successAlert').removeClass('show').hide();
                }, 3000);
            },

            showCacheIndicator: function(cached) {
                if (cached) {
                    console.log('Data loaded from cache');

                    // Show a subtle cache indicator
                    var indicator = '<small class="text-muted cache-indicator"><i class="material-icons" style="font-size: 14px;">cached</i> Cached data</small>';

                    // Add cache indicator to the dashboard header if not already present
                    if ($('.cache-indicator').length === 0) {
                        $('.card-title').append(' ' + indicator);

                        // Remove indicator after 3 seconds
                        setTimeout(function() {
                            $('.cache-indicator').fadeOut();
                        }, 3000);
                    }
                }
            },

            formatCurrency: function(value) {
                return new Intl.NumberFormat('en-US', {
                    style: 'currency',
                    currency: 'BDT',
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                }).format(value || 0);
            },

            formatNumber: function(value) {
                return new Intl.NumberFormat('en-US').format(value || 0);
            },

            formatPercentage: function(value) {
                return (value || 0).toFixed(2) + '%';
            },

            startAutoRefresh: function() {
                var self = this;
                setInterval(function() {
                    self.refreshAllData();
                }, this.config.refreshInterval);
            }
        };

        // Initialize RM Dashboard
        $(document).ready(function() {
            RMDashboard.init({
                userId: '@(User.Identity.Name ?? "admin")',
                baseUrl: '@Url.Action("", "Dashboard")/',
                microserviceBaseUrl: '@(ViewBag.MicroserviceBaseUrl ?? "https://localhost:7000")',
                apiTimeout: parseInt('@(ViewBag.ApiTimeout ?? "10")') * 60000, // Convert minutes to milliseconds
                enableFallbackMode: '@(ViewBag.EnableFallbackMode ?? "true")'.toLowerCase() === 'true',
                enableCaching: true,
                autoRefresh: false,
                refreshInterval: 300000 // 5 minutes
            });

            console.log('RM Dashboard initialized with configuration:', {
                microserviceBaseUrl: '@(ViewBag.MicroserviceBaseUrl ?? "https://localhost:7000")',
                apiTimeout: '@(ViewBag.ApiTimeout ?? "10") minutes',
                enableFallbackMode: '@(ViewBag.EnableFallbackMode ?? "true")'
            });

            // Test basic AJAX connectivity
            console.log('Testing basic AJAX connectivity...');
            $.ajax({
                url: '@Url.Action("TestConnection", "Dashboard")',
                type: 'GET',
                dataType: 'json',
                success: function(response) {
                    console.log('✅ AJAX Test Success:', response);
                },
                error: function(xhr, status, error) {
                    console.error('❌ AJAX Test Failed:', xhr, status, error);
                }
            });
        });
    </script>
}

@section Styles {
    <style>
        /* Material Dashboard Pro Navigation Pills */
        .nav-pills .nav-link {
            border-radius: 30px;
            margin-right: 10px;
            padding: 10px 20px;
            color: #999;
            background: transparent;
            border: 1px solid transparent;
        }

        .nav-pills .nav-link.active {
            background: linear-gradient(60deg, #e91e63, #ad1457);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(233, 30, 99, 0.4);
            color: #fff;
        }

        .nav-pills .nav-link:hover {
            background: rgba(233, 30, 99, 0.1);
            color: #e91e63;
        }

        /* Card Headers */
        .card-header-rose {
            background: linear-gradient(60deg, #e91e63, #ad1457);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(233, 30, 99, 0.4);
        }

        .card-header-danger {
            background: linear-gradient(60deg, #ef5350, #e53935);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(244, 67, 54, 0.4);
        }

        .card-header-warning {
            background: linear-gradient(60deg, #ff9800, #fb8c00);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(255, 152, 0, 0.4);
        }

        .card-header-info {
            background: linear-gradient(60deg, #26c6da, #00acc1);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(0, 188, 212, 0.4);
        }

        .card-header-success {
            background: linear-gradient(60deg, #66bb6a, #43a047);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(76, 175, 80, 0.4);
        }

        .card-header-primary {
            background: linear-gradient(60deg, #ab47bc, #8e24aa);
            box-shadow: 0 4px 20px 0px rgba(0, 0, 0, 0.14), 0 7px 10px -5px rgba(156, 39, 176, 0.4);
        }

        /* Dashboard Cards - Material Dashboard Pro Style */
        .card-stats {
            transition: transform 0.2s ease-in-out;
            margin-bottom: 30px;
            min-height: 200px;
        }

        .card-stats:hover {
            transform: translateY(-2px);
        }

        .card-stats .card-header {
            border-radius: 3px;
            padding: 15px;
            margin: -20px 15px 0;
            position: relative;
            z-index: 2;
        }

        .card-stats .card-header.card-header-icon {
            text-align: right;
            padding: 15px;
        }

        .card-stats .card-title {
            margin: 0;
            color: #fff;
            font-weight: 300;
            font-size: 2.5em;
            line-height: 1.4em;
            margin-top: 10px;
        }

        .card-stats .card-category {
            color: #fff;
            margin: 0;
            font-size: 14px;
            margin-top: 0;
            padding-top: 10px;
            margin-bottom: 0;
            font-weight: 400;
        }

        .card-stats .card-icon {
            float: left;
            padding: 15px;
            margin-top: -20px;
            margin-right: 15px;
            border-radius: 3px;
            background: rgba(255, 255, 255, 0.2);
            width: 70px;
            height: 70px;
            text-align: center;
        }

        .card-stats .card-icon i {
            font-size: 36px;
            line-height: 40px;
            color: #fff;
            width: 40px;
            height: 40px;
            text-align: center;
        }

        .card-stats .card-footer {
            padding: 0;
            line-height: 30px;
            border-top: none;
            background: transparent;
        }

        .card-stats .card-footer .stats {
            color: #999999;
            font-size: 12px;
            line-height: 22px;
            padding: 15px;
        }

        .card-stats .card-footer .stats i {
            font-size: 16px;
            margin-right: 5px;
            position: relative;
            top: 3px;
        }

        /* Table Styling */
        .table th {
            border-top: none;
            font-weight: 500;
        }

        .table-striped tbody tr:nth-of-type(odd) {
            background-color: rgba(0, 0, 0, 0.05);
        }

        /* DataTables Styling */
        .dataTables_wrapper .dataTables_paginate .paginate_button.current {
            background: linear-gradient(60deg, #e91e63, #ad1457) !important;
            border-color: #e91e63 !important;
            color: white !important;
        }

        .dataTables_wrapper .dataTables_paginate .paginate_button:hover {
            background: rgba(233, 30, 99, 0.1) !important;
            border-color: #e91e63 !important;
            color: #e91e63 !important;
        }

        /* Legacy Color Classes */
        .text-danger {
            color: #f44336 !important;
        }

        .text-success {
            color: #4caf50 !important;
        }

        /* Responsive adjustments */
        @@media (max-width: 768px) {
            .card-stats .card-title {
                font-size: 1.8em;
            }

            .nav-pills .nav-link {
                margin-bottom: 5px;
                margin-right: 5px;
                padding: 8px 15px;
            }

            .card-stats {
                margin-bottom: 20px;
                min-height: 180px;
            }

            .card-stats .card-header {
                margin: -15px 10px 0;
            }
        }

        /* Ensure consistent card heights */
        .row .card-stats {
            height: 100%;
        }

        .row [class*="col-"] {
            display: flex;
            align-items: stretch;
        }
    </style>
}
