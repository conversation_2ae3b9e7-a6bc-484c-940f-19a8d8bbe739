@model SmartBI.Data.ViewModels.RM_DASHBOARD_ITEMS
@{
    ViewData["Title"] = "RM Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

@section Styles {
    <link rel="stylesheet" href="~/css/material-dashboard.min.css">
    <link rel="stylesheet" href="~/css/site.css">
    <style>
        /* Card Styles */
        .card {
            margin-bottom: 30px;
            border-radius: 6px;
            color: rgba(0,0,0,.87);
            background: #fff;
            box-shadow: 0 2px 2px 0 rgba(0,0,0,.14), 0 3px 1px -2px rgba(0,0,0,.2), 0 1px 5px 0 rgba(0,0,0,.12);
        }

        .card-header {
            padding: 15px;
            margin: -20px 15px 0;
            border-radius: 3px;
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(233,30,99,.4);
        }

        .card-header.card-header-rose {
            background: linear-gradient(60deg,#ec407a,#d81b60);
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(233,30,99,.4);
        }

        .card-header.card-header-warning {
            background: linear-gradient(60deg,#ffa726,#fb8c00);
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(255,152,0,.4);
        }

        .card-header.card-header-info {
            background: linear-gradient(60deg,#26c6da,#00acc1);
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(0,188,212,.4);
        }

        .card-header.card-header-success {
            background: linear-gradient(60deg,#66bb6a,#43a047);
            box-shadow: 0 4px 20px 0 rgba(0,0,0,.14), 0 7px 10px -5px rgba(76,175,80,.4);
        }

        .card-title {
            color: #fff;
            margin-top: 0;
            margin-bottom: 5px;
            font-size: 15px; /* Set to exactly 15px as requested */
            font-weight: 300;
            line-height: 1.2;
            word-wrap: break-word;
        }

        .card-category {
            color: #fff; /* Changed to pure white as requested */
            margin: 0;
            font-size: 14px;
            font-weight: 500;
        }

        /* Stats Cards */
        .card-stats .card-header.card-header-icon {
            text-align: right;
            padding: 15px;
        }

        .card-stats .card-header.card-header-icon i {
            font-size: 36px;
            line-height: 56px;
            width: 56px;
            height: 56px;
            text-align: center;
        }

        .card-stats .card-header .card-category {
            margin-top: 10px;
            margin-bottom: 0;
            color: #fff; /* Changed to white as requested */
        }

        .card-stats .card-header .card-title {
            margin: 0;
            color: #3c4858;
            font-size: 15px; /* Set to exactly 15px as requested */
            font-weight: 300;
            line-height: 1.3em;
            word-wrap: break-word;
            overflow-wrap: break-word;
        }

        .card-stats .card-footer {
            border-top: 1px solid #eee;
            margin-top: 20px;
            padding: 10px 0;
        }

        .card-stats .card-footer .stats {
            color: #999;
            font-size: 12px;
            line-height: 22px;
        }

        .card-stats .card-footer .stats i {
            position: relative;
            top: 4px;
            font-size: 16px;
        }

        /* Utility Classes */
        .text-warning { color: #ff9800 !important; }
        .text-info { color: #00bcd4 !important; }
        .text-success { color: #4caf50 !important; }
        .text-rose { color: #e91e63 !important; }

        .loading-spinner {
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 200px;
        }
        .nav-pills .nav-link.active {
            background: linear-gradient(60deg,#ec407a,#d81b60);
        }
        .progress {
            height: 4px;
            border-radius: 0;
            margin: 10px 0;
        }

        /* Product Type Summary Cards - Light background */
        .card-stats {
            background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
            border: 1px solid #dee2e6;
            box-shadow: 0 2px 8px rgba(0,0,0,0.1);
        }

        .card-stats .card-body {
            background: transparent;
        }

        /* Target vs Achievement Table Borders */
        .table-bordered {
            border: 1px solid #dee2e6;
        }

        .table-bordered th,
        .table-bordered td {
            border: 1px solid #dee2e6;
            padding: 12px;
            text-align: center;
            vertical-align: middle;
        }

        .table-bordered thead th {
            border-bottom: 2px solid #dee2e6;
            background-color: #f8f9fa;
            font-weight: 600;
        }

        .table-bordered tbody tr:nth-child(even) {
            background-color: #f8f9fa;
        }

        .table-bordered tbody tr:hover {
            background-color: #e9ecef;
        }
    </style>
}

<div class="content">
    <div class="container-fluid">
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-rose">
                        <h4 class="card-title">RM Dashboard</h4>
                        <p class="card-category">Comprehensive Performance Analytics</p>
                    </div>
                    <div class="card-body">
                        <!-- Navigation Pills -->
                        <ul class="nav nav-pills nav-pills-rose" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" data-toggle="pill" href="#performance" role="tab">
                                    <i class="material-icons">dashboard</i> Performance Dashboard
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="pill" href="#growth" role="tab">
                                    <i class="material-icons">trending_up</i> Growth Analysis
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="pill" href="#attrition" role="tab">
                                    <i class="material-icons">assessment</i> Performance & Attrition
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" data-toggle="pill" href="#targets" role="tab">
                                    <i class="material-icons">track_changes</i> Target vs Achievement
                                </a>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content">
                            <!-- Performance Dashboard Tab -->
                            <div class="tab-pane fade show active" id="performance" role="tabpanel">
                                @if (Model?.RM_PERFORMANCE != null)
                                {
                                    @await Html.PartialAsync("_PerformanceData", Model.RM_PERFORMANCE)
                                }
                                else
                                {
                                    <div class="text-center py-5">
                                        <i class="material-icons" style="font-size: 48px; color: #999;">error_outline</i>
                                        <h4 class="mt-3 text-muted">Performance Data Unavailable</h4>
                                        <p class="text-muted">Unable to load performance data at this time.</p>
                                    </div>
                                }
                            </div>

                            <!-- Growth Analysis Tab -->
                            <div class="tab-pane fade" id="growth" role="tabpanel">
                                @if (Model?.RM_GROWTH != null && Model.RM_GROWTH.Any())
                                {
                                    @await Html.PartialAsync("_RMGrowthData", Model.RM_GROWTH)
                                }
                                else
                                {
                                    <div class="text-center py-5">
                                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                                        <h4 class="mt-3 text-muted">Growth Data Unavailable</h4>
                                        <p class="text-muted">No growth analysis data available for the current user.</p>
                                    </div>
                                }
                            </div>

                            <!-- Attrition Analysis Tab -->
                            <div class="tab-pane fade" id="attrition" role="tabpanel">
                                @if (Model?.RM_ATTRITION_GROWTH != null && Model.RM_ATTRITION_GROWTH.Any())
                                {
                                    @await Html.PartialAsync("_PerformanceAttritionData", Model.RM_ATTRITION_GROWTH)
                                }
                                else
                                {
                                    <div class="text-center py-5">
                                        <i class="material-icons" style="font-size: 48px; color: #999;">trending_down</i>
                                        <h4 class="mt-3 text-muted">Attrition Data Unavailable</h4>
                                        <p class="text-muted">No attrition analysis data available for the current user.</p>
                                    </div>
                                }
                            </div>

                            <!-- Target Achievement Tab -->
                            <div class="tab-pane fade" id="targets" role="tabpanel">
                                @if (Model?.RM_TARGET_VS_ACHIEVEMENT != null)
                                {
                                    @await Html.PartialAsync("_TargetAchievementData", Model.RM_TARGET_VS_ACHIEVEMENT)
                                }
                                else
                                {
                                    <div class="text-center py-5">
                                        <i class="material-icons" style="font-size: 48px; color: #999;">track_changes</i>
                                        <h4 class="mt-3 text-muted">Target Data Unavailable</h4>
                                        <p class="text-muted">No target vs achievement data available for the current user.</p>
                                    </div>
                                }
                            </div>
                        </div>



                        </div>

                        <!-- Refresh Button -->
                        <div class="row mt-3">
                            <div class="col-12 text-center">
                                <button onclick="refreshDashboard()" class="btn btn-rose">
                                    <i class="material-icons">refresh</i> Refresh Data
                                </button>
                                @if (ViewBag.CacheHit == true)
                                {
                                    <small class="text-muted ml-2">
                                        <i class="material-icons" style="font-size: 14px; vertical-align: middle;">cached</i>
                                        Data loaded from cache
                                    </small>
                                }
                                else if (ViewBag.CacheHit == false)
                                {
                                    <small class="text-muted ml-2">
                                        <i class="material-icons" style="font-size: 14px; vertical-align: middle;">cloud_download</i>
                                        Fresh data loaded
                                    </small>
                                }
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script src="~/js/material-dashboard.min.js"></script>
    <script>
        // Refresh functionality - clears cache and reloads fresh data
        function refreshDashboard() {
            // Show loading indicator
            const refreshBtn = document.querySelector('button[onclick="refreshDashboard()"]');
            if (refreshBtn) {
                const originalText = refreshBtn.innerHTML;
                refreshBtn.innerHTML = '<i class="material-icons">hourglass_empty</i> Refreshing...';
                refreshBtn.disabled = true;

                // Use the RefreshRMDashboard action which clears cache and redirects
                window.location.href = '@Url.Action("RefreshRMDashboard", "Dashboard")';
            }
        }

        // Initialize Material Dashboard components and tab functionality
        document.addEventListener('DOMContentLoaded', function() {
            // Initialize Bootstrap tabs
            const tabLinks = document.querySelectorAll('.nav-pills .nav-link');
            const tabPanes = document.querySelectorAll('.tab-pane');

            // Add click event listeners to tab links
            tabLinks.forEach(function(tabLink) {
                tabLink.addEventListener('click', function(e) {
                    e.preventDefault();

                    // Remove active class from all tabs and panes
                    tabLinks.forEach(link => link.classList.remove('active'));
                    tabPanes.forEach(pane => {
                        pane.classList.remove('show', 'active');
                    });

                    // Add active class to clicked tab
                    this.classList.add('active');

                    // Show corresponding tab pane
                    const targetId = this.getAttribute('href').substring(1);
                    const targetPane = document.getElementById(targetId);
                    if (targetPane) {
                        targetPane.classList.add('show', 'active');
                    }

                    console.log('Switched to tab:', targetId);
                });
            });

            // Ensure first tab is active on load
            if (tabLinks.length > 0 && tabPanes.length > 0) {
                tabLinks[0].classList.add('active');
                tabPanes[0].classList.add('show', 'active');
            }

            console.log('RM Dashboard loaded with server-side rendered data and tab functionality initialized');
        });
    </script>
}