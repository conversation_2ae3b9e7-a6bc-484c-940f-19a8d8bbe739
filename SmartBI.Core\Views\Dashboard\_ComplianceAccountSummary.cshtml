@model SmartBI.Data.ViewModels.Dashboard.ComplianceDashboardViewModel

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-rose">
                <h4 class="card-title">
                    <i class="material-icons">account_balance</i>
                    Compliance Account Statistics
                </h4>
                <p class="card-category">Branch-wise compliance account overview</p>
            </div>
            <div class="card-body">
                @if (Model?.AccountStatistics != null && Model.AccountStatistics.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped compliance-table" id="accountStatisticsTable">
                            <thead>
                                <tr>
                                    @for (int i = 0; i < Model.AccountStatistics.Columns.Count; i++)
                                    {
                                        <th>@Model.AccountStatistics.Columns[i].ColumnName</th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.AccountStatistics.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.AccountStatistics.Columns.Count; j++)
                                        {
                                            <td>@Model.AccountStatistics.Rows[i][j]</td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No Account Statistics Available</h4>
                        <p class="text-muted">No compliance account statistics found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Standalone FDR Summary -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-info">
                <h4 class="card-title">
                    <i class="material-icons">assessment</i>
                    Standalone FDR Summary
                </h4>
                <p class="card-category">Fixed Deposit Receipt summary analysis</p>
            </div>
            <div class="card-body">
                @if (Model?.StandAloneTDRSummary != null && Model.StandAloneTDRSummary.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped compliance-table" id="standaloneFdrSummaryTable">
                            <thead>
                                <tr>
                                    @for (int i = 0; i < Model.StandAloneTDRSummary.Columns.Count; i++)
                                    {
                                        <th>@Model.StandAloneTDRSummary.Columns[i].ColumnName</th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.StandAloneTDRSummary.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.StandAloneTDRSummary.Columns.Count; j++)
                                        {
                                            <td>@Model.StandAloneTDRSummary.Rows[i][j]</td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No FDR Summary Available</h4>
                        <p class="text-muted">No standalone FDR summary data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Customer KYC Update Status -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-warning">
                <h4 class="card-title">
                    <i class="material-icons">update</i>
                    Customer KYC Update Status
                </h4>
                <p class="card-category">KYC update tracking and status monitoring</p>
            </div>
            <div class="card-body">
                @if (Model?.CUSTOMER_KYC_UPDATE_STATUS != null && Model.CUSTOMER_KYC_UPDATE_STATUS.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped compliance-table" id="customerKycUpdateTable">
                            <thead>
                                <tr>
                                    @for (int i = 0; i < Model.CUSTOMER_KYC_UPDATE_STATUS.Columns.Count; i++)
                                    {
                                        <th>@Model.CUSTOMER_KYC_UPDATE_STATUS.Columns[i].ColumnName</th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.CUSTOMER_KYC_UPDATE_STATUS.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.CUSTOMER_KYC_UPDATE_STATUS.Columns.Count; j++)
                                        {
                                            <td>@Model.CUSTOMER_KYC_UPDATE_STATUS.Rows[i][j]</td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No KYC Update Status Available</h4>
                        <p class="text-muted">No customer KYC update status data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        // Initialize DataTables for account summary tables
        $('#accountStatisticsTable, #standaloneFdrSummaryTable, #customerKycUpdateTable').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "pageLength": 10
        });
    });
</script>
