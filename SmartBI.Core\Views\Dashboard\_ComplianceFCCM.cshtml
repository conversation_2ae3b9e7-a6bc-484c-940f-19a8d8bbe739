@model SmartBI.Data.ViewModels.Dashboard.ComplianceDashboardViewModel

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-danger">
                <h4 class="card-title">
                    <i class="material-icons">warning</i>
                    FCCM Alert Dashboard
                </h4>
                <p class="card-category">Financial Crime Compliance Management</p>
            </div>
            <div class="card-body">
                @if (Model?.FCCM != null)
                {
                    <!-- FCCM Alert Summary Cards -->
                    <div class="row">
                        <div class="col-lg-3 col-md-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-danger card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">warning</i>
                                    </div>
                                    <p class="card-category">Total Alerts</p>
                                    <h3 class="card-title">@Model.FCCM.TOTAL_ALERTS.ToString("N0")</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-danger">info</i>
                                        All FCCM Alerts
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-warning card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">pending</i>
                                    </div>
                                    <p class="card-category">Pending Alerts</p>
                                    <h3 class="card-title">@Model.FCCM.TOTAL_PENDING.ToString("N0")</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-warning">info</i>
                                        Awaiting Action
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-success card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">check_circle</i>
                                    </div>
                                    <p class="card-category">Closed Alerts</p>
                                    <h3 class="card-title">@Model.FCCM.TOTAL_CLOSED.ToString("N0")</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-success">info</i>
                                        Resolved Cases
                                    </div>
                                </div>
                            </div>
                        </div>
                        <div class="col-lg-3 col-md-6">
                            <div class="card card-stats">
                                <div class="card-header card-header-info card-header-icon">
                                    <div class="card-icon">
                                        <i class="material-icons">trending_up</i>
                                    </div>
                                    <p class="card-category">Resolution Rate</p>
                                    <h3 class="card-title">@Model.FCCM.PERCENTAGE.ToString("N1")%</h3>
                                </div>
                                <div class="card-footer">
                                    <div class="stats">
                                        <i class="material-icons text-info">info</i>
                                        Closure Percentage
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- FCCM Alert Progress Chart -->
                    <div class="row">
                        <div class="col-12">
                            <div class="card">
                                <div class="card-header card-header-rose">
                                    <h4 class="card-title">FCCM Alert Status Distribution</h4>
                                    <p class="card-category">Visual representation of alert status</p>
                                </div>
                                <div class="card-body">
                                    <div class="row">
                                        <div class="col-md-6">
                                            <canvas id="fccmAlertChart" width="400" height="200"></canvas>
                                        </div>
                                        <div class="col-md-6">
                                            <div class="table-responsive">
                                                <table class="table table-striped">
                                                    <thead>
                                                        <tr>
                                                            <th>Status</th>
                                                            <th>Count</th>
                                                            <th>Percentage</th>
                                                        </tr>
                                                    </thead>
                                                    <tbody>
                                                        <tr>
                                                            <td><span class="badge badge-danger">Total Alerts</span></td>
                                                            <td>@Model.FCCM.TOTAL_ALERTS.ToString("N0")</td>
                                                            <td>100.0%</td>
                                                        </tr>
                                                        <tr>
                                                            <td><span class="badge badge-warning">Pending</span></td>
                                                            <td>@Model.FCCM.TOTAL_PENDING.ToString("N0")</td>
                                                            <td>@((Model.FCCM.TOTAL_ALERTS > 0 ? (decimal)Model.FCCM.TOTAL_PENDING / Model.FCCM.TOTAL_ALERTS * 100 : 0).ToString("N1"))%</td>
                                                        </tr>
                                                        <tr>
                                                            <td><span class="badge badge-success">Closed</span></td>
                                                            <td>@Model.FCCM.TOTAL_CLOSED.ToString("N0")</td>
                                                            <td>@Model.FCCM.PERCENTAGE.ToString("N1")%</td>
                                                        </tr>
                                                    </tbody>
                                                </table>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No FCCM Alert Data Available</h4>
                        <p class="text-muted">No FCCM alert data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        @if (Model?.FCCM != null)
        {
            // Create FCCM Alert Chart
            var ctx = document.getElementById('fccmAlertChart').getContext('2d');
            var fccmChart = new Chart(ctx, {
                type: 'doughnut',
                data: {
                    labels: ['Pending Alerts', 'Closed Alerts'],
                    datasets: [{
                        data: [@Model.FCCM.TOTAL_PENDING, @Model.FCCM.TOTAL_CLOSED],
                        backgroundColor: [
                            '#ff9800', // Orange for pending
                            '#4caf50'  // Green for closed
                        ],
                        borderWidth: 2,
                        borderColor: '#fff'
                    }]
                },
                options: {
                    responsive: true,
                    maintainAspectRatio: false,
                    legend: {
                        position: 'bottom',
                        labels: {
                            padding: 20,
                            usePointStyle: true
                        }
                    },
                    tooltips: {
                        callbacks: {
                            label: function(tooltipItem, data) {
                                var dataset = data.datasets[tooltipItem.datasetIndex];
                                var total = dataset.data.reduce(function(previousValue, currentValue) {
                                    return previousValue + currentValue;
                                });
                                var currentValue = dataset.data[tooltipItem.index];
                                var percentage = Math.round(((currentValue / total) * 100));
                                return data.labels[tooltipItem.index] + ': ' + currentValue + ' (' + percentage + '%)';
                            }
                        }
                    }
                }
            });
        }
    });
</script>
