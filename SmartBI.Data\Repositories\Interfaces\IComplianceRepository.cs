using System.Data;
using SmartBI.Data.ViewModels.Dashboard;

namespace SmartBI.Data.Repositories.Interfaces
{
    /// <summary>
    /// Repository interface for Compliance dashboard operations
    /// Legacy SmartBI2 DashboardRepository compatible methods
    /// </summary>
    public interface IComplianceRepository
    {
        #region KYC Overdue APIs
        /// <summary>
        /// Legacy API: GET_DASHBOARD_KYC_OVERDUE - Gets KYC overdue data by risk level
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_KYC_OVERDUE method
        /// </summary>
        /// <param name="riskLevel">Risk level: "HIGH" or "LOW"</param>
        /// <param name="userId">User ID</param>
        /// <returns>KYC overdue data</returns>
        Task<DataTable> GET_DASHBOARD_KYC_OVERDUE(string riskLevel, string userId);

        /// <summary>
        /// Legacy API: GET_DASHBOARD_KYC_OVERDUE_SAF - Gets KYC overdue SAF data by risk level
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_KYC_OVERDUE_SAF method
        /// </summary>
        /// <param name="riskLevel">Risk level: "HIGH" or "LOW"</param>
        /// <param name="userId">User ID</param>
        /// <returns>KYC overdue SAF data</returns>
        Task<DataTable> GET_DASHBOARD_KYC_OVERDUE_SAF(string riskLevel, string userId);
        #endregion

        #region Compliance Monitoring APIs
        /// <summary>
        /// Legacy API: GET_DASHBOARD_NGO_NPO - Gets NGO/NPO compliance data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_NGO_NPO method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>NGO/NPO data</returns>
        Task<DataTable> GET_DASHBOARD_NGO_NPO(string userId);

        /// <summary>
        /// Legacy API: GET_DASHBOARD_PEP_IP - Gets PEP/IP compliance data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_PEP_IP method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>PEP/IP data</returns>
        Task<DataTable> GET_DASHBOARD_PEP_IP(string userId);

        /// <summary>
        /// Legacy API: GET_DASHBOARD_STAND_ALONE_FDR - Gets standalone FDR data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_STAND_ALONE_FDR method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Standalone FDR data</returns>
        Task<DataTable> GET_DASHBOARD_STAND_ALONE_FDR(string userId);

        /// <summary>
        /// Legacy API: GET_DASHBOARD_FATCA - Gets FATCA compliance data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_FATCA method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>FATCA data</returns>
        Task<DataTable> GET_DASHBOARD_FATCA(string userId);

        /// <summary>
        /// Legacy API: GET_DASHBOARD_AML - Gets AML compliance data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_AML method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>AML data</returns>
        Task<DataTable> GET_DASHBOARD_AML(string userId);

        /// <summary>
        /// Legacy API: GET_DASHBOARD_FCCM_ALERT - Gets FCCM alert data
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_FCCM_ALERT method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>FCCM alert data</returns>
        Task<SmartBI.Data.ViewModels.Dashboard.FCCM_ALERT_DASHBOARD> GET_DASHBOARD_FCCM_ALERT(string userId);

        /// <summary>
        /// Legacy API: GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS - Gets customer KYC update status
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Customer KYC update status data</returns>
        Task<DataTable> GET_DASHBOARD_CUSTOMER_KYC_UPDATE_STATUS(string userId);

        /// <summary>
        /// Legacy API: GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS - Gets compliance account statistics
        /// Compatible with legacy DashboardRepository.GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS method
        /// Note: Legacy spelling "COMPLAINCE" maintained for compatibility
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Account statistics data</returns>
        Task<DataTable> GET_DASHBOARD_COMPLAINCE_ACCOUNT_STATISTICS(string userId);

        /// <summary>
        /// Legacy API: SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY - Gets standalone FDR summary
        /// Compatible with legacy DashboardRepository.SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY method
        /// </summary>
        /// <param name="userId">User ID</param>
        /// <returns>Standalone FDR summary data</returns>
        Task<DataTable> SP_GET_DASHBOARD_STANDALONE_FDR_SUMMARY(string userId);
        #endregion
    }
}
