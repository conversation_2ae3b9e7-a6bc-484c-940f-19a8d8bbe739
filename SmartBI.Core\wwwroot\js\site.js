// Common utility functions
const utils = {
    // Format numbers with commas
    formatNumber: (val) => new Intl.NumberFormat('en-US').format(val || 0),
    
    // Format currency (BDT)
    formatCurrency: (val) => 'BDT ' + new Intl.NumberFormat('en-US').format(val || 0),
    
    // Format percentage
    formatPercentage: (val) => (val || 0).toFixed(2) + '%',
    
    // Show loading spinner
    showLoader: (elementId) => {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.display = 'block';
        }
    },
    
    // Hide loading spinner
    hideLoader: (elementId) => {
        const element = document.getElementById(elementId);
        if (element) {
            element.style.display = 'none';
        }
    },
    
    // Show alert message
    showAlert: (type, message) => {
        const alertDiv = document.getElementById(type + 'Alert');
        const messageSpan = document.getElementById(type + 'Message');
        if (alertDiv && messageSpan) {
            messageSpan.textContent = message;
            alertDiv.style.display = 'block';
            setTimeout(() => {
                alertDiv.style.display = 'none';
            }, 5000);
        }
    },
    
    // Case-insensitive object field access
    getFieldValue: (obj, field, defaultValue = 0) => {
        if (!obj) return defaultValue;
        
        // Try exact match first
        if (obj[field] !== undefined && obj[field] !== null) {
            return obj[field];
        }
        
        // Try case-insensitive match
        const lowerField = field.toLowerCase();
        for (const key in obj) {
            if (key.toLowerCase() === lowerField) {
                return obj[key];
            }
        }
        
        return defaultValue;
    }
};

// Initialize Material Dashboard components
document.addEventListener('DOMContentLoaded', function() {
    // Initialize tooltips
    $('[data-toggle="tooltip"]').tooltip();
    
    // Initialize popovers
    $('[data-toggle="popover"]').popover();
    
    // Add ripple effect to buttons
    const buttons = document.querySelectorAll('.btn');
    buttons.forEach(button => {
        button.addEventListener('click', function(e) {
            const ripple = document.createElement('div');
            ripple.classList.add('ripple');
            this.appendChild(ripple);
            const rect = this.getBoundingClientRect();
            const size = Math.max(rect.width, rect.height);
            ripple.style.width = ripple.style.height = size + 'px';
            ripple.style.left = e.clientX - rect.left - size/2 + 'px';
            ripple.style.top = e.clientY - rect.top - size/2 + 'px';
            ripple.addEventListener('animationend', () => {
                ripple.remove();
            });
        });
    });
}); 