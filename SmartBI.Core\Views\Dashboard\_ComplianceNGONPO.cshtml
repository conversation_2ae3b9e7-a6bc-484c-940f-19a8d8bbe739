@model SmartBI.Data.ViewModels.Dashboard.ComplianceDashboardViewModel

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-primary">
                <h4 class="card-title">
                    <i class="material-icons">business</i>
                    NGO/NPO Compliance Monitoring
                </h4>
                <p class="card-category">Non-Governmental Organizations & Non-Profit Organizations</p>
            </div>
            <div class="card-body">
                @if (Model?.NGO_NPO != null && Model.NGO_NPO.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered compliance-table" id="ngoNpoTable">
                            <thead>
                                <tr>
                                    @for (int i = 0; i < Model.NGO_NPO.Columns.Count; i++)
                                    {
                                        <th>@Model.NGO_NPO.Columns[i].ColumnName</th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.NGO_NPO.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.NGO_NPO.Columns.Count; j++)
                                        {
                                            <td>@Model.NGO_NPO.Rows[i][j]</td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No NGO/NPO Data Available</h4>
                        <p class="text-muted">No NGO/NPO compliance data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#ngoNpoTable').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "pageLength": 10
        });
    });
</script>
