namespace SmartBI.Data.ViewModels
{
    public class RM_TARGET_VS_ACHIEVEMENT
    {
        public string RM_CODE { get; set; }
        public int MONTHLY_TARGET_ACCOUNTS { get; set; }
        public decimal MONTHLY_TARGET_AMOUNTS { get; set; }
        public int TARGET_YEAR { get; set; }
        public int YTD_TARGET_ACCOUNTS { get; set; }
        public int YEARLY_TARGET_ACCOUNTS { get; set; }
        public decimal YEARLY_TARGET_AMOUNTS { get; set; }
        public decimal YTD_TARGET_AMOUNTS { get; set; }
        public int YTD_ACHIEVED_ACCOUNTS { get; set; }
        public decimal YTD_ACHIEVED_AMOUNTS { get; set; }
        public int MTD_ACHIEVED_ACCOUNTS { get; set; }
        public decimal MTD_ACHIEVED_AMOUNTS { get; set; }
        public int MTD_VARIANCE_ACCOUNTS { get; set; }
        public decimal MTD_VARIANCE_AMOUNS { get; set; }
        public int YTD_VARIANCE_ACCOUNTS { get; set; }
        public decimal YTD_VARIANCE_AMOUNTS { get; set; }
        public decimal ACC_PERFORMANCE { get; set; }
        public decimal VOL_PERFORMANCE { get; set; }
    }
}
