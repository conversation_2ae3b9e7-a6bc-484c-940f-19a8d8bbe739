@model SmartBI.Data.ViewModels.Dashboard.ComplianceDashboardViewModel
@{
    ViewData["Title"] = "Compliance Dashboard";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-header card-header-rose">
                        <h4 class="card-title">
                            <i class="material-icons">security</i>
                            Compliance Dashboard
                        </h4>
                        <p class="card-category">Comprehensive Compliance Analytics & Monitoring</p>
                    </div>
                </div>
            </div>
        </div>

        <!-- Navigation Tabs -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body">
                        <!-- Navigation Pills - Horizontal Tabs -->
                        <ul class="nav nav-pills nav-pills-rose" id="compliance-tabs" role="tablist">
                            <li class="nav-item">
                                <a class="nav-link active" id="account-summary-tab" data-toggle="pill" href="#account-summary" role="tab" aria-controls="account-summary" aria-selected="true">
                                    <i class="material-icons">account_balance</i>
                                    Account Summary
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="kyc-casa-tab" data-toggle="pill" href="#kyc-casa" role="tab" aria-controls="kyc-casa" aria-selected="false">
                                    <i class="material-icons">assignment_late</i>
                                    KYC Overdue (CASA)
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="kyc-standalone-tab" data-toggle="pill" href="#kyc-standalone" role="tab" aria-controls="kyc-standalone" aria-selected="false">
                                    <i class="material-icons">assignment_turned_in</i>
                                    KYC Overdue (Stand Alone)
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="ngo-npo-tab" data-toggle="pill" href="#ngo-npo" role="tab" aria-controls="ngo-npo" aria-selected="false">
                                    <i class="material-icons">business</i>
                                    NGO/NPO
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="pep-ip-tab" data-toggle="pill" href="#pep-ip" role="tab" aria-controls="pep-ip" aria-selected="false">
                                    <i class="material-icons">person_pin</i>
                                    PEP/IP
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="fatca-tab" data-toggle="pill" href="#fatca" role="tab" aria-controls="fatca" aria-selected="false">
                                    <i class="material-icons">gavel</i>
                                    FATCA
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="aml-tab" data-toggle="pill" href="#aml" role="tab" aria-controls="aml" aria-selected="false">
                                    <i class="material-icons">shield</i>
                                    AML
                                </a>
                            </li>
                            <li class="nav-item">
                                <a class="nav-link" id="fccm-tab" data-toggle="pill" href="#fccm" role="tab" aria-controls="fccm" aria-selected="false">
                                    <i class="material-icons">warning</i>
                                    FCCM Alert
                                </a>
                            </li>
                        </ul>

                        <!-- Tab Content -->
                        <div class="tab-content" id="compliance-tab-content">
                            <!-- Account Summary Tab -->
                            <div class="tab-pane fade show active" id="account-summary" role="tabpanel" aria-labelledby="account-summary-tab">
                                @await Html.PartialAsync("_ComplianceAccountSummary", Model)
                            </div>

                            <!-- KYC Overdue (CASA) Tab -->
                            <div class="tab-pane fade" id="kyc-casa" role="tabpanel" aria-labelledby="kyc-casa-tab">
                                @await Html.PartialAsync("_ComplianceKYCCASA", Model)
                            </div>

                            <!-- KYC Overdue (Stand Alone) Tab -->
                            <div class="tab-pane fade" id="kyc-standalone" role="tabpanel" aria-labelledby="kyc-standalone-tab">
                                @await Html.PartialAsync("_ComplianceKYCStandalone", Model)
                            </div>

                            <!-- NGO/NPO Tab -->
                            <div class="tab-pane fade" id="ngo-npo" role="tabpanel" aria-labelledby="ngo-npo-tab">
                                @await Html.PartialAsync("_ComplianceNGONPO", Model)
                            </div>

                            <!-- PEP/IP Tab -->
                            <div class="tab-pane fade" id="pep-ip" role="tabpanel" aria-labelledby="pep-ip-tab">
                                @await Html.PartialAsync("_CompliancePEPIP", Model)
                            </div>

                            <!-- FATCA Tab -->
                            <div class="tab-pane fade" id="fatca" role="tabpanel" aria-labelledby="fatca-tab">
                                @await Html.PartialAsync("_ComplianceFATCA", Model)
                            </div>

                            <!-- AML Tab -->
                            <div class="tab-pane fade" id="aml" role="tabpanel" aria-labelledby="aml-tab">
                                @await Html.PartialAsync("_ComplianceAML", Model)
                            </div>

                            <!-- FCCM Alert Tab -->
                            <div class="tab-pane fade" id="fccm" role="tabpanel" aria-labelledby="fccm-tab">
                                @await Html.PartialAsync("_ComplianceFCCM", Model)
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Cache Information -->
        @if (ViewBag.CacheHit != null)
        {
            <div class="row">
                <div class="col-12">
                    <div class="alert alert-info alert-dismissible fade show" role="alert">
                        <i class="material-icons">info</i>
                        <strong>Cache Status:</strong> 
                        @if ((bool)ViewBag.CacheHit)
                        {
                            <span class="text-success">Data loaded from cache</span>
                        }
                        else
                        {
                            <span class="text-warning">Fresh data loaded from microservices</span>
                        }
                        <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                            <span aria-hidden="true">&times;</span>
                        </button>
                    </div>
                </div>
            </div>
        }

        <!-- Refresh Button -->
        <div class="row">
            <div class="col-12">
                <div class="card">
                    <div class="card-body text-center">
                        <a href="@Url.Action("RefreshComplianceDashboard", "Dashboard")" class="btn btn-rose">
                            <i class="material-icons">refresh</i>
                            Refresh Dashboard
                        </a>
                        <button type="button" class="btn btn-info" onclick="clearComplianceCache()">
                            <i class="material-icons">clear_all</i>
                            Clear Cache
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

@section Scripts {
    <script>
        // Initialize DataTables for all compliance tables
        $(document).ready(function() {
            // Initialize all DataTables with consistent settings
            $('.compliance-table').DataTable({
                "paging": true,
                "lengthChange": false,
                "searching": true,
                "ordering": true,
                "info": true,
                "autoWidth": false,
                "responsive": true,
                "pageLength": 10,
                "language": {
                    "search": "Search:",
                    "lengthMenu": "Show _MENU_ entries",
                    "info": "Showing _START_ to _END_ of _TOTAL_ entries",
                    "infoEmpty": "No entries available",
                    "infoFiltered": "(filtered from _MAX_ total entries)",
                    "paginate": {
                        "first": "First",
                        "last": "Last",
                        "next": "Next",
                        "previous": "Previous"
                    }
                }
            });

            // Tab switching event handler
            $('#compliance-tabs a[data-toggle="pill"]').on('shown.bs.tab', function (e) {
                // Recalculate DataTables column widths when tab is shown
                $.fn.dataTable.tables({ visible: true, api: true }).columns.adjust();
            });
        });

        // Clear compliance cache function
        function clearComplianceCache() {
            $.ajax({
                url: '@Url.Action("ClearComplianceCache", "Dashboard")',
                type: 'POST',
                success: function(response) {
                    if (response.success) {
                        showNotification('success', 'Cache cleared successfully');
                        setTimeout(function() {
                            location.reload();
                        }, 1000);
                    } else {
                        showNotification('error', 'Error clearing cache: ' + response.message);
                    }
                },
                error: function() {
                    showNotification('error', 'Error clearing cache');
                }
            });
        }

        // Show notification function
        function showNotification(type, message) {
            var alertClass = type === 'success' ? 'alert-success' : 'alert-danger';
            var icon = type === 'success' ? 'check_circle' : 'error';
            
            var notification = `
                <div class="alert ${alertClass} alert-dismissible fade show" role="alert">
                    <i class="material-icons">${icon}</i>
                    ${message}
                    <button type="button" class="close" data-dismiss="alert" aria-label="Close">
                        <span aria-hidden="true">&times;</span>
                    </button>
                </div>
            `;
            
            $('.container-fluid').prepend(notification);
            
            // Auto-dismiss after 3 seconds
            setTimeout(function() {
                $('.alert').alert('close');
            }, 3000);
        }
    </script>

    <style>
        /* Custom styling for compliance dashboard */
        .nav-pills .nav-link {
            border-radius: 30px;
            margin: 0 5px;
            padding: 10px 20px;
            font-weight: 500;
        }

        .nav-pills .nav-link.active {
            background-color: #e91e63;
            color: white;
        }

        .nav-pills .nav-link:not(.active) {
            background-color: #f8f9fa;
            color: #495057;
        }

        .nav-pills .nav-link:not(.active):hover {
            background-color: #e9ecef;
            color: #e91e63;
        }

        .nav-pills .nav-link i {
            margin-right: 8px;
            vertical-align: middle;
        }

        .compliance-table {
            font-size: 0.875rem;
        }

        .compliance-table th {
            background-color: #f8f9fa;
            font-weight: 600;
            border-bottom: 2px solid #dee2e6;
        }

        .compliance-table td {
            vertical-align: middle;
        }

        .risk-high {
            color: #dc3545;
            font-weight: 600;
        }

        .risk-low {
            color: #28a745;
            font-weight: 600;
        }

        .card-stats .card-body {
            background: transparent;
        }
    </style>
}
