@{
    ViewData["Title"] = ViewBag.Title ?? "API Connectivity Test";
    Layout = "~/Views/Shared/_Layout.cshtml";
}

<div class="content">
    <div class="container-fluid">
        <!-- Page Header -->
        <div class="row">
            <div class="col-md-12">
                <div class="card">
                    <div class="card-header card-header-rose card-header-icon">
                        <div class="card-icon">
                            <i class="material-icons">api</i>
                        </div>
                        <h4 class="card-title">SmartBI Microservice API Connectivity Test</h4>
                        <p class="card-category">Testing connection to: <strong>@ViewBag.MicroserviceBaseUrl</strong></p>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-info">
                            <i class="material-icons">info</i>
                            This page tests the connectivity to all SmartBI Microservice API endpoints.
                            <br><strong>Green</strong> = API is working, <strong>Red</strong> = API has issues.
                        </div>
                        
                        <div class="table-responsive">
                            <table class="table table-striped">
                                <thead>
                                    <tr>
                                        <th>Status</th>
                                        <th>Endpoint</th>
                                        <th>URL</th>
                                        <th>HTTP Status</th>
                                        <th>Response</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @foreach (dynamic result in ViewBag.Results)
                                    {
                                        <tr class="@(result.success ? "table-success" : "table-danger")">
                                            <td>
                                                @if (result.success)
                                                {
                                                    <i class="material-icons text-success">check_circle</i>
                                                }
                                                else
                                                {
                                                    <i class="material-icons text-danger">error</i>
                                                }
                                            </td>
                                            <td><strong>@result.endpoint</strong></td>
                                            <td><small>@result.url</small></td>
                                            <td>
                                                <span class="badge @(result.success ? "badge-success" : "badge-danger")">
                                                    @result.status
                                                </span>
                                            </td>
                                            <td>
                                                @if (result.success)
                                                {
                                                    <span class="text-success">@result.content</span>
                                                }
                                                else if (result.error != null)
                                                {
                                                    <span class="text-danger">@result.error</span>
                                                }
                                                else
                                                {
                                                    <span class="text-warning">@result.content</span>
                                                }
                                            </td>
                                        </tr>
                                    }
                                </tbody>
                            </table>
                        </div>
                        
                        <div class="row mt-3">
                            <div class="col-md-12">
                                <a href="@Url.Action("ComplianceTest", "Dashboard")" class="btn btn-rose">
                                    <i class="material-icons">dashboard</i>
                                    Go to Compliance Test Dashboard
                                </a>
                                <button onclick="location.reload()" class="btn btn-info">
                                    <i class="material-icons">refresh</i>
                                    Refresh Test
                                </button>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    .table-success {
        background-color: rgba(40, 167, 69, 0.1) !important;
    }
    
    .table-danger {
        background-color: rgba(220, 53, 69, 0.1) !important;
    }
    
    .badge-success {
        background-color: #28a745;
    }
    
    .badge-danger {
        background-color: #dc3545;
    }
    
    .text-success {
        color: #28a745 !important;
    }
    
    .text-danger {
        color: #dc3545 !important;
    }
    
    .text-warning {
        color: #ffc107 !important;
    }
</style>
