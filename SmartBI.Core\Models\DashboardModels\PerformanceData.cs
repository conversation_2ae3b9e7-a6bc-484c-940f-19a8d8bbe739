using System;

namespace SmartBI.Core.Models.DashboardModels
{
    public class PerformanceData
    {
        // Total Stats
        public int TOTAL_AC { get; set; }
        public decimal TOTAL_BAL { get; set; }
        public decimal AVG_BAL { get; set; }
        public decimal COD { get; set; }

        // Current Account Stats
        public int CA_AC { get; set; }
        public decimal CA_BAL { get; set; }
        public decimal CA_AVG_BAL { get; set; }

        // Savings Account Stats
        public int SA_AC { get; set; }
        public decimal SA_BAL { get; set; }
        public decimal SA_AVG_BAL { get; set; }

        // FDR Account Stats
        public int FDR_AC { get; set; }
        public decimal FDR_BAL { get; set; }
        public decimal FDR_AVG_BAL { get; set; }

        // DPS Account Stats
        public int DPS_AC { get; set; }
        public decimal DPS_BAL { get; set; }
        public decimal DPS_AVG_BAL { get; set; }

        // Dormant Account Stats
        public int NO_OF_DORMANT_ACCOUNTS { get; set; }
        public decimal BALANCE_OF_DORMANT_ACCOUNTS { get; set; }

        // Zero Balance Account Stats
        public int NO_OF_ZERO_ACCOUNTS { get; set; }
        public decimal BALANCE_OF_ZERO_ACCOUNTS { get; set; }
    }
} 