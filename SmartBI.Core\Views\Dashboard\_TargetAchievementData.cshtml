@model SmartBI.Data.ViewModels.RM_TARGET_VS_ACHIEVEMENT

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-rose">
                <h4 class="card-title">Target VS Achievement</h4>
                <p class="card-category">Performance comparison for @DateTime.Now.Year</p>
            </div>
            <div class="card-body">
                @if (Model != null)
                {
                    <div class="table-responsive">
                        <table class="table table-striped">
                            <thead>
                                <tr>
                                    <th colspan="4" style="text-align:center; background-color: #f8f9fa;">Target</th>
                                    <th colspan="4" style="text-align:center; background-color: #e9ecef;">Achievement</th>
                                    <th colspan="4" style="text-align:center; background-color: #dee2e6;">Variance</th>
                                    <th colspan="2" style="text-align:center; background-color: #ced4da;">Overall Performance</th>
                                </tr>
                                <tr>
                                    <th colspan="2" style="text-align:center;">Account</th>
                                    <th colspan="2" style="text-align:center;">Volume</th>
                                    <th colspan="2" style="text-align:center;">Account</th>
                                    <th colspan="2" style="text-align:center;">Volume</th>
                                    <th colspan="2" style="text-align:center;">Account</th>
                                    <th colspan="2" style="text-align:center;">Volume</th>
                                    <th colspan="2" style="text-align:center;"></th>
                                </tr>
                                <tr>
                                    <th>Monthly</th>
                                    <th>Yearly</th>
                                    <th>Monthly</th>
                                    <th>Yearly</th>
                                    <th>Monthly</th>
                                    <th>Yearly</th>
                                    <th>Monthly</th>
                                    <th>Yearly</th>
                                    <th>MTD</th>
                                    <th>YTD</th>
                                    <th>MTD</th>
                                    <th>YTD</th>
                                    <th>Account Growth</th>
                                    <th>Volume Growth</th>
                                </tr>
                            </thead>
                            <tbody>
                                <tr>
                                    <td>@Model.MONTHLY_TARGET_ACCOUNTS.ToString("N0")</td>
                                    <td>@Model.YEARLY_TARGET_ACCOUNTS.ToString("N0")</td>
                                    <td>BDT @Model.MONTHLY_TARGET_AMOUNTS.ToString("N2")</td>
                                    <td>BDT @Model.YEARLY_TARGET_AMOUNTS.ToString("N2")</td>
                                    <td>@Model.MTD_ACHIEVED_ACCOUNTS.ToString("N0")</td>
                                    <td>@Model.YTD_ACHIEVED_ACCOUNTS.ToString("N0")</td>
                                    <td>BDT @Model.MTD_ACHIEVED_AMOUNTS.ToString("N2")</td>
                                    <td>BDT @Model.YTD_ACHIEVED_AMOUNTS.ToString("N2")</td>
                                    <td class="@(Model.MTD_VARIANCE_ACCOUNTS >= 0 ? "text-success" : "text-danger")">@Model.MTD_VARIANCE_ACCOUNTS.ToString("N0")</td>
                                    <td class="@(Model.YTD_VARIANCE_ACCOUNTS >= 0 ? "text-success" : "text-danger")">@Model.YTD_VARIANCE_ACCOUNTS.ToString("N0")</td>
                                    <td class="@(Model.MTD_VARIANCE_AMOUNS >= 0 ? "text-success" : "text-danger")">BDT @Model.MTD_VARIANCE_AMOUNS.ToString("N2")</td>
                                    <td class="@(Model.YTD_VARIANCE_AMOUNTS >= 0 ? "text-success" : "text-danger")">BDT @Model.YTD_VARIANCE_AMOUNTS.ToString("N2")</td>
                                    <td class="@(Model.ACC_PERFORMANCE >= 100 ? "text-success" : Model.ACC_PERFORMANCE >= 75 ? "text-warning" : "text-danger")">@Model.ACC_PERFORMANCE.ToString("N2")%</td>
                                    <td class="@(Model.VOL_PERFORMANCE >= 100 ? "text-success" : Model.VOL_PERFORMANCE >= 75 ? "text-warning" : "text-danger")">@Model.VOL_PERFORMANCE.ToString("N2")%</td>
                                </tr>
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No Target Data Available</h4>
                        <p class="text-muted">No target vs achievement data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

@if (Model != null)
{
    <!-- Performance Summary Cards -->
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-info card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">track_changes</i>
                    </div>
                    <p class="card-category">MTD Account Achievement</p>
                    <h3 class="card-title">@((Model.MONTHLY_TARGET_ACCOUNTS > 0 ? (decimal)Model.MTD_ACHIEVED_ACCOUNTS / Model.MONTHLY_TARGET_ACCOUNTS * 100 : 0).ToString("N1"))%</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-info">info</i>
                        @Model.MTD_ACHIEVED_ACCOUNTS.ToString("N0") of @Model.MONTHLY_TARGET_ACCOUNTS.ToString("N0")
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-success card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">monetization_on</i>
                    </div>
                    <p class="card-category">MTD Volume Achievement</p>
                    <h3 class="card-title">@((Model.MONTHLY_TARGET_AMOUNTS > 0 ? Model.MTD_ACHIEVED_AMOUNTS / Model.MONTHLY_TARGET_AMOUNTS * 100 : 0).ToString("N1"))%</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-success">info</i>
                        BDT @Model.MTD_ACHIEVED_AMOUNTS.ToString("N0") of @Model.MONTHLY_TARGET_AMOUNTS.ToString("N0")
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-warning card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">trending_up</i>
                    </div>
                    <p class="card-category">YTD Account Achievement</p>
                    <h3 class="card-title">@((Model.YEARLY_TARGET_ACCOUNTS > 0 ? (decimal)Model.YTD_ACHIEVED_ACCOUNTS / Model.YEARLY_TARGET_ACCOUNTS * 100 : 0).ToString("N1"))%</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-warning">info</i>
                        @Model.YTD_ACHIEVED_ACCOUNTS.ToString("N0") of @Model.YEARLY_TARGET_ACCOUNTS.ToString("N0")
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-rose card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">assessment</i>
                    </div>
                    <p class="card-category">YTD Volume Achievement</p>
                    <h3 class="card-title">@((Model.YEARLY_TARGET_AMOUNTS > 0 ? Model.YTD_ACHIEVED_AMOUNTS / Model.YEARLY_TARGET_AMOUNTS * 100 : 0).ToString("N1"))%</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-rose">info</i>
                        BDT @Model.YTD_ACHIEVED_AMOUNTS.ToString("N0") of @Model.YEARLY_TARGET_AMOUNTS.ToString("N0")
                    </div>
                </div>
            </div>
        </div>
    </div>
}
