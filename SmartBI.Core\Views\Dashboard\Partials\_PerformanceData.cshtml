@model SmartBI.Core.Models.DashboardModels.PerformanceDataViewModel

<div class="row" id="performanceMetrics">
    <!-- Total Stats Row -->
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-rose card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_balance_wallet</i>
                </div>
                <p class="card-category">Total Accounts</p>
                <h3 class="card-title">@Model.TOTAL_AC.ToString("N0")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-rose">info</i>
                    All account types combined
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-success card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">account_balance</i>
                </div>
                <p class="card-category">Total Balance</p>
                <h3 class="card-title">BDT @Model.TOTAL_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-success">info</i>
                    Combined balance amount
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-info card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">trending_up</i>
                </div>
                <p class="card-category">Average Balance</p>
                <h3 class="card-title">BDT @Model.AVG_BAL.ToString("N2")</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-info">info</i>
                    Per account average
                </div>
            </div>
        </div>
    </div>
    <div class="col-lg-3 col-md-6 col-sm-6">
        <div class="card card-stats">
            <div class="card-header card-header-warning card-header-icon">
                <div class="card-icon">
                    <i class="material-icons">attach_money</i>
                </div>
                <p class="card-category">Cost of Deposit</p>
                <h3 class="card-title">@Model.COD.ToString("N2")%</h3>
            </div>
            <div class="card-footer">
                <div class="stats">
                    <i class="material-icons text-warning">info</i>
                    Current COD rate
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Account Type Details -->
<div class="row mt-4">
    <div class="col-md-12">
        <div class="card">
            <div class="card-header card-header-primary">
                <h4 class="card-title">Account Type Details</h4>
                <p class="card-category">Breakdown by account type</p>
            </div>
            <div class="card-body">
                <div class="table-responsive">
                    <table class="table">
                        <thead class="text-primary">
                            <tr>
                                <th>Account Type</th>
                                <th>Total Accounts</th>
                                <th>Total Balance</th>
                                <th>Average Balance</th>
                            </tr>
                        </thead>
                        <tbody>
                            <tr>
                                <td>Current Accounts</td>
                                <td>@Model.CA_AC.ToString("N0")</td>
                                <td>BDT @Model.CA_BAL.ToString("N2")</td>
                                <td>BDT @Model.CA_AVG_BAL.ToString("N2")</td>
                            </tr>
                            <tr>
                                <td>Savings Accounts</td>
                                <td>@Model.SA_AC.ToString("N0")</td>
                                <td>BDT @Model.SA_BAL.ToString("N2")</td>
                                <td>BDT @Model.SA_AVG_BAL.ToString("N2")</td>
                            </tr>
                            <tr>
                                <td>Fixed Deposits</td>
                                <td>@Model.FDR_AC.ToString("N0")</td>
                                <td>BDT @Model.FDR_BAL.ToString("N2")</td>
                                <td>BDT @Model.FDR_AVG_BAL.ToString("N2")</td>
                            </tr>
                            <tr>
                                <td>DPS Accounts</td>
                                <td>@Model.DPS_AC.ToString("N0")</td>
                                <td>BDT @Model.DPS_BAL.ToString("N2")</td>
                                <td>BDT @Model.DPS_AVG_BAL.ToString("N2")</td>
                            </tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>
</div> 