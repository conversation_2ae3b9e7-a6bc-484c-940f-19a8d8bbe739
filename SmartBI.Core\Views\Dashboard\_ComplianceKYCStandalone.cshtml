@model SmartBI.Data.ViewModels.Dashboard.ComplianceDashboardViewModel

<!-- KYC Overdue SAF High Risk -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-danger">
                <h4 class="card-title">
                    <i class="material-icons">warning</i>
                    Periodic KYC - Branch wise Total KYC Overdue Status (SAF)
                </h4>
                <p class="card-category">
                    <span class="risk-high">Risk Level: High</span> - Stand Alone Accounts
                </p>
            </div>
            <div class="card-body">
                @if (Model?.KYC_OVERDUE_SAF_HIGH != null && Model.KYC_OVERDUE_SAF_HIGH.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered compliance-table" id="kycOverdueSafHighTable">
                            <thead>
                                <tr>
                                    <th>SL</th>
                                    <th>Branch</th>
                                    <th>Total BAU Overdue</th>
                                    <th>0 to 30 Days</th>
                                    <th>31 to 60 Days</th>
                                    <th>61 to 90 Days</th>
                                    <th>91 to 120 Days (4 months)</th>
                                    <th>121 to 180 Days (4 TO 6m)</th>
                                    <th>181 days to 1 Year</th>
                                    <th>Above 1 year</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.KYC_OVERDUE_SAF_HIGH.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.KYC_OVERDUE_SAF_HIGH.Columns.Count && j < 10; j++)
                                        {
                                            <td>
                                                @if (j > 1 && Model.KYC_OVERDUE_SAF_HIGH.Rows[i][j] != null)
                                                {
                                                    var value = Convert.ToInt32(Model.KYC_OVERDUE_SAF_HIGH.Rows[i][j]);
                                                    if (value > 0)
                                                    {
                                                        <span class="text-danger font-weight-bold">@value</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-success">@value</span>
                                                    }
                                                }
                                                else
                                                {
                                                    @Model.KYC_OVERDUE_SAF_HIGH.Rows[i][j]
                                                }
                                            </td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No High Risk SAF KYC Overdue Data Available</h4>
                        <p class="text-muted">No high risk KYC overdue data found for standalone accounts.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- KYC Overdue SAF Low Risk -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-success">
                <h4 class="card-title">
                    <i class="material-icons">check_circle</i>
                    Periodic KYC - Branch wise Total KYC Overdue Status (SAF)
                </h4>
                <p class="card-category">
                    <span class="risk-low">Risk Level: Low</span> - Stand Alone Accounts
                </p>
            </div>
            <div class="card-body">
                @if (Model?.KYC_OVERDUE_SAF_LOW != null && Model.KYC_OVERDUE_SAF_LOW.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered compliance-table" id="kycOverdueSafLowTable">
                            <thead>
                                <tr>
                                    <th>SL</th>
                                    <th>Branch</th>
                                    <th>Total BAU Overdue</th>
                                    <th>0 to 30 Days</th>
                                    <th>31 to 60 Days</th>
                                    <th>61 to 90 Days</th>
                                    <th>91 to 120 Days (4 months)</th>
                                    <th>121 to 180 Days (4 TO 6m)</th>
                                    <th>181 days to 1 Year</th>
                                    <th>Above 1 year</th>
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.KYC_OVERDUE_SAF_LOW.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.KYC_OVERDUE_SAF_LOW.Columns.Count && j < 10; j++)
                                        {
                                            <td>
                                                @if (j > 1 && Model.KYC_OVERDUE_SAF_LOW.Rows[i][j] != null)
                                                {
                                                    var value = Convert.ToInt32(Model.KYC_OVERDUE_SAF_LOW.Rows[i][j]);
                                                    if (value > 0)
                                                    {
                                                        <span class="text-warning font-weight-bold">@value</span>
                                                    }
                                                    else
                                                    {
                                                        <span class="text-success">@value</span>
                                                    }
                                                }
                                                else
                                                {
                                                    @Model.KYC_OVERDUE_SAF_LOW.Rows[i][j]
                                                }
                                            </td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No Low Risk SAF KYC Overdue Data Available</h4>
                        <p class="text-muted">No low risk KYC overdue data found for standalone accounts.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Standalone FDR Details -->
<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-info">
                <h4 class="card-title">
                    <i class="material-icons">account_balance_wallet</i>
                    Standalone FDR Details
                </h4>
                <p class="card-category">Fixed Deposit Receipt compliance monitoring</p>
            </div>
            <div class="card-body">
                @if (Model?.StandAloneTDR != null && Model.StandAloneTDR.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered compliance-table" id="standaloneFdrTable">
                            <thead>
                                <tr>
                                    @for (int i = 0; i < Model.StandAloneTDR.Columns.Count; i++)
                                    {
                                        <th>@Model.StandAloneTDR.Columns[i].ColumnName</th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.StandAloneTDR.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.StandAloneTDR.Columns.Count; j++)
                                        {
                                            <td>@Model.StandAloneTDR.Rows[i][j]</td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No Standalone FDR Data Available</h4>
                        <p class="text-muted">No standalone FDR data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<!-- Summary Cards for SAF -->
@if (Model?.KYC_OVERDUE_SAF_HIGH != null && Model.KYC_OVERDUE_SAF_HIGH.Rows.Count > 0)
{
    <div class="row">
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-danger card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">warning</i>
                    </div>
                    <p class="card-category">SAF High Risk Overdue</p>
                    <h3 class="card-title">
                        @{
                            var safHighRiskTotal = 0;
                            for (int i = 0; i < Model.KYC_OVERDUE_SAF_HIGH.Rows.Count; i++)
                            {
                                if (Model.KYC_OVERDUE_SAF_HIGH.Rows[i][2] != null)
                                {
                                    safHighRiskTotal += Convert.ToInt32(Model.KYC_OVERDUE_SAF_HIGH.Rows[i][2]);
                                }
                            }
                        }
                        @safHighRiskTotal.ToString("N0")
                    </h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-danger">info</i>
                        SAF High Risk Cases
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-success card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">check_circle</i>
                    </div>
                    <p class="card-category">SAF Low Risk Overdue</p>
                    <h3 class="card-title">
                        @{
                            var safLowRiskTotal = 0;
                            if (Model?.KYC_OVERDUE_SAF_LOW != null)
                            {
                                for (int i = 0; i < Model.KYC_OVERDUE_SAF_LOW.Rows.Count; i++)
                                {
                                    if (Model.KYC_OVERDUE_SAF_LOW.Rows[i][2] != null)
                                    {
                                        safLowRiskTotal += Convert.ToInt32(Model.KYC_OVERDUE_SAF_LOW.Rows[i][2]);
                                    }
                                }
                            }
                        }
                        @safLowRiskTotal.ToString("N0")
                    </h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-success">info</i>
                        SAF Low Risk Cases
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-info card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">assessment</i>
                    </div>
                    <p class="card-category">Total SAF Overdue</p>
                    <h3 class="card-title">@((safHighRiskTotal + safLowRiskTotal).ToString("N0"))</h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-info">info</i>
                        Combined SAF Cases
                    </div>
                </div>
            </div>
        </div>
        <div class="col-lg-3 col-md-6">
            <div class="card card-stats">
                <div class="card-header card-header-warning card-header-icon">
                    <div class="card-icon">
                        <i class="material-icons">trending_up</i>
                    </div>
                    <p class="card-category">SAF Risk Ratio</p>
                    <h3 class="card-title">
                        @{
                            var safRiskRatio = (safHighRiskTotal + safLowRiskTotal) > 0 ? (decimal)safHighRiskTotal / (safHighRiskTotal + safLowRiskTotal) * 100 : 0;
                        }
                        @safRiskRatio.ToString("N1")%
                    </h3>
                </div>
                <div class="card-footer">
                    <div class="stats">
                        <i class="material-icons text-warning">info</i>
                        SAF High Risk %
                    </div>
                </div>
            </div>
        </div>
    </div>
}

