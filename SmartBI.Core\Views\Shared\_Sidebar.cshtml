@using System.Text.Json
@using System.Text.Json.Serialization
@using SmartBI.Data.Models
@using SmartBI.Data.ViewModels
@using Microsoft.Extensions.Logging
@inject ILogger<dynamic> Logger

<div class="sidebar" data-color="orange" data-background-color="black" data-image="/assets/img/sidebar-1.jpg">
    <!--
        Tip 1: You can change the color of the sidebar using: data-color="purple | azure | green | orange | danger"

        Tip 2: you can also add an image using data-image tag
    -->
    <div class="logo">
        <a href="@Url.Action("Index", "Home")" class="simple-text logo-mini">
            SB
        </a>
        <a href="@Url.Action("Index", "Home")" class="simple-text logo-normal">
            SmartBI
        </a>
    </div>
    <div class="sidebar-wrapper">
        @{
            // Get the current user's login credentials from session
            var loginCredentials = !string.IsNullOrEmpty(Context.Session.GetString("LoginCredentials")) ?
                JsonSerializer.Deserialize<LoginModels>(Context.Session.GetString("LoginCredentials")) : null;

            string userName = Context.Session.GetString("UserName") ?? "User";
        }
        <!-- Official Material Dashboard Pro User Section -->
        <div class="user">
            <div class="photo">
                <img src="~/assets/img/default-avatar.png" alt="@userName" />
            </div>
            <div class="user-info">
                <a data-toggle="collapse" href="#ProfileNav" class="username" aria-expanded="false">
                    <span>
                        @userName
                        <b class="caret"></b>
                    </span>
                </a>
                <div class="collapse" id="ProfileNav">
                    <ul class="nav">
                        <li class="nav-item">
                            <a class="nav-link" href="/UserProfile/Index">
                                <span class="sidebar-mini"> MP </span>
                                <span class="sidebar-normal"> My Profile </span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <a class="nav-link" href="/UserProfile/Index?tab=password">
                                <span class="sidebar-mini"> S </span>
                                <span class="sidebar-normal"> Settings </span>
                            </a>
                        </li>
                        <li class="nav-item">
                            <form id="logoutFormSidebar" method="post" action="@Url.Action("Logout", "Account")" style="margin:0;padding:0;">
                                @Html.AntiForgeryToken()
                                <a class="nav-link" href="javascript:;" onclick="document.getElementById('logoutFormSidebar').submit();">
                                    <span class="sidebar-mini"> L </span>
                                    <span class="sidebar-normal"> Logout </span>
                                </a>
                            </form>
                        </li>
                    </ul>
                </div>
            </div>
        </div>
        <ul class="nav">
            @{
                // Get the user's role ID (which may contain multiple comma-separated values)
                string roleId = loginCredentials?.UserRoleId ?? "0";

                // Get menu items from session - using MenuModels (flat structure) like legacy system
                var menuMaster = new List<MenuModels>();
                string menuItemsJson = Context.Session.GetString("MenuItems");

                Logger.LogInformation($"_Sidebar: Getting menu items from session. JSON length: {(menuItemsJson?.Length ?? 0)}");

                if (!string.IsNullOrEmpty(menuItemsJson))
                {
                    try
                    {
                        var jsonOptions = new JsonSerializerOptions
                        {
                            PropertyNameCaseInsensitive = true,
                            ReferenceHandler = ReferenceHandler.Preserve
                        };

                        // Deserialize as MenuModels (flat structure) like legacy _LeftSidebar.cshtml
                        menuMaster = JsonSerializer.Deserialize<List<MenuModels>>(menuItemsJson, jsonOptions) ?? new List<MenuModels>();
                        menuMaster = menuMaster.OrderBy(m=>m.MenuOrder).ToList();
                        Logger.LogInformation($"_Sidebar: Successfully deserialized {menuMaster.Count} menu items");
                    }
                    catch (Exception ex)
                    {
                        // If deserialization fails, use an empty list
                        Logger.LogError(ex, "_Sidebar: Error deserializing menu items from session");
                        menuMaster = new List<MenuModels>();
                    }
                }
                else
                {
                    Logger.LogWarning("_Sidebar: No menu items found in session");
                }

                // Apply legacy logic: Order by MenuOrder and Group by MainMenuId like legacy _LeftSidebar.cshtml
                var groupByMenu = menuMaster.GroupBy(x => x.MainMenuId).ToList();
                
                // Get current controller and action
                var currentController = ViewContext.RouteData.Values["controller"]?.ToString();
                var currentAction = ViewContext.RouteData.Values["action"]?.ToString();

                // Find active main menu based on current controller and action
                string activeMainMenuId = null;
                foreach (var menuGroup in groupByMenu)
                {
                    foreach (var menuItem in menuGroup)
                    {
                        if (string.Equals(currentController, menuItem.ControllerName, StringComparison.OrdinalIgnoreCase) &&
                            string.Equals(currentAction, menuItem.ActionName, StringComparison.OrdinalIgnoreCase))
                        {
                            activeMainMenuId = menuItem.MainMenuId;
                            break;
                        }
                    }
                    if (activeMainMenuId != null) break;
                }
            }

            <!-- Dynamic menu items from database -->
            @foreach (var menuList in groupByMenu)
            {
                var firstMenuItem = menuList.FirstOrDefault();
                if (firstMenuItem != null)
                {
                    // Determine icon based on menu name
                    string iconName = "widgets"; // Default icon
                    
                    // Map menu names to appropriate icons from reference image
                    string menuNameLower = (firstMenuItem.MainMenuName ?? firstMenuItem.SubMenuName ?? "").ToLower();
                    if (menuNameLower.Contains("dashboard")) iconName = "dashboard";  
                    else if (menuNameLower.Contains("page")) iconName = "image";  
                    else if (menuNameLower.Contains("component")) iconName = "apps";  
                    else if (menuNameLower.Contains("form")) iconName = "content_paste";  
                    else if (menuNameLower.Contains("table")) iconName = "grid_on";  
                    else if (menuNameLower.Contains("map")) iconName = "place";  
                    else if (menuNameLower.Contains("widget")) iconName = "widgets";  
                    else if (menuNameLower.Contains("chart")) iconName = "timeline";  
                    else if (menuNameLower.Contains("calendar")) iconName = "date_range";
                    else if (menuNameLower.Contains("setting")) iconName = "settings";
                    else if (menuNameLower.Contains("report")) iconName = "assessment";
                    else if (menuNameLower.Contains("analy")) iconName = "analytics";
                    else if (menuNameLower.Contains("admin")) iconName = "admin_panel_settings";
                    
                    bool isSingleMenuItem = menuList.Count() == 0;
                    bool isActiveMenu = firstMenuItem.MainMenuId == activeMainMenuId;
                    string activeClass = isActiveMenu ? "active" : "";
                    string menuId = $"menu{firstMenuItem.MainMenuId}";

                    if (isSingleMenuItem)
                    {
                        string menuUrl = $"/{firstMenuItem.ControllerName}/{firstMenuItem.ActionName}";

                        <li class="nav-item @activeClass">
                            <a class="nav-link" href="@menuUrl">
                                <i class="material-icons">@iconName</i>
                                <p>@(firstMenuItem.SubMenuName ?? firstMenuItem.MainMenuName)</p>
                            </a>
                        </li>
                    }
                    else
                    {
                        <li class="nav-item @activeClass">
                            <a href="#@menuId" class="nav-link" data-toggle="collapse" aria-expanded="false" aria-controls="@menuId">
                                <i class="material-icons">@iconName</i>
                                <p>
                                    @firstMenuItem.MainMenuName
                                    <b class="caret"></b>
                                </p>
                            </a>
                            <div class="collapse" id="@menuId" aria-expanded="false">
                                <ul class="nav">
                                    @foreach (var subMenuItem in menuList.OrderBy(m => m.MenuOrder))
                                    {
                                        string subMenuUrl = $"/{subMenuItem.ControllerName}/{subMenuItem.ActionName}";

                                        bool isActive = string.Equals(currentController, subMenuItem.ControllerName, StringComparison.OrdinalIgnoreCase) &&
                                                     string.Equals(currentAction, subMenuItem.ActionName, StringComparison.OrdinalIgnoreCase);
                                        string subActiveClass = isActive ? "active" : "";

                                        <li class="nav-item @subActiveClass">
                                            <a class="nav-link" href="@subMenuUrl">
                                                <span class="sidebar-mini">@(subMenuItem.SubMenuName != null && subMenuItem.SubMenuName.Length > 0 ? subMenuItem.SubMenuName[0].ToString() : "")</span>
                                                <span class="sidebar-normal">@subMenuItem.SubMenuName</span>
                                            </a>
                                        </li>
                                    }
                                </ul>
                            </div>
                        </li>
                    }
                }
            }
            

        </ul>
    </div>
</div>
