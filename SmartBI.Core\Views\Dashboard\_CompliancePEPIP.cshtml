@model SmartBI.Data.ViewModels.Dashboard.ComplianceDashboardViewModel

<div class="row">
    <div class="col-12">
        <div class="card">
            <div class="card-header card-header-warning">
                <h4 class="card-title">
                    <i class="material-icons">person_pin</i>
                    PEP/IP Compliance Monitoring
                </h4>
                <p class="card-category">Politically Exposed Persons & Important Persons</p>
            </div>
            <div class="card-body">
                @if (Model?.PEP_IP != null && Model.PEP_IP.Rows.Count > 0)
                {
                    <div class="table-responsive">
                        <table class="table table-striped table-bordered compliance-table" id="pepIpTable">
                            <thead>
                                <tr>
                                    @for (int i = 0; i < Model.PEP_IP.Columns.Count; i++)
                                    {
                                        <th>@Model.PEP_IP.Columns[i].ColumnName</th>
                                    }
                                </tr>
                            </thead>
                            <tbody>
                                @for (int i = 0; i < Model.PEP_IP.Rows.Count; i++)
                                {
                                    <tr>
                                        @for (int j = 0; j < Model.PEP_IP.Columns.Count; j++)
                                        {
                                            <td>@Model.PEP_IP.Rows[i][j]</td>
                                        }
                                    </tr>
                                }
                            </tbody>
                        </table>
                    </div>
                }
                else
                {
                    <div class="text-center py-5">
                        <i class="material-icons" style="font-size: 48px; color: #999;">info_outline</i>
                        <h4 class="mt-3 text-muted">No PEP/IP Data Available</h4>
                        <p class="text-muted">No PEP/IP compliance data found for the current user.</p>
                    </div>
                }
            </div>
        </div>
    </div>
</div>

<script>
    $(document).ready(function() {
        $('#pepIpTable').DataTable({
            "paging": true,
            "lengthChange": false,
            "searching": true,
            "ordering": true,
            "info": true,
            "autoWidth": false,
            "responsive": true,
            "pageLength": 10
        });
    });
</script>
